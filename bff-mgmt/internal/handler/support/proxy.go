package support

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/support"
	mclient "github.com/asim/go-micro/v3/client"
	"github.com/hashicorp/go-uuid"
	"golang.org/x/net/html"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

func FetchHtmlTitle(url string) string {
	ctx, cancel := context.WithTimeout(context.Background(), 4*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return url
	}

	uid, _ := uuid.GenerateUUID()
	userAgent := fmt.Sprintf("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/********* tanlive-%s", uid)
	req.Header.Set("User-Agent", userAgent)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return url
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return url
	}
	// reader, _ := charset.NewReader(resp.Body, "utf-8")
	//
	// body, err := io.ReadAll(reader)
	// if err != nil {
	//	return url
	// }
	// // 解析HTML并提取<title>
	// doc, err := html.Parse(strings.NewReader(string(body)))

	doc, err := html.Parse(resp.Body)
	if err != nil {
		return url
	}

	var title string
	var ogTitle string
	var twitterTitle string

	var f func(*html.Node)
	f = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "title" {
			if n.FirstChild != nil {
				title = n.FirstChild.Data
			}
		}
		// 查找<meta property="og:title" content="..."> 或 <meta name="twitter:title" content="...">
		if n.Type == html.ElementNode && n.Data == "meta" {
			var property string
			var content string
			for _, attr := range n.Attr {
				if attr.Key == "property" && (attr.Val == "og:title") {
					property = attr.Val
				}
				if attr.Key == "name" && (attr.Val == "twitter:title") {
					property = attr.Val
				}
				if attr.Key == "content" {
					content = attr.Val
				}
			}
			// 如果找到了og:title 或 twitter:title
			if property == "og:title" {
				ogTitle = content
			}
			if property == "twitter:title" {
				twitterTitle = content
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			f(c)
		}
	}
	f(doc)

	// 优先返回<title> > og:title > twitter:title >
	var finalTitle string
	if title != "" {
		finalTitle = title
	} else if ogTitle != "" {
		finalTitle = ogTitle
	} else if twitterTitle != "" {
		finalTitle = twitterTitle
	}

	if finalTitle == "" {
		return url
	}

	if !utf8.ValidString(finalTitle) {
		// try gb18030
		reader := transform.NewReader(strings.NewReader(finalTitle), simplifiedchinese.GB18030.NewDecoder())
		gbStr, err := io.ReadAll(reader)
		if err != nil {
			return url
		}
		finalTitle = string(gbStr)
	}
	if !utf8.ValidString(finalTitle) {
		return url
	}
	if strings.Contains(finalTitle, "404") || strings.Contains(finalTitle, "loading") || strings.Contains(finalTitle, "页面找不到") {
		return url
	}
	return finalTitle
}

// Proxy 代理网站
// 获取失败也不会返回错误
func (h *Support) Proxy(ctx context.Context, req *bffpb.ReqProxy, rsp *bffpb.RspProxy) error {
	if len(req.Urls) == 0 {
		return nil
	}
	res, err := client.AiClient.FetchHtmlTitles(ctx, &aipb.ReqFetchHtmlTitles{Urls: req.Urls}, mclient.WithRequestTimeout(60*time.Second))
	if err != nil {
		return err
	}
	for url, title := range res.UrlWithTitles {
		rsp.Contents = append(rsp.Contents, &bffpb.RspProxy_Content{Url: url, Title: title})
	}

	return nil
}
