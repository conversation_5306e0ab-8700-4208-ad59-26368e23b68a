package ai

import (
	"context"

	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
)

// ImportQAs 导入qa
func (a *Ai) ImportQAs(ctx context.Context, req *ai.ReqImportQAs, rsp *ai.RspImportQAs) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)

	contributor := &aipb.Contributor{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id}
	operator := &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id}
	var scopedAssistant []uint64

	importLogic := ailogic.NewQAImportLogic(context.WithoutCancel(ctx), contributor, operator, scopedAssistant, req)
	err := importLogic.Do()
	if err != nil {
		return err
	}

	return nil
}

// DescribeExportTasks 查询导入/导出任务列表
func (a *Ai) DescribeExportTasks(ctx context.Context, req *ai.ReqDescribeExportTasks, rsp *ai.RspDescribeExportTasks) error {
	res, err := client.AiClient.DescribeExportTasks(ctx, &aipb.ReqDescribeExportTasks{
		UserId:        ailogic.TanliveOpTaskTenant,
		Type:          req.Type,
		OperationType: req.OperationType,
	})
	if err != nil {
		return err
	}
	rsp.Tasks = res.Tasks
	return nil
}
