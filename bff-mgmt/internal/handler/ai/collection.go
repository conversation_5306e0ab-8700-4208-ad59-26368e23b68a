package ai

import (
	"context"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/mgmt"
	mclient "github.com/asim/go-micro/v3/client"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// CreateTextFiles collection管理里，创建文本与文件
func (a *Ai) CreateTextFiles(ctx context.Context, req *ai.ReqCreateTextFiles, rsp *ai.RspCreateTextFiles) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqCreateTextFileInBulk{Items: make([]*aipb.TextFile, 0, len(req.Items))}
	for _, v := range req.Items {
		item := &aipb.TextFile{
			Name:        v.FileName,
			Text:        v.Text,
			Url:         v.Url,
			State:       v.State,
			Contributor: v.Contributor,
			CreateBy:    &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
			UpdateBy:    &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
			UgcType:     v.UgcType,
			UgcId:       v.UgcId,
			ParsedUrl:   v.ParsedUrl,
			Type:        v.Type,
			States: func() []*aipb.DocAssistantState {
				r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
				for _, vv := range v.AssistantId {
					r = append(r, &aipb.DocAssistantState{State: v.State, AssistantId: vv})
				}
				return r
			}(),
			ShowContributor: v.ShowContributor,
			Reference:       v.Reference,
			ParseMode:       v.ParseMode,
			DataSource:      v.DataSource,
		}
		if len(item.Contributor) == 0 {
			// 贡献者默认加上当前用户
			item.Contributor = append(item.Contributor, &aipb.Contributor{
				Type: base.IdentityType_IDENTITY_TYPE_MGMT,
				Id:   user.Id,
			})
		}

		pbReq.Items = append(pbReq.Items, item)
	}
	pbRsp, err := client.AiClient.CreateTextFileInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("create ai collection doc text files failed: %v", err)
		return err
	}
	rsp.Id = pbRsp.Id
	return nil
}

// ListTextFiles 获取文本/文件列表
func (a *Ai) ListTextFiles(ctx context.Context, req *ai.ReqListTextFiles, rsp *ai.RspListTextFiles) error {
	pbReq, err := ailogic.CreateAiReqListTextFile(ctx, req)
	if err != nil {
		return err
	}
	pbRsp, err := client.AiClient.ListTextFile(ctx, pbReq, mclient.WithRequestTimeout(time.Second*30))
	if err != nil {
		log.WithContext(ctx).Errorf("list ai collection doc text files failed: %v", err)
		return err
	}

	var operators []*aipb.Operator
	for _, v := range pbRsp.Items {
		if v.Doc.CreateBy != nil {
			operators = append(operators, v.Doc.CreateBy)
		}
		if v.Doc.UpdateBy != nil {
			operators = append(operators, v.Doc.UpdateBy)
		}
		for _, c := range v.Copies {
			if c.CreateBy != nil {
				operators = append(operators, c.CreateBy)
			}
			if c.UpdateBy != nil {
				operators = append(operators, c.UpdateBy)
			}
		}
	}
	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range pbRsp.Items {
		contributors = append(contributors, v.Doc.Contributor...)
		for _, c := range v.Copies {
			contributors = append(contributors, c.Contributor...)
		}
	}
	_, err = ailogic.GetAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}

	rsp.Items = ailogic.FullTextFilesToCollectionTextFiles(pbRsp.Items, operatorsToShow)
	// 非系统数据分享至的状态为空
	if !req.IsSystem || req.DataSource != aipb.DocDataSource_DOC_DATA_SOURCE_UGC {
		for _, v := range rsp.Items {
			v.SharedStates = nil
		}
	}
	rsp.TotalCount = pbRsp.Total
	rsp.FailParseCount = pbRsp.FailParseCount
	return nil
}

// GetTextFile id获取文件/文本详情
func (a *Ai) GetTextFile(ctx context.Context, req *ai.ReqGetTextFile, rsp *ai.RspGetTextFile) error {
	pbReq := &aipb.ReqListTextFile{
		Id: []uint64{req.Id},
		// IgnoreIsSystem: true,
		IgnoreIsCopy: true,
	}
	pbRsp, err := client.AiClient.ListTextFile(ctx, pbReq)
	if err != nil {
		return err
	}
	if len(pbRsp.Items) == 0 {
		return nil
	}

	var operators []*aipb.Operator
	for _, v := range pbRsp.Items {
		if v.Doc.CreateBy != nil {
			operators = append(operators, v.Doc.CreateBy)
		}
		if v.Doc.UpdateBy != nil {
			operators = append(operators, v.Doc.UpdateBy)
		}
		for _, c := range v.Copies {
			if c.CreateBy != nil {
				operators = append(operators, c.CreateBy)
			}
			if c.UpdateBy != nil {
				operators = append(operators, c.UpdateBy)
			}
		}
	}
	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}

	rsp.Item = ailogic.FullTextFileToCollectionTextFile(pbRsp.Items[0], operatorsToShow)
	return nil
}

// 重写fieldMask
func rewriteUpdateTextFileMask(mask *fieldmaskpb.FieldMask) *fieldmaskpb.FieldMask {
	for i, v := range mask.GetPaths() {
		if v == "file_name" {
			mask.Paths[i] = "name" // 重写mask路径
		}
	}
	if len(mask.GetPaths()) != 0 {
		mask.Paths = append(mask.Paths, "update_by", "update_type")
	}
	return mask
}

// UpdateTextFiles 更新文本/文件列表
func (a *Ai) UpdateTextFiles(ctx context.Context, req *ai.ReqUpdateTextFiles, _ *emptypb.Empty) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqUpdateTextFileInBulk{Items: make([]*aipb.ReqUpdateTextFile, 0, len(req.Items))}
	for _, v := range req.Items {
		for _, vv := range v.States {
			// 如果状态不是启用/禁用，设置为禁用
			if vv.State != aipb.DocState_DOC_STATE_ENABLED && vv.State != aipb.DocState_DOC_STATE_DISABLED {
				vv.State = aipb.DocState_DOC_STATE_DISABLED
			}
		}
		pbReq.Items = append(pbReq.Items, &aipb.ReqUpdateTextFile{
			Id:              v.Id,
			Name:            v.FileName,
			Text:            v.Text,
			Url:             v.Url,
			ParsedUrl:       v.ParsedUrl,
			States:          v.States,
			Contributor:     v.Contributor,
			UpdateBy:        &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
			UgcType:         v.UgcType,
			UgcId:           v.UgcId,
			Mask:            rewriteUpdateTextFileMask(v.Mask),
			ShowContributor: v.ShowContributor,
			Reference:       v.Reference,
			DownloadAsRef:   v.DownloadAsRef,
		})
	}

	_, err := client.AiClient.UpdateTextFileInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("update ai collection doc text files failed: %v", err)
		return err
	}
	return nil
}

// ListQA 获取QA列表
func (a *Ai) ListQA(ctx context.Context, req *ai.ReqListQA, rsp *ai.RspListQA) error {
	pbReq, err := ailogic.CreateAiListQA(ctx, req)
	if err != nil {
		return err
	}
	pbRsp, err := client.AiClient.ListQA(ctx, pbReq, mclient.WithRequestTimeout(time.Second*30))
	if err != nil {
		log.WithContext(ctx).Errorf("list ai collection doc qa failed: %v", err)
		return err
	}
	var operators []*aipb.Operator
	for _, v := range pbRsp.Items {
		if v.CreateBy != nil {
			operators = append(operators, v.CreateBy)
		}
		if v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}
	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range pbRsp.Items {
		contributors = append(contributors, v.Contributor...)
	}
	_, err = ailogic.GetAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}
	for _, v := range pbRsp.Items {
		rsp.Items = append(rsp.Items, &ai.CollectionQA{
			Id:               v.Id,
			Question:         v.Question,
			Answer:           v.Answer,
			Contributor:      v.Contributor,
			Reference:        v.Reference,
			HitCount:         v.HitCount,
			CreateBy:         operatorsToShow[v.CreateBy],
			UpdateBy:         operatorsToShow[v.UpdateBy],
			UpdateDate:       v.UpdateDate,
			CreateDate:       v.CreateDate,
			VersionLag:       v.VersionLag,
			ShowContributor:  v.ShowContributor,
			States:           ailogic.SpiltDocAssistantsByShared(v.States),
			Assistants:       v.Assistants,
			State:            v.State,
			Labels:           v.Labels,
			MatchPatterns:    v.MatchPatterns,
			QuestionOversize: v.QuestionOversize,
			HasRepeated:      v.HasRepeated,
		})
	}
	rsp.TotalCount = pbRsp.Total
	return nil
}

// CreateQAs 创建QA
func (a *Ai) CreateQAs(ctx context.Context, req *ai.ReqCreateQAs, _ *emptypb.Empty) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqCreateQAInBulk{Items: make([]*aipb.QA, 0, len(req.Items))}
	for _, v := range req.Items {
		item := &aipb.QA{
			Question:    v.Question,
			Answer:      v.Answer,
			Reference:   v.Reference,
			State:       v.State,
			Contributor: v.Contributor,
			CreateBy:    &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
			UpdateBy:    &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
			States: func() []*aipb.DocAssistantState {
				r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
				for _, vv := range v.AssistantId {
					r = append(r, &aipb.DocAssistantState{State: v.State, AssistantId: vv})
				}
				return r
			}(),
			ShowContributor: v.ShowContributor,
			MatchPatterns:   v.MatchPatterns,
		}
		if len(item.Contributor) == 0 {
			// 贡献者默认加上当前用户
			item.Contributor = append(item.Contributor, &aipb.Contributor{
				Type: base.IdentityType_IDENTITY_TYPE_MGMT,
				Id:   user.Id,
			})
		}
		pbReq.Items = append(pbReq.Items, item)
	}
	_, err := client.AiClient.CreateQAInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("create ai collection doc qa failed: %v", err)
		return err
	}
	return nil
}

// UpdateQAs 更新QA
func (a *Ai) UpdateQAs(ctx context.Context, req *ai.ReqUpdateQAs, _ *emptypb.Empty) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqUpdateQAInBulk{Items: make([]*aipb.ReqUpdateQA, 0, len(req.Items))}
	for _, v := range req.Items {
		if len(v.GetMask().GetPaths()) != 0 {
			v.Mask.Paths = append(v.Mask.Paths, "update_by", "update_type")
		}
		pbReq.Items = append(pbReq.Items, &aipb.ReqUpdateQA{
			Question:        v.Question,
			Answer:          v.Answer,
			Reference:       v.Reference,
			States:          v.States,
			Id:              v.Id,
			Operator:        &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
			Contributor:     v.Contributor,
			Mask:            v.Mask,
			ShowContributor: v.ShowContributor,
			MatchPatterns:   v.MatchPatterns,
		})
	}
	_, err := client.AiClient.UpdateQAInBulk(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("update ai collection doc qa failed: %v", err)
		return err
	}
	return nil
}

// DeleteQAs 删除QA对
func (a *Ai) DeleteQAs(ctx context.Context, req *ai.ReqDeleteDocs, rsp *ai.RspDeleteDocs) error {
	if req.QueryId == 0 && len(req.Id) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqDeleteDocInBulk{
		Id:                  req.Id,
		Operator:            &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
		QueryId:             req.QueryId,
		IsContributorDelete: true,
	}
	rpcRsp, err := client.AiClient.DeleteDocInBulk(ctx, pbReq)
	if err != nil {
		return err
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}
	return nil
}

// SearchCollection 搜索collection
func (a *Ai) SearchCollection(ctx context.Context, req *ai.ReqSearchCollection, rsp *ai.RspSearchCollection) error {
	pbReq := &aipb.ReqSearchCollectionOneShot{
		Search:            req.Search,
		AssistantId:       req.AssistantId,
		DocType:           req.DocType,
		TimeRecord:        true,
		Threshold:         req.Threshold,
		IsSearchChat:      true,
		TextRecallTopN:    req.TextRecallTopN,
		TopN:              req.TopN,
		TextRecallQuery:   req.TextRecallQuery,
		TextRecallPattern: req.TextRecallPattern,
		TextRecallSlop:    req.TextRecallSlop,
		CleanChunks:       req.CleanChunks,
		Temperature:       req.Temperature,
	}

	//pbReq.From = req.Offset
	//if req.TopN <= req.Offset {
	//	pbReq.TopN = 0
	//}
	//if req.TextRecallTopN <= req.Offset {
	//	pbReq.TextRecallTopN = 0
	//}
	//
	//if req.TopN > req.Offset && req.TopN < (req.Offset+req.Limit) {
	//	pbReq.TopN = req.TopN
	//}
	//if req.TextRecallTopN > req.Offset && req.TextRecallTopN < (req.Offset+req.Limit) {
	//	pbReq.TextRecallTopN = req.TextRecallTopN
	//}
	//if req.TopN >= (req.Offset + req.Limit) {
	//	pbReq.TopN = req.Limit
	//}
	//if req.TextRecallTopN >= (req.Offset + req.Limit) {
	//	pbReq.TextRecallTopN = req.Limit
	//}

	var searchItems, searchTextItems []*aipb.SearchCollectionItem
	var startTime, endTime *timestamppb.Timestamp
	// var totalCount uint32

	matchRsp, matchPattern, err := ailogic.MatchQACollection(ctx, pbReq)
	if err != nil {
		return err
	}
	if matchRsp != nil && len(matchRsp.Items) > 0 {
		searchTextItems = matchRsp.Items
		startTime = matchRsp.StartTime
		endTime = matchRsp.EndTime
	} else {
		searchRsp, err := client.AiClient.SearchCollectionOneShot(ctx, pbReq, mclient.WithRequestTimeout(60*time.Second))
		if err != nil {
			log.WithContext(ctx).Errorf("search ai collection doc qa failed: %v", err)
			return err
		}
		searchItems = searchRsp.SearchItems
		searchTextItems = searchRsp.SearchTextItems
		startTime = searchRsp.StartTime
		endTime = searchRsp.EndTime
		// totalCount = searchRsp.TotalCount
	}

	var operators []*aipb.Operator
	for _, v := range searchItems {
		if v != nil && v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}
	for _, v := range searchTextItems {
		if v != nil && v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}

	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range searchItems {
		contributors = append(contributors, v.Contributor...)
	}
	for _, v := range searchTextItems {
		contributors = append(contributors, v.Contributor...)
	}
	_, err = ailogic.GetAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}

	//for _, v := range searchItems {
	//	rsp.Item = append(rsp.Item, &ai.SearchCollectionItem{
	//		Text:        v.Text,
	//		Question:    v.Question,
	//		Score:       v.Score,
	//		FileName:    v.RefName,
	//		Url:         v.RefUrl,
	//		Contributor: v.Contributor,
	//		UpdateBy:    operatorsToShow[v.UpdateBy],
	//		Id:          v.Id,
	//		Type:        v.Type,
	//		IsRelated:   v.IsRelated,
	//		DocType:     v.DocType,
	//	})
	//}
	//for _, v := range searchTextItems {
	//	rsp.Item = append(rsp.Item, &ai.SearchCollectionItem{
	//		Text:        v.Text,
	//		Question:    v.Question,
	//		Score:       v.Score,
	//		FileName:    v.RefName,
	//		Url:         v.RefUrl,
	//		Contributor: v.Contributor,
	//		UpdateBy:    operatorsToShow[v.UpdateBy],
	//		Id:          v.Id,
	//		Type:        v.Type,
	//		IsRelated:   v.IsRelated,
	//		DocType:     v.DocType,
	//	})
	//}

	combineItems := append(searchItems, searchTextItems...)
	var target []*aipb.SearchCollectionItem
	// 处理分页
	if len(combineItems) > 0 {
		start := int(req.Offset)
		if start >= 0 && start < len(combineItems) {
			end := start + int(req.Limit)
			if end > len(combineItems) {
				end = len(combineItems)
			}
			target = combineItems[start:end]
		}
	}

	for _, v := range target {
		rsp.Item = append(rsp.Item, &ai.SearchCollectionItem{
			Text:        v.Text,
			Question:    v.Question,
			Score:       v.Score,
			FileName:    v.RefName,
			Url:         v.RefUrl,
			Contributor: v.Contributor,
			UpdateBy:    operatorsToShow[v.UpdateBy],
			Id:          v.Id,
			Type:        v.Type,
			IsRelated:   v.IsRelated,
			DocType:     v.DocType,
		})
	}

	rsp.Start = startTime
	rsp.End = endTime
	rsp.TotalCount = uint32(len(combineItems))
	rsp.MatchPattern = matchPattern

	return nil
}

// ValidateQAs 校验待创建的QA对
func (a *Ai) ValidateQAs(ctx context.Context, req *ai.ReqValidateQAs, rsp *ai.RspValidateQAs) error {
	pbReq := &aipb.ReqValidateQAInBulk{}
	for _, v := range req.Items {
		pbReq.Items = append(pbReq.Items, &aipb.QA{
			Question:    v.Question,
			Answer:      v.Answer,
			Reference:   v.Reference,
			State:       v.State,
			Contributor: v.Contributor,
			Assistants: func() []*aipb.Assistant {
				r := make([]*aipb.Assistant, 0, len(v.AssistantId))
				for _, vv := range v.AssistantId {
					r = append(r, &aipb.Assistant{Id: vv})
				}
				return r
			}(),
		})
	}
	pbRsp, err := client.AiClient.ValidateQAInBulk(ctx, pbReq, mclient.WithRequestTimeout(15*time.Second))
	if err != nil {
		log.WithContext(ctx).Errorf("validate ai collection qa failed: %v", err)
		return err
	}
	for _, v := range pbRsp.Errors {
		msg := ""
		if v.Error == errorspb.AiError_AiCollectionQuestionExisted {
			msg = "问题已经存在"
		}
		rsp.Errors = append(rsp.Errors, &ai.RspValidateQAs_Err{
			Code:    v.Error,
			Message: msg,
			Id:      v.Id,
		})
	}
	return nil
}

// ListCollection 获取collection用户端列表
func (a *Ai) ListCollection(ctx context.Context, req *emptypb.Empty, rsp *ai.RspListCollection) error {
	pbRsp, err := client.AiClient.ListCollection(ctx, &aipb.ReqListCollection{})
	if err != nil {
		return err
	}
	rsp.Collections = pbRsp.Collections
	return nil
}

// ListContributor 获取贡献者列表
func (a *Ai) ListContributor(ctx context.Context, req *ai.ReqListContributor, rsp *ai.RspListContributor) error {
	pbRsp, err := client.AiClient.ListContributor(ctx, &aipb.ReqListContributor{
		Type:       req.Type,
		DataSource: req.DataSource,
	})
	if err != nil {
		return err
	}
	info, err := ailogic.GetAiDoctContributorShowInfo(ctx, pbRsp.Contributors...)
	if err != nil {
		return err
	}
	for _, v := range info {
		if len(req.Search) != 0 && !strings.Contains(v.Text, req.Search) {
			continue
		}
		rsp.Contributors = append(rsp.Contributors, v)
	}
	return nil
}

// ListOperator 获取更新人列表
func (a *Ai) ListOperator(ctx context.Context, req *ai.ReqListOperator, rsp *ai.RspListOperator) error {
	rpcF := client.AiClient.ListUpdateBy
	if req.Creator {
		rpcF = client.AiClient.ListCreateBy
	}
	pbRsp, err := rpcF(ctx, &aipb.ReqListUpdateBy{
		Type:       req.Type,
		DataSource: req.DataSource,
	})
	if err != nil {
		return err
	}
	info, err := ailogic.GetAiDocOperatorsShowInfo(ctx, pbRsp.Operators...)
	if err != nil {
		return err
	}
	for _, v := range info {
		if len(req.Search) != 0 && !strings.Contains(v.Username, req.Search) && !strings.Contains(v.TeamName, req.Search) {
			continue
		}
		rsp.Operators = append(rsp.Operators, v)
	}
	return nil
}

// ListCollectionFileName 获取文件名称列表
func (a *Ai) ListCollectionFileName(ctx context.Context, req *ai.ReqListCollectionFileName, rsp *ai.RspListCollectionFileName) error {
	pbReq := &aipb.ReqListTextFile{
		Page: &base.Paginator{
			Offset: req.Offset,
			Limit:  req.Limit,
		},
		Search: &aipb.ReqListTextFile_Search{
			FileName:     req.Search,
			FullFileName: req.FullSearch,
		},
		Mask:       &fieldmaskpb.FieldMask{Paths: []string{"id", "file_name", "ref"}},
		DocType:    []aipb.DocType{aipb.DocType_DOCTYPE_FILE},
		DataSource: req.DataSource,
	}
	ailogic.ListTextFileHook(ctx, pbReq)
	pbRsp, err := client.AiClient.ListTextFile(ctx, pbReq)
	if err != nil {
		return err
	}
	for _, v := range pbRsp.Items {
		rsp.Items = append(rsp.Items, &ai.RspListCollectionFileName_Item{
			Url:  v.Doc.Url,
			Name: v.Doc.Name,
			Id:   v.Doc.Id,
		})
	}
	rsp.TotalCount = pbRsp.Total
	return nil
}

// CloneDoc 复制doc
func (a *Ai) CloneDoc(ctx context.Context, req *ai.ReqCloneDoc, rsp *ai.RspCloneDoc) error {
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqCloneDocInBulk{
		Operator: &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
		// QueryId:  req.QueryId,// 2.11 克隆暂时不支持全选
	}
	for _, v := range req.Id {
		pbReq.Id = append(pbReq.Id, v)
	}
	pbRsp, err := client.AiClient.CloneDocInBulk(ctx, pbReq)
	if err != nil {
		return err
	}
	for _, v := range pbRsp.Id {
		rsp.Id = append(rsp.Id, v)
	}
	return nil
}

// OnOffDocs 启用/禁用
func (a *Ai) OnOffDocs(ctx context.Context, req *ai.ReqOnOffDocs, rsp *ai.RspOnOffDocs) error {
	if req.QueryId == 0 && len(req.Id) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	pbReq := &aipb.ReqOnOffDocInBulk{
		Operator: &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
		State:    req.State,
		QueryId:  req.QueryId,
	}
	for _, v := range req.Id {
		pbReq.Id = append(pbReq.Id, v)
	}
	data, err := client.AiClient.OnOffDocInBulk(ctx, pbReq)
	if err != nil {
		return err
	}

	for _, v := range data.RepeatCollections {
		rsp.RepeatCollections = append(rsp.RepeatCollections, &ai.RspOnOffDocs_RepeatCollection{Id: v.Id, FileName: v.FileName})
	}
	for _, v := range data.PreRepeatCollections {
		rsp.PreRepeatCollections = append(rsp.PreRepeatCollections, &ai.RspOnOffDocs_RepeatCollection{Id: v.Id, FileName: v.FileName})
	}
	for _, v := range data.ExceedQaContainsMatchLimit {
		rsp.QaNumExceed = append(rsp.QaNumExceed, &ai.RspOnOffDocs_QaContainsMatchCount{
			AssistantId: v.AssistantId,
			Cnt:         v.QaCnt,
		})
	}
	rsp.Async = data.Async

	return nil
}

// ListDocByRef 根据文档ref_id查询文档
func (a *Ai) ListDocByRef(ctx context.Context, req *ai.ReqListDocByRef, rsp *ai.RspListDocByRef) error {
	if len(req.Ref) == 0 {
		return nil
	}
	pbReq := &aipb.ReqListDocByRef{
		Ref: req.Ref,
	}
	pbRsp, err := client.AiClient.ListDocByRef(ctx, pbReq)
	if err != nil {
		return err
	}
	var operators []*aipb.Operator
	for _, v := range pbRsp.Docs {
		if v.UpdateBy != nil {
			operators = append(operators, v.UpdateBy)
		}
	}
	operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)
	if err != nil {
		return err
	}
	var contributors []*aipb.Contributor
	for _, v := range pbRsp.Docs {
		contributors = append(contributors, v.Contributor...)
	}
	_, err = ailogic.GetAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return err
	}

	for _, v := range pbRsp.Docs {
		rsp.Docs = append(rsp.Docs, &ai.RspListDocByRef_Doc{
			Text:        v.Text,
			Question:    v.Question,
			FileName:    v.FileName,
			Url:         v.Url,
			Contributor: v.Contributor,
			UpdateBy:    operatorsToShow[v.UpdateBy],
			Ref:         v.Ref,
		})
	}
	return nil
}

// BatchUpdateDocAttr 批量更新doc
func (a *Ai) BatchUpdateDocAttr(ctx context.Context, req *ai.ReqBatchUpdateDocAttr, rsp *ai.RspBatchUpdateDocAttr) error {
	if req.QueryId == 0 && len(req.Id) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	if len(req.Mask.Paths) == 0 {
		return nil
	}
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	operator := &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id}
	pbReq := &aipb.ReqUpdateDocAttrInBulk{
		Id:              req.Id,
		Mask:            req.Mask,
		Contributor:     req.Contributor,
		ShowContributor: req.ShowContributor,
		DownloadAsRef:   req.DownloadAsRef,
		AssistantId:     req.AssistantId,
		UpdateBy:        operator,
		MatchPatterns:   req.MatchPatterns,
		Reference:       req.Reference,
		QueryId:         req.QueryId,
	}
	// 加大超时时间
	rpcTimeOut := time.Duration(10) * time.Second
	rpcRsp, err := client.AiClient.UpdateDocAttrInBulk(ctx, pbReq, mclient.WithRequestTimeout(rpcTimeOut))
	if err != nil {
		return err
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}
	return nil
}

// ReparseTextFiles 重新解析doc
func (a Ai) ReparseTextFiles(ctx context.Context, req *ai.ReqReparseTextFiles, rsp *ai.RspReparseTextFiles) error {
	if req.QueryId == 0 && len(req.Ids) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}
	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	rpcRsp, err := client.AiClient.ReparseTextFiles(ctx, &aipb.ReqReparseTextFiles{
		Ids:       req.Ids,
		Operator:  &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
		ParseMode: req.ParseMode,
		QueryId:   req.QueryId,
	})
	if err != nil {
		return err
	}
	if rpcRsp != nil {
		rsp.Async = rpcRsp.Async
	}

	return err
}

// GetTextFileTip 获取文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
func (a *Ai) GetTextFileTip(ctx context.Context, req *ai.ReqGetTextFileTip, rsp *ai.RspGetTextFileTip) error {
	// 构建调用微服务的请求参数
	pbReq := &aipb.ReqGetTextFileTip{
		Id: req.Id,
	}

	// 调用AI服务
	pbRsp, err := client.AiClient.GetTextFileTip(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("get text file tip failed: %v", err)
		return err
	}

	// 将微服务返回的结果转换为BFF层的响应格式
	rsp.TableOverSize = pbRsp.TableOverSize
	rsp.State = pbRsp.State
	rsp.Repeated = pbRsp.Repeated

	return nil
}

// GetQaTip 查询QA的知识提示（问题超长，内容重复）等信息
func (a *Ai) GetQaTip(ctx context.Context, req *ai.ReqGetQaTip, rsp *ai.RspGetQaTip) error {
	// 构建调用微服务的请求参数
	pbReq := &aipb.ReqGetQaTip{
		Id: req.Id,
	}

	// 调用AI服务
	pbRsp, err := client.AiClient.GetQaTip(ctx, pbReq)
	if err != nil {
		log.WithContext(ctx).Errorf("get qa tip failed: %v", err)
		return err
	}

	// 将微服务返回的结果转换为BFF层的响应格式
	rsp.QuestionOverSize = pbRsp.QuestionOverSize
	rsp.Repeated = pbRsp.Repeated

	return nil
}

// CreateDocQuery ...
func (a Ai) CreateDocQuery(ctx context.Context, req *ai.ReqCreateDocQuery,
	rsp *ai.RspCreateDocQuery,
) error {
	var err error

	user := xsession.UserFromContext[mgmt.OpUser](ctx)
	rpcReq := &aipb.ReqCreateDocQuery{
		CreateBy: &aipb.Operator{Type: base.IdentityType_IDENTITY_TYPE_MGMT, Id: user.Id},
	}

	if req.Doc != nil {
		rpcReq.Doc, err = ailogic.CreateAiReqListTextFile(ctx, req.Doc)
	} else if req.Qa != nil {
		rpcReq.Qa, err = ailogic.CreateAiListQA(ctx, req.Qa)
	} else {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	if err != nil {
		return err
	}
	rcpRsp, err := client.AiClient.CreateDocQuery(ctx, rpcReq)
	if err != nil {
		return err
	}
	rsp.QueryId = rcpRsp.QueryId
	rsp.TotalCount = rcpRsp.TotalCount
	rsp.IsEmpty = rcpRsp.IsEmpty
	return nil
}
