package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"

	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
)

// ListChat chat对话列表
func (a *Ai) ListChat(ctx context.Context, req *bffaipb.ReqListChat,
	rsp *bffaipb.RspListChat) error {
	var err error

	tenant, err := ailogic.GetTanliveOpCustomLabelTenant()
	if err != nil {
		return err
	}
	rpcReq := &ai.ReqListChat{
		Offset: req.Offset,
		Limit:  req.Limit,
		Filter: &ai.ReqListChat_Filter{
			UserIds:         req.Filter.UserIds,
			ChatTitles:      req.Filter.ChatTitles,
			ChatType:        req.Filter.ChatType,
			Nicknames:       req.Filter.Nicknames,
			RejectJobResult: req.Filter.RejectJobResult,
			RegionCodes:     req.Filter.RegionCodes,
			IsManual:        req.Filter.IsManual,
		},
		CreateDateRange: req.CreateDateRange,
		UpdateDateRange: req.UpdateDateRange,
		Labels:          req.Labels,
		AssistantIds:    req.AssistantIds,
		LabelTenant:     tenant,
		OrderByLabel:    req.OrderByLabel,
	}

	if len(req.OrderBy) > 0 {
		rpcReq.OrderBy, err = logic.TransformOrderBy(req.OrderBy)
		if err != nil {
			return err
		}
	} else { // 默认按照更新时间倒序
		rpcReq.OrderBy = []*base.OrderBy{{Column: "update_date", Desc: true}}
	}

	rpcRsp, err := client.AiClientByRegion(req.Filter.Region).ListChat(ctx, rpcReq)
	if err != nil {
		return err
	}
	if rpcRsp == nil || rpcRsp.TotalCount == 0 {
		return nil
	}

	userIDs := make([]uint64, 0, len(rpcRsp.Chats))
	assistantIds := make(map[uint64]struct{})
	for _, v := range rpcRsp.Chats {
		assistantIds[v.AssistantId] = struct{}{}
		if v.CreateBy > 0 {
			userIDs = append(userIDs, v.CreateBy)
		}
	}
	userMap, err := logic.GetUsersMapByKey(ctx, userIDs)
	if err != nil {
		return err
	}
	for _, v := range userMap {
		if ailogic.IsWechatUnionID(v.Username) { // 小程序的临时用户要显示用户昵称，但是不太方便返回给前端让前端判断显示，此处直接username设置为空，前端显示nickname
			v.Username = ""
			v.NickName = ailogic.ChatUsernameMask(v.NickName)
		}
	}

	rsp.Chats = make([]*bffaipb.Chat, 0, len(rpcRsp.Chats))
	assistantMap := ailogic.GetAssistantInfo(ctx, maps.Keys(assistantIds)...)

	rsp.TotalCount = rpcRsp.TotalCount
	for _, v := range rpcRsp.Chats {
		chat := &bffaipb.Chat{
			Id:              v.Id,
			Title:           v.Title,
			CreateBy:        userMap[v.CreateBy],
			CreateDate:      v.CreateDate,
			UpdateDate:      v.UpdateDate,
			ChatType:        v.ChatType,
			AssistantName:   assistantMap.GetName(v.AssistantId),
			AssistantId:     v.AssistantId,
			SupportType:     v.SupportType,
			ChatState:       assistantMap.GetChatStatus(v.ChatState, v.AssistantId, v.ChatType, v.UpdateDate),
			QuestionCnt:     v.QuestionCnt,
			Labels:          v.Labels,
			RejectJobResult: v.RejectJobResult,
			RegionCode:      v.RegionCode,
			IsManual:        v.IsManual,
			DocHits:         v.DocHits,
			AvgDuration:     v.AvgDuration,
		}
		if v.RatingScale > 8 {
			chat.RatingScale = ai.RatingScale_RATING_SCALE_SATISFIED
		} else if v.RatingScale == 0 {
			chat.RatingScale = ai.RatingScale_RATING_SCALE_UNSPECIFIED
		} else if v.RatingScale <= 3 {
			chat.RatingScale = ai.RatingScale_RATING_SCALE_DISSATISFIED
		} else {
			chat.RatingScale = ai.RatingScale_RATING_SCALE_AVERAGE
		}
		if len(v.Nickname) > 0 {
			chat.CreateBy = &iampb.UserInfo{Username: ailogic.ChatUsernameMask(v.Nickname)}
		}
		rsp.Chats = append(rsp.Chats, chat)
	}

	return nil
}

// GetChatDetail chat对话详情
func (a *Ai) GetChatDetail(ctx context.Context, req *bffaipb.ReqGetChatDetail,
	rsp *bffaipb.RspGetChatDetail) error {
	origin := ai.ChatOrigin_CHAT_ORIGIN_UNSPECIFIED
	if ailogic.IsWuduUser(ctx) { // 无毒用户只允许查询无毒的数据
		origin = ai.ChatOrigin_CHAT_TYPE_WUDU
	} else if ailogic.IsShenghuanUser(ctx) { // 生环用户只允许查询生环的数据
		origin = ai.ChatOrigin_CHAT_TYPE_SHENGHUAN
	} else if ailogic.IsTFoundationUser(ctx) { // 腾讯基金会用户只允许查询生环的数据
		origin = ai.ChatOrigin_CHAT_ORIGIN_TFOUNDATION
	} else if ailogic.IsZHFoundationUser(ctx) { // 洲明基金会只允许查询生环的数据
		origin = ai.ChatOrigin_CHAT_ORIGIN_ZHFOUNDATION
	}
	// 获取chat
	chat, err := client.AiClientByRegion(req.Region).GetChatDetail(ctx, &ai.ReqGetChatDetail{
		Id:     req.Id,
		Origin: origin,
	})
	if err != nil || chat == nil {
		log.WithContext(ctx).Errorw("GetChatDetail GetChatDetail", "err", err)
		return err
	}
	detail := chat.ChatDetail
	var userInfo *iampb.UserInfo
	if detail.CreateBy == 0 {
		userInfo = &iampb.UserInfo{
			Username: ailogic.ChatUsernameMask(detail.Nickname),
		}
	} else {
		userInfo, err = logic.GetUserByKey(ctx, detail.CreateBy)
		if err != nil {
			log.WithContext(ctx).Errorw("GetChatDetail GetUserByKey", "err", err)
			return err
		}
		if ailogic.IsWechatUnionID(userInfo.Username) { // 小程序的临时用户要显示用户昵称，但是不太方便返回给前端让前端判断显示，此处直接username设置为空，前端显示nickname
			userInfo.Username = ""
			userInfo.NickName = ailogic.ChatUsernameMask(userInfo.NickName)
		}
	}

	assistantInfo := ailogic.GetAssistantInfo(ctx, detail.AssistantId)
	rsp.ChatDetail = &bffaipb.ChatDetail{
		Id:          detail.Id,
		Title:       detail.Title,
		Region:      req.Region,
		CreateBy:    userInfo,
		ChatType:    detail.ChatType,
		CreateDate:  detail.CreateDate,
		FinishDate:  detail.UpdateDate,
		SupportType: detail.SupportType,
		ChatState:   assistantInfo.GetChatStatus(detail.ChatState, detail.AssistantId, detail.ChatType, detail.UpdateDate),
		AssistantId: detail.AssistantId,
	}

	if detail.AssistantId > 0 {
		assistant, err1 := client.AiClient.GetAssistant(ctx, &ai.ReqGetAssistant{Id: detail.AssistantId})
		if err1 != nil || assistant.AssistantDetail == nil {
			log.WithContext(ctx).Errorw("GetChatDetail GetAssistant error", "err", err1)
		} else {
			rsp.ChatDetail.AssistantAvatar = ailogic.GetWorkWeixinAccountAvatar(ctx, assistant.AssistantDetail.AppCode)
		}
	}

	if detail.ChatType == ai.ChatType_CHAT_TYPE_WECHAT || detail.ChatType == ai.ChatType_CHAT_TYPE_WHATSAPP {
		return ailogic.DescribeUserChatRecords(ctx, req, rsp)
	}

	messages, totalCount, err := ailogic.DescribeChatQuestionAnswersByPage(ctx, detail.Id, req.QuestionId, req.Limit, req.Offset)
	if err != nil {
		return err
	}

	rsp.ChatDetail.Messages = messages
	rsp.TotalCount = totalCount
	if len(messages) > 0 {
		rsp.ChatDetail.CreateDate = messages[0].CreateDate
		rsp.ChatDetail.FinishDate = messages[len(messages)-1].CreateDate
	}

	return nil
}

func (a *Ai) GetChatMessageDetail(ctx context.Context, req *bffaipb.ReqGetChatMessageDetail, rsp *bffaipb.RspGetChatMessageDetail) error {
	// 获取答案
	response, err := client.AiClientByRegion(req.Region).DescribeMessage(ctx, &ai.ReqDescribeMessage{
		MessageId:      req.Id,
		WithConfig:     true,
		WithDocs:       true,
		WithCollection: true,
		WithFeedback:   true,
	})
	if err != nil {
		return err
	}
	if response == nil || response.Message == nil {
		return xerrors.InternalServerError(err)
	}

	// 获取docs
	var docs []*ai.ChatMessageDoc
	if response.Message.DocSnapshot != nil {
		docs = response.Message.DocSnapshot.Docs
	} else {
		docs, err = ailogic.DescribeMessagesDocs(ctx, []*ai.ChatMessage{response.Message})
		if err != nil {
			return xerrors.InternalServerError(err)
		}
	}

	message := ailogic.TransformAIMessageToEventMessage(response.Message, docs)
	rsp.Message = message

	// 获取助手
	aRsp, err := client.AiClient.GetAssistant(ctx, &ai.ReqGetAssistant{Id: message.AssistantId})
	if err != nil {
		return err
	}

	// 获取log
	res, err := client.AiClientByRegion(req.Region).DescribeMessageLogs(ctx, &ai.ReqDescribeMessageLog{MessageId: response.Message.Id, AssistantDetail: aRsp.AssistantDetail, IgnoreChainVisible: true})
	if err != nil {
		return err
	}
	rsp.Logs = res.MessageLogs

	// 获取CollectionSnapshot
	//qres, err := client.AiClientByRegion(req.Region).DescribeMessage(ctx, &ai.ReqDescribeMessage{
	//	MessageId: req.Id,
	//})
	//if err != nil {
	//	return xerrors.InternalServerError(err)
	//}
	if response.Message.CollectionSnapshot != nil {
		// 修复历史数据
		snapshot := response.Message.CollectionSnapshot
		rsp.CollectionTime = &base.TimeRange{
			Start: snapshot.StartTime,
			End:   snapshot.EndTime,
		}
		rsp.CleanChunks = snapshot.CleanChunks
		itemsRsp, err := client.AiClient.FixSearchCollectionItems(ctx, &ai.ReqFixSearchCollectionItems{
			Items: snapshot.Items,
		})
		if err == nil {
			snapshot.Items = itemsRsp.Items
			_, err = client.AiClientByID(req.Id).UpdateChatMessageCollections(ctx, &ai.ReqUpdateChatMessageCollections{
				MessageId:   response.Message.Id,
				Items:       snapshot.Items,
				StartTime:   snapshot.StartTime,
				EndTime:     snapshot.EndTime,
				CleanChunks: snapshot.CleanChunks,
			})
			if err != nil {
				log.WithContext(ctx).Errorf("marshal collection items failed: %v", err)
				return err
			}
		}

		// get contributor..
		var contributors []*ai.Contributor
		for _, doc := range snapshot.Items {
			contributors = append(contributors, doc.Contributor...)
		}
		_, err = ailogic.GetAiDoctContributorShowInfo(ctx, contributors...)

		// get operators
		var operators []*ai.Operator
		for _, v := range snapshot.Items {
			if v.UpdateBy != nil {
				operators = append(operators, v.UpdateBy)
			}
		}
		operatorsToShow, err := ailogic.GetAiDocOperatorsShowInfo(ctx, operators...)

		if err != nil {
			return err
		}
		for _, v := range snapshot.Items {
			operator := operatorsToShow[v.UpdateBy]

			ci := &bffaipb.SearchCollectionItem{
				Id:          v.Id,
				Text:        v.Text,
				Question:    v.Question,
				Score:       v.Score,
				FileName:    v.FileName,
				Url:         v.Url,
				Contributor: v.Contributor,
				UpdateBy:    operator,
				Type:        v.Type,
				IsRelated:   v.IsRelated,
				DocType:     v.DocType,
				DocId:       v.DocId,
				DataSource:  v.DataSource,
				DocName:     v.DocName,
			}

			// oneshot兼容上线前的历史数据
			if v.Type == ai.SearchCollectionType_SEARCH_COLLECTION_TYPE_UNSPECIFIED {
				ci.Type = ai.SearchCollectionType_SEARCH_COLLECTION_TYPE_VECTOR
			}

			if v.Question != "" {
				ci.DocType = ai.DocType_DOCTYPE_QA
			} else if v.FileName != "" && v.Url != "" {
				ci.DocType = ai.DocType_DOCTYPE_FILE
			} else {
				ci.DocType = ai.DocType_DOCTYPE_TEXT
			}

			rsp.CollectionItems = append(rsp.CollectionItems, ci)
		}
	}

	return nil
}

// SearchChat 搜索chat
func (a *Ai) SearchChat(ctx context.Context, req *bffaipb.ReqSearchChat, rsp *bffaipb.RspSearchChat) error {
	userId := xsession.SessionFromContext(ctx).GetLoginUser().Uint64()
	var answer *ai.ChatMessage
	var docs []*ai.ChatMessageDoc

	f := func(ctx context.Context) error {
		var err error
		if answer.Type == ai.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION && len(answer.DocNames) > 0 {
			docs, err = ailogic.DescribeMessagesDocs(ctx, []*ai.ChatMessage{answer})
		}
		return err
	}

	startTime := time.Now()
	if qa, err := client.AiClient.DescribeMessageMatchQa(ctx, &ai.ReqDescribeMessageMatchQa{Text: req.Text, AssistantId: req.AssistantId}); err == nil && len(qa.Docs) > 0 {
		time.Sleep(2 * time.Second)
		docNames := []string{qa.Docs[0].RagFilename}
		answerText := qa.Docs[0].Text
		for i := 1; i < len(qa.Docs); i++ {
			answerText += "\n\n" + qa.Docs[i].Text
			docNames = append(docNames, qa.Docs[i].RagFilename)
		}
		answer = &ai.ChatMessage{
			Type:            ai.ChatMessageType_CHAT_MESSAGE_TYPE_COLLECTION,
			QuestionId:      req.QuestionId,
			AssistantId:     req.AssistantId,
			Text:            answerText,
			DocMatchPattern: qa.MatchPattern,
			DocNames:        docNames,
			FinalQuery:      req.Text,
			StartTime:       timestamppb.New(startTime),
			EndTime:         timestamppb.Now(),
		}
		if err = f(ctx); err != nil {
			return err
		}
		rsp.Message = ailogic.TransformAIMessageToEventMessageWithDocText(answer, docs, true)
		rsp.UserId = userId
		rsp.IsOnlySearch = true
	} else {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			// get assistant
			aRsp, err := client.AiClient.GetAssistant(ctx, &ai.ReqGetAssistant{Id: req.AssistantId})
			if err != nil {
				return err
			}
			assistant := aRsp.AssistantDetail
			if req.TopN > 0 {
				assistant.SearchTopN = int32(req.TopN)
				assistant.DocTopN = int32(req.TopN)
			}
			assistant.Threshold = req.Threshold
			assistant.TextRecallTopN = req.TextRecallTopN
			assistant.Temperature = req.Temperature
			assistant.CleanChunks = req.CleanChunks
			sendRsp, err := client.AiClient.SendMessageWithoutSaveSync(ctx, &ai.ReqSendMessageWithoutSaveSync{
				Text:            req.Text,
				QuestionId:      req.QuestionId,
				AssistantDetail: assistant,
				WaitAnswer:      true,
			}, mclient.WithRequestTimeout(180*time.Second))
			if err != nil {
				return err
			}
			answer = sendRsp.Message
			answer.QuestionId = req.QuestionId
			if err = f(ctx); err != nil {
				return err
			}
			eventMsg := ailogic.TransformAIMessageToEventMessage(answer, docs)
			if _, err = client.AiClient.PublishChatMessage(ctx, &ai.ReqPublishChatMessage{Message: eventMsg, UserId: userId, IsOp: true}); err != nil {
				return err
			}
			return nil
		}, boot.TraceGo(ctx))
	}

	return nil
}

// SyncFixChatMessageCollection 修复更新message的collection
func (a *Ai) SyncFixChatMessageCollection(ctx context.Context, req *bffaipb.ReqSyncFixChatMessageCollection, rsp *emptypb.Empty) error {
	go func() {
		_, err := client.AiClient.SyncFixChatMessageCollection(context.Background(), &ai.ReqSyncFixChatMessageCollection{}, mclient.WithRequestTimeout(180*time.Second))
		if err != nil {
			log.WithContext(ctx).Errorf("sync fix chat message collection err: %v", err)
		}
		_, err = client.AiInternational.SyncFixChatMessageCollection(context.Background(), &ai.ReqSyncFixChatMessageCollection{}, mclient.WithRequestTimeout(180*time.Second))
		if err != nil {
			log.WithContext(ctx).Errorf("sync fix chat message collection err: %v", err)
		}
	}()
	return nil
}

// ProxyChatHtmlUrl 获取url title
func (a *Ai) ProxyChatHtmlUrl(ctx context.Context, req *bffaipb.ReqProxyChatHtmlUrl, rsp *bffaipb.RspProxyChatHtmlUrl) error {
	var urlWithTitles map[string]string
	assistant, err := client.AiClient.GetAssistant(ctx, &ai.ReqGetAssistant{Id: req.AssistantId})
	if err != nil {
		return err
	}
	urlWithTitles, err = ailogic.FetchChatHtmlTitle(ctx, req.Urls, assistant.AssistantDetail)
	if err != nil {
		return err
	}
	contents := make([]*bffaipb.RspProxyChatHtmlUrl_Content, 0, len(req.Urls))
	for url, title := range urlWithTitles {
		contents = append(contents, &bffaipb.RspProxyChatHtmlUrl_Content{Url: url, Title: title})
	}
	rsp.Contents = contents
	return nil
}

// DescribeChatSuggestLog 获取建议问题log
func (a *Ai) DescribeChatSuggestLog(ctx context.Context, req *bffaipb.ReqDescribeChatSuggestLog, rsp *bffaipb.RspDescribeChatSuggestLog) error {
	res, err := client.AiClientByID(req.MessageId).DescribeSuggestLogs(ctx, &ai.ReqDescribeSuggestLog{MessageId: req.MessageId})
	if err != nil {
		return err
	}
	rsp.Logs = res.Logs
	return nil
}

// DescribeChatRegionCode 获取所有会话的地区编码
func (a *Ai) DescribeChatRegionCode(ctx context.Context, req *bffaipb.ReqDescribeChatRegionCode,
	rsp *bffaipb.RspDescribeChatRegionCode) error {
	regionClient := client.AiClient
	if config.GetBoolOr("bff.abroad", true) {
		regionClient = client.AiClientByRegion(req.Region)
	}
	rpcRsp, err := regionClient.DescribeChatRegionCode(ctx, &ai.ReqDescribeChatRegionCode{
		AssistantIds: req.AssistantIds,
	})
	if err != nil {
		return err
	}

	if rpcRsp == nil || len(rpcRsp.RegionCodes) == 0 {
		return nil
	}

	rsp.RegionCodes = rpcRsp.RegionCodes

	return nil
}
