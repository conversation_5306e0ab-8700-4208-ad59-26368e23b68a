package ai

import (
	"context"
	"encoding/json"
	"math"
	"sync"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-mgmt/ai"
	terrors "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	mclient "github.com/asim/go-micro/v3/client"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
)

// TanliveOpTaskTenant 运营端任务租户id，默认是最大值，以区分其他用户端团队
const TanliveOpTaskTenant = uint64(math.MaxUint64)

func UpdateTaskState(ctx context.Context, taskID uint64, status aipb.ExportTaskState, extraInfo ...string) error {
	_, err := client.AiClient.UpdateExportTask(ctx, &aipb.ReqUpdateExportTask{
		Id:    taskID,
		State: status,
		ExtraInfo: func() string {
			if len(extraInfo) > 0 {
				return extraInfo[0]
			}
			return ""
		}(),
	})
	if err != nil {
		return err
	}
	return nil
}

type ImportDocTaskInfo struct {
	SucceedCount uint64                  `json:"succeed_count"`
	FailedInfo   []*ImportDocTaskErrInfo `json:"failed_info"`
}

type ImportDocTaskErrInfo struct {
	DocType aipb.DocType
	DocName string
	Err     string
}

var (
	ImportDocTaskErr = xerrors.InternalServerError("")
	ImportRpcTimeOut = func() time.Duration {
		d, err := time.ParseDuration(config.GetStringOr("export.task.doc.timeout", "10m"))
		if err == nil {
			return d
		}
		return time.Second * 60 * 30
	}
)

var ImportParallism = func() int {
	return config.GetIntOr("import.task.doc.parallism", 1)
}

type QAImportLogic struct {
	ctx context.Context
	req *ai.ReqImportQAs

	toCreate      map[int]*aipb.QA
	toUpdate      map[int]*aipb.ReqUpdateQA
	toUpdateShare map[int]*ai.ReqCreateAssistantShare

	contributor     *aipb.Contributor
	operator        *aipb.Operator
	scopedAssistant []uint64
	labelTenant     uint64
}

func NewQAImportLogic(ctx context.Context, contributor *aipb.Contributor,
	operator *aipb.Operator, scopedAssistant []uint64, req *ai.ReqImportQAs,
) *QAImportLogic {
	return &QAImportLogic{
		ctx:             ctx,
		req:             req,
		contributor:     contributor,
		operator:        operator,
		scopedAssistant: scopedAssistant,
		toCreate:        make(map[int]*aipb.QA),
		toUpdate:        make(map[int]*aipb.ReqUpdateQA),
		toUpdateShare:   make(map[int]*ai.ReqCreateAssistantShare, 0),
	}
}

func (l *QAImportLogic) pbTextFileToPbUpdateShare(order int, id uint64, v *ai.ReqImportQA) *ai.ReqCreateAssistantShare {
	if slices.Contains(v.GetMask().GetPaths(), "share_assistant_id") {
		l.toUpdateShare[order] = &ai.ReqCreateAssistantShare{
			AssistantId: v.ShareAssistantId,
			DocId:       id,
		}
	}
	return nil
}

func (l *QAImportLogic) pbTextFileToPbUpdate(id uint64, v *ai.ReqImportQA) *aipb.ReqUpdateQA {
	item := &aipb.ReqUpdateQA{
		Id:                id,
		Question:          v.Question,
		Answer:            v.Answer,
		Contributor:       v.Contributor,
		Operator:          l.operator,
		Mask:              v.Mask,
		ShowContributor:   v.ShowContributor,
		ScopedAssistantId: l.scopedAssistant,
		States: func() []*aipb.DocAssistantState {
			r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
			for _, vv := range v.AssistantId {
				// 先把状态设置为禁用,后续重写状态
				r = append(r, &aipb.DocAssistantState{State: aipb.DocState_DOC_STATE_DISABLED, AssistantId: vv})
			}
			return r
		}(),
		Labels:        v.Labels,
		Reference:     v.Reference,
		LabelTenant:   l.labelTenant,
		MatchPatterns: v.MatchPatterns,
	}
	l.rewriteMask(item, item.Mask)
	return item
}

func (l *QAImportLogic) pbTextFileToPbCreate(v *ai.ReqImportQA) *aipb.QA {
	item := &aipb.QA{
		Question:        v.Question,
		Answer:          v.Answer,
		Contributor:     v.Contributor,
		CreateBy:        l.operator,
		UpdateBy:        l.operator,
		ShowContributor: v.ShowContributor,
		Labels:          v.Labels,
		States: func() []*aipb.DocAssistantState {
			r := make([]*aipb.DocAssistantState, 0, len(v.AssistantId))
			for _, vv := range v.AssistantId {
				// 导入创建时，默认时禁用状态
				r = append(r, &aipb.DocAssistantState{State: aipb.DocState_DOC_STATE_DISABLED, AssistantId: vv})
			}
			return r
		}(),
		Reference:     v.Reference,
		MatchPatterns: v.MatchPatterns,
	}
	if len(item.Contributor) == 0 {
		item.Contributor = append(item.Contributor, l.contributor)
	}
	return item
}

func (l *QAImportLogic) rewriteMask(req *aipb.ReqUpdateQA, mask *fieldmaskpb.FieldMask) *fieldmaskpb.FieldMask {
	if mask == nil {
		return nil
	}
	mapping := map[string]string{
		// assistant_id 会被填充为states字段
		"assistant_id": "states",
	}
	for i, path := range mask.GetPaths() {
		if v, ok := mapping[path]; ok {
			mask.Paths[i] = v
		}
	}

	// 如果没有管理的助手，那么无需修改助手关联关系
	if len(l.scopedAssistant) == 0 {
		mask.Paths = slices.DeleteFunc(mask.Paths, func(s string) bool {
			return s == "states"
		})
	}
	// 如果贡献者为空，不需要更新贡献者
	if len(req.Contributor) == 0 {
		mask.Paths = slices.DeleteFunc(mask.Paths, func(s string) bool {
			return s == "contributor"
		})
	}
	// 添加更新人 mask
	mask.Paths = append(mask.Paths, "update_by")
	return mask
}

// SplitCreateAndUpdate 区分出哪些是创建/哪些是覆盖
func (l *QAImportLogic) SplitCreateAndUpdate() error {
	// labelTenantId, err := GetCustomLabelTenantId(l.ctx)
	// if err != nil {
	// return err
	// }
	// l.labelTenant = labelTenantId

	// 分离需要校验重复的和强制新建的
	var needValidateItems []*aipb.QA
	var needValidateIndexes []int

	for i, v := range l.req.Items {
		if v.ForceCreate {
			// 强制新建，直接创建，不进行重复校验
			l.toCreate[i] = l.pbTextFileToPbCreate(v)
		} else {
			// 需要进行重复校验
			needValidateItems = append(needValidateItems, &aipb.QA{
				Question: v.Question,
			})
			needValidateIndexes = append(needValidateIndexes, i)
		}
	}

	// 如果有需要校验的项目，进行批量校验
	if len(needValidateItems) > 0 {
		validReq := &aipb.ReqValidateQAInBulk{
			Contributor:     l.contributor,
			ScopedAssistant: l.scopedAssistant,
			Items:           needValidateItems,
		}
		rsp, err := client.AiClient.ValidateQAInBulk(l.ctx, validReq)
		if err != nil {
			return err
		}

		// 处理校验结果
		for j, originalIndex := range needValidateIndexes {
			v := l.req.Items[originalIndex]
			if rsp.Errors[j].Error != terrors.AiError_AiNoError {
				// 存在重复，进行更新
				l.toUpdate[originalIndex] = l.pbTextFileToPbUpdate(rsp.Errors[j].Id, v)
				l.pbTextFileToPbUpdateShare(originalIndex, rsp.Errors[j].Id, v)
			} else {
				// 不存在重复，进行创建
				l.toCreate[originalIndex] = l.pbTextFileToPbCreate(v)
			}
		}
	}

	err := l.rewriteUpdateAssistantState()
	if err != nil {
		return err
	}

	return nil
}

// 目前，一个doc在多个助手中，只能存在一中状态
func (l *QAImportLogic) rewriteUpdateAssistantState() error {
	if len(l.toUpdate) != 0 {
		ids := make([]uint64, 0, len(l.toUpdate))
		for _, v := range l.toUpdate {
			ids = append(ids, v.Id)
		}

		fs, err := client.AiClient.ListQA(l.ctx, &aipb.ReqListQA{
			Id: ids,
		})
		if err != nil {
			return err
		}
		for _, v := range l.toUpdate {
			for _, vv := range fs.Items {
				if vv.Id == v.Id && len(vv.States) != 0 {
					// 直接取第一个的状态
					for _, state := range v.States {
						state.State = vv.States[0].State
					}
				}
			}
		}
	}
	return nil
}

func (l *QAImportLogic) doCreate(req *aipb.QA, errHandler func(*ImportDocTaskErrInfo)) {
	creatQ := &aipb.ReqCreateQAInBulk{
		Items: []*aipb.QA{req},
	}
	// 创建时分享至助手
	for _, item := range l.req.Items {
		if item.Question == req.Question {
			creatQ.SharedAssistant = append(creatQ.SharedAssistant, &aipb.ReqCreateQAInBulk_Slice{Id: item.ShareAssistantId})
			break
		}
	}
	_, err := client.AiClient.CreateQAInBulk(l.ctx, creatQ, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Question, Err: ImportDocTaskErr.Error()})
	}
	return
}

func (l *QAImportLogic) doUpdate(req *aipb.ReqUpdateQA, errHandler func(*ImportDocTaskErrInfo)) {
	// is, err := IsDocContributor(l.ctx, req.Id)
	// if err != nil {
	// 	errHandler(&ImportDocTaskErrInfo{DocName: req.Question, Err: ImportDocTaskErr.Error()})
	// 	return
	// }
	// if !is {
	// 	IgnoreDocNoEditableFields(req)
	// }
	updateQ := req
	_, err := client.AiClient.UpdateQA(l.ctx, updateQ, mclient.WithRequestTimeout(ImportRpcTimeOut()))
	if err != nil {
		errHandler(&ImportDocTaskErrInfo{DocName: req.Question, Err: ImportDocTaskErr.Error()})
		return
	}
	return
}

func (l *QAImportLogic) Do() error {
	// 先创建任务
	task, err := client.AiClient.CreateExportTask(l.ctx, &aipb.ReqCreateExportTask{
		UserId:        TanliveOpTaskTenant,
		Type:          aipb.ExportTaskType_EXPORT_TASK_TYPE_QA,
		OperationType: aipb.TaskOperationType_TASK_OPERATION_TYPE_IMPORT,
	})
	if err != nil {
		return err
	}
	err = UpdateTaskState(l.ctx, task.Task.Id, aipb.ExportTaskState_EXPORT_TASK_STATE_RUNNING)
	if err != nil {
		return err
	}

	xsync.SafeGo(l.ctx, func(ctx context.Context) error {
		var taskErrInfo []*ImportDocTaskErrInfo
		mtx := &sync.Mutex{}
		recordTaskErrInfo := func(info *ImportDocTaskErrInfo) {
			mtx.Lock()
			defer mtx.Unlock()
			info.DocType = aipb.DocType_DOCTYPE_QA
			for _, v := range taskErrInfo {
				if v.DocName == info.DocName && v.DocType == info.DocType {
					return
				}
			}
			taskErrInfo = append(taskErrInfo, info)
		}

		defer func() {
			var errInfo []byte
			var taskInfo ImportDocTaskInfo
			taskInfo.FailedInfo = taskErrInfo
			taskInfo.SucceedCount = uint64(len(l.req.Items) - len(taskErrInfo))
			errInfo, _ = json.Marshal(taskInfo)
			state := aipb.ExportTaskState_EXPORT_TASK_STATE_COMPLETED
			if err != nil {
				state = aipb.ExportTaskState_EXPORT_TASK_STATE_FAILED
			}
			err = UpdateTaskState(l.ctx, task.Task.Id, state, string(errInfo))
			if err != nil {
				log.Errorf("update import text file task status failed, %s", err.Error())
			}
		}()

		// 1. 先拆分创建和更新
		err = l.SplitCreateAndUpdate()
		if err != nil {
			return err
		}

		parallelism := ImportParallism()
		synG := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)), xsync.GroupQuota(parallelism))
		items, groupSize := groupSlice(l.req.Items, parallelism)
		for groupIdx, group := range items {
			groupIdx := groupIdx
			group := group
			synG.SafeGo(func(ctx context.Context) error {
				for offset := range group {
					idx := groupSize*groupIdx + offset

					// 创建
					createReq := l.toCreate[idx]
					if createReq != nil {
						l.doCreate(createReq, recordTaskErrInfo)
					}

					// 更新
					updateReq := l.toUpdate[idx]
					if updateReq != nil {
						l.doUpdate(updateReq, recordTaskErrInfo)
					}

					// 更新分享至(运营端无法更新分享)
					// updateShareReq := l.toUpdateShare[idx]
					// if updateShareReq != nil {
					// 	l.doUpdateShare(updateShareReq, recordTaskErrInfo)
					// }
				}
				return nil
			})
		}
		synG.Wait()
		return nil
	}, boot.TraceGo(l.ctx))

	return nil
}

// 泛型分组函数
func groupSlice[T any](slice []T, groupCount int) ([][]T, int) {
	if groupCount <= 0 {
		return nil, 0 // 如果分组数无效，返回空
	}

	result := make([][]T, 0, groupCount)                    // 创建结果切片
	groupSize := (len(slice) + groupCount - 1) / groupCount // 计算每组的平均大小

	for i := 0; i < len(slice); i += groupSize {
		end := i + groupSize
		if end > len(slice) {
			end = len(slice) // 防止越界
		}
		result = append(result, slice[i:end]) // 将分组结果追加到结果切片中
	}
	return result, groupSize
}
