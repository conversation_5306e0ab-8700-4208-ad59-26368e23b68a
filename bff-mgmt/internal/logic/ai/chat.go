package ai

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/client"
	"e.coding.net/tencent-ssv/tanlive/services/bff-mgmt/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/workweixin"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	mclient "github.com/asim/go-micro/v3/client"
	"golang.org/x/sync/errgroup"
)

type CollectionSnapshot struct {
	StartTime   time.Time                    `json:"startTime,omitempty"`
	EndTime     time.Time                    `json:"endTime,omitempty"`
	Items       []*aipb.SearchCollectionItem `json:"items,omitempty"`
	CleanChunks bool                         `json:"cleanChunks,omitempty"`
}

// ExtractMessageDocNames 从消息中提取所有文档名称
func ExtractMessageDocNames(messages []*aipb.ChatMessage) []string {
	docNamesSet := make(map[string]struct{})
	for _, m := range messages {
		if m.QuestionId > 0 {
			names := m.DocNames
			for _, name := range names {
				if name != "" {
					docNamesSet[name] = struct{}{}
				}
			}
		}
	}
	docNames := make([]string, 0, len(docNamesSet))
	for name := range docNamesSet {
		docNames = append(docNames, name)
	}
	return docNames
}

func DescribeMessagesDocs(ctx context.Context, messages []*aipb.ChatMessage) ([]*aipb.ChatMessageDoc, error) {
	docNames := ExtractMessageDocNames(messages)
	var docs []*aipb.ChatMessageDoc
	if len(docNames) == 0 {
		return docs, nil
	}

	docRsp, err := client.AiClient.DescribeMessageDocs(ctx, &aipb.ReqDescribeMessageDocs{DocNames: strings.Join(docNames, ","), HitCount: false})
	if err != nil {
		return docs, err
	}
	docs = docRsp.Docs
	var contributors []*aipb.Contributor
	for _, doc := range docs {
		contributors = append(contributors, doc.Contributor...)
	}
	_, err = GetAiDoctContributorShowInfo(ctx, contributors...)
	if err != nil {
		return nil, err
	}

	referenceMap := make(map[string]*aipb.DocReference)
	var messageDocs []*aipb.ChatMessageDoc
	for _, doc := range docs {
		md := &aipb.ChatMessageDoc{
			UgcType:     doc.UgcType,
			UgcId:       doc.UgcId,
			Contributor: doc.Contributor,
			DataType:    doc.DataType,
			FileName:    doc.FileName,
			DataSource:  doc.DataSource,
		}
		var docReference []*aipb.DocReference
		for _, reference := range doc.Reference {
			if reference.Id > 0 {
				if referenceMap[fmt.Sprintf("%d", reference.Id)] != nil {
					continue
				}
				referenceMap[fmt.Sprintf("%d", reference.Id)] = reference
			} else if len(reference.Name) > 0 {
				if referenceMap[reference.Name] != nil {
					continue
				}
				referenceMap[reference.Name] = reference
			} else if len(reference.Text) > 0 {
				if referenceMap[reference.Text] != nil {
					continue
				}
				referenceMap[reference.Text] = reference
			}
			docReference = append(docReference, reference)
		}
		md.Reference = docReference
		messageDocs = append(messageDocs, md)
	}

	return messageDocs, nil
}

func handleAnswerStopText(answer *aipb.ChatMessage) *aipb.ChatMessage {
	// 对已停止回答的回答进行返回内容处理
	if answer.LastOperator != nil && (answer.LastOperator.OperationType == aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_THINK || answer.LastOperator.OperationType == aipb.ChatOperationType_CHAT_OPERATION_TYPE_STOP_TEXT) {
		answer.Link = ""
		answer.DocNames = []string{}
		answer.Ugcs = []*aipb.MessageUgc{}
		answer.SuggestQuestion = []string{}
		if answer.LastOperator.StopText != "" {
			answer.Text = answer.LastOperator.StopText
		}
		if answer.LastOperator.StopThink != "" {
			answer.Think = answer.LastOperator.StopThink
		}
	}
	return answer
}

func combineQuestionAndAnswer(questions, answers []*aipb.ChatMessage, docs []*aipb.ChatMessageDoc) []*aipb.EventChatMessage {
	var messages []*aipb.EventChatMessage
	for _, question := range questions {
		pbQuestion := TransformAIMessageToEventMessage(question, nil)
		messages = append(messages, pbQuestion)
		var lastAnswer *aipb.ChatMessage
		var pbLastAnswer *aipb.EventChatMessage
		var combineAnswers []*aipb.EventChatMessage
		// 遍历问题的回答
		for _, answer := range answers {
			if answer == nil || answer.QuestionId != question.Id {
				continue
			}
			if lastAnswer == nil || answer.Id > lastAnswer.Id {
				lastAnswer = answer
			}
		}
		if lastAnswer != nil {
			pbLastAnswer = TransformAIMessageToEventMessage(handleAnswerStopText(lastAnswer), docs)
		}
		for _, answer := range answers {
			if answer == nil || answer.QuestionId != question.Id {
				continue
			}

			if lastAnswer != nil && answer.PublishHashId != lastAnswer.PublishHashId { // 重新回答的answer
				if pbLastAnswer != nil {
					pbAnswer := TransformAIMessageToEventMessage(handleAnswerStopText(answer), docs)
					pbLastAnswer.Answers = append(pbLastAnswer.Answers, pbAnswer)
				}
			} else { // 多个回答的answer
				if pbLastAnswer != nil && pbLastAnswer.Id == answer.Id {
					combineAnswers = append(combineAnswers, pbLastAnswer)
				} else {
					combineAnswers = append([]*aipb.EventChatMessage{TransformAIMessageToEventMessage(handleAnswerStopText(answer), docs)}, combineAnswers...)
				}
			}
		}

		messages = append(messages, combineAnswers...)
	}
	return messages
}

func DescribeChatQuestionAnswersByPage(ctx context.Context, chatId, questionId uint64, limit, offset uint32) ([]*aipb.EventChatMessage, uint32, error) {
	// 获取message
	rpcRsp, err := client.AiClientByID(chatId).DescribeChatQuestionAnswersByPage(ctx, &aipb.ReqDescribeChatQuestionAnswersByPage{
		ChatId:     chatId,
		Offset:     offset,
		Limit:      limit,
		QuestionId: questionId,
	})
	if err != nil || rpcRsp == nil {
		return nil, 0, err
	}

	// 获取docs
	var needDocsMessage []*aipb.ChatMessage
	for _, msg := range rpcRsp.Answers {
		if msg.DocSnapshot != nil {
			continue
		}
		needDocsMessage = append(needDocsMessage, msg)
	}

	docs, err := DescribeMessagesDocs(ctx, needDocsMessage)
	if err != nil {
		return nil, 0, err
	}

	// 把回答拼接到question的数组里
	messages := combineQuestionAndAnswer(rpcRsp.Questions, rpcRsp.Answers, docs)
	return messages, rpcRsp.TotalCount, nil
}

func TransformAIMessageToEventMessage(m *aipb.ChatMessage, docs []*aipb.ChatMessageDoc) *aipb.EventChatMessage {
	return TransformAIMessageToEventMessageWithDocText(m, docs, false)
}

func TransformAIMessageToEventMessageWithDocText(m *aipb.ChatMessage, docs []*aipb.ChatMessageDoc, withDocText bool) *aipb.EventChatMessage {
	docNames := m.DocNames
	messageDocs := make([]*aipb.ChatMessageDoc, 0, len(docNames))
	if m.DocSnapshot != nil {
		messageDocs = m.DocSnapshot.Docs
	} else {
		for _, doc := range docs {
			md := &aipb.ChatMessageDoc{
				UgcType:     doc.UgcType,
				UgcId:       doc.UgcId,
				Contributor: doc.Contributor,
				DataType:    doc.DataType,
				FileName:    doc.FileName,
				DataSource:  doc.DataSource,
				Reference:   doc.Reference,
			}
			if withDocText {
				md.IndexText = doc.IndexText
				md.Text = doc.Text
			}
			messageDocs = append(messageDocs, md)
		}
	}

	eventMsg := &aipb.EventChatMessage{
		Id:          m.Id,
		ChatId:      m.ChatId,
		Text:        m.Text,
		Type:        m.Type,
		RatingScale: m.RatingScale,
		Docs:        messageDocs,
		Link:        m.Link,
		QuestionId:  m.QuestionId,
		Ugcs:        m.Ugcs,
		CreateDate:  m.CreateDate,
		Lang:        m.Lang,
		AssistantId: m.AssistantId,
		State:       m.State,
		AskType:     m.AskType,
		ProcessTime: &base.TimeRange{
			Start: m.StartTime,
			End:   m.EndTime,
		},
		SqlQuery:          m.SqlQuery,
		RejectReason:      m.RejectReason,
		SuggestQuestions:  m.SuggestQuestion,
		DocFinalQuery:     m.FinalQuery,
		ImageUrl:          m.ImageUrl,
		ShowType:          m.ShowType,
		DocMatchPattern:   m.DocMatchPattern,
		SuggestionMode:    m.SuggestionMode,
		Think:             m.Think,
		ThinkDuration:     m.ThinkDuration,
		PromptType:        m.PromptType,
		LastOperationType: m.LastOperationType,
		FeedbackId:        m.FeedbackId,
		HasUserFeedback:   m.HasUserFeedback,
		IsFileReady:       m.IsFileReady,
		Files:             m.Files,
	}
	if m.Task != nil {
		eventMsg.IsAgentCommand = true
	}
	return eventMsg
}

// GetWorkWeixinAccountAvatar 获取企业微信助手账号头像
func GetWorkWeixinAccountAvatar(ctx context.Context, openKfid string) string {
	if openKfid == "" {
		log.WithContext(ctx).Error("GetWorkWeixinAccountAvatar openKfid is nil")
		return ""
	}
	if config.GetBoolOr("workWeixin.enable", true) {
		return ""
	}

	accounts, err := workweixin.BuildNewClient(workweixin.Client, "TODO", LoadOpenWorkAccessToken).
		BatchGetKfAccounts()
	if err != nil {
		log.WithContext(ctx).Errorw("GetWorkWeixinAccountAvatar error", "err", err, "openKfid", openKfid)
		return ""
	}
	if len(accounts) == 0 {
		log.WithContext(ctx).Errorw("GetWorkWeixinAccountAvatar cant find account", "openKfid", openKfid)
		return ""
	}

	for _, account := range accounts {
		if account.OpenKfid == openKfid {
			return account.Avatar
		}
	}
	log.WithContext(ctx).Errorw("GetWorkWeixinAccountAvatar cant find account again", "openKfid", openKfid)
	return ""
}

func FetchChatHtmlTitle(ctx context.Context, urls []string, assistant *aipb.AssistantDetail) (map[string]string, error) {
	var urlWithTitles map[string]string
	if assistant.SearchEngine == config.GetStringOr("llm.international.engine", "google") {
		res, err := client.AiInternational.FetchHtmlTitles(ctx, &aipb.ReqFetchHtmlTitles{Urls: urls}, mclient.WithRequestTimeout(60*time.Second))
		if err != nil {
			return nil, err
		}
		urlWithTitles = res.UrlWithTitles
	} else {
		res, err := client.AiClient.FetchHtmlTitles(ctx, &aipb.ReqFetchHtmlTitles{Urls: urls}, mclient.WithRequestTimeout(60*time.Second))
		if err != nil {
			return nil, err
		}
		urlWithTitles = res.UrlWithTitles
	}
	return urlWithTitles, nil
}

// CoverQuestionTextByAskType 根据用户提供类型覆盖问题文本
func CoverQuestionTextByAskType(question *aipb.ChatMessage, origin aipb.ChatType) string {
	var answerSlice []string

	switch origin {
	case aipb.ChatType_CHAT_TYPE_WECHAT:
		switch question.AskType {
		case aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION:
			answerSlice = config.GetStringSliceOr("workweixin.ai.answerAgainKeywords", []string{"重新回答"})
		case aipb.QuestionAskType_QUESTION_ASK_TYPE_CONTINUE:
			answerSlice = config.GetStringSliceOr("workweixin.ai.humanServiceConfirmClickNo", []string{"不，继续回答"})
		}

	case aipb.ChatType_CHAT_TYPE_WHATSAPP:
		if question.AskType == aipb.QuestionAskType_QUESTION_ASK_TYPE_REPETITION {
			answerSlice = config.GetStringSliceOr("whatsapp.ai.answerAgainKeywords", []string{"Try again"})
		}
	default:
		return question.Text
	}
	if len(answerSlice) > 0 {
		return answerSlice[0]
	}
	return question.Text
}

func GeAiDoctContributorShowInfo(ctx context.Context, contributors ...*aipb.Contributor) (
	map[*aipb.Contributor]*aipb.Contributor, error) {
	if len(contributors) == 0 {
		return map[*aipb.Contributor]*aipb.Contributor{}, nil
	}
	ret := make(map[*aipb.Contributor]*aipb.Contributor, len(contributors))
	var userIds []uint64
	var teamIds []uint64
	for _, v := range contributors {
		switch v.GetType() {
		case base.IdentityType_IDENTITY_TYPE_TEAM:
			teamIds = append(teamIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_USER:
			userIds = append(userIds, v.Id)
		case base.IdentityType_IDENTITY_TYPE_CUSTOM:
		}
		ret[v] = v
	}
	g := &errgroup.Group{}
	if len(userIds) > 0 {
		g.Go(func() error {
			names, err := logic.FetchUserName(ctx, userIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				name := names[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_USER && name != "" {
					v.Text = name
				}
			}
			return nil
		})
	}
	if len(teamIds) > 0 {
		g.Go(func() error {
			infos, err := logic.FetchTeamInfo(ctx, teamIds)
			if err != nil {
				return err
			}
			for _, v := range contributors {
				info := infos[v.Id]
				if v.Type == base.IdentityType_IDENTITY_TYPE_TEAM && info != nil {
					v.Text = info.ShortName
					v.Level = info.Level
					v.IsPublished = info.IsPublished
					v.FullName = info.FullName
				}
			}
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		return nil, err
	}
	return ret, nil
}

var (
	wechatMiniprogramUnionRegex = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
)

// IsWechatUnionID 判断是否是微信 union_id
func IsWechatUnionID(unionID string) bool {
	// 检查长度
	if len(unionID) < 26 || len(unionID) > 34 {
		return false
	}

	// 检查是否只包含字母、数字和_-
	matched := wechatMiniprogramUnionRegex.MatchString(unionID)
	if !matched {
		return false
	}

	return true
}
