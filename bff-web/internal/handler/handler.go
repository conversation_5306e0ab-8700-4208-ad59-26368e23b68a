// Package handler ...
package handler

import (
	"net/http"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/server/bff"
	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xhttp"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/ai"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/cms"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/iam"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/notify"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/review"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/search"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/support"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/tag"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/handler/team"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	bffiampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/iam"
	bffnotifypb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/notify"
	bffsearchpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/search"
	bffsupportpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/support"
	bfftagpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/tag"
	bffteampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/team"
	"github.com/asim/go-micro/v3"
)

// RegisterBFFHandler 注册BFF handler
func RegisterBFFHandler(svc micro.Service) {
	server := svc.Server().(bff.Server)

	// 初始化路由器
	router := server.BffOptions().Router
	router.SortMiddleware(middlewareSort)
	router.Middleware(maintenance)
	router.SetHooks(xhttp.Hooks{
		OnNotFound:         translateErrorResponse.WrapHandler(bff.OnNotFound(server)),
		OnMethodNotAllowed: translateErrorResponse.WrapHandler(bff.OnMethodNotAllowed(server)),
		OnPanic:            translateErrorResponse.WrapPanicHandler(OnPanic(server)),
	})

	registerBFFRoutes(server)
	registerHTTPRoutes(router)
}

// OnPanic panic handler
func OnPanic(s bff.Server) xhttp.PanicHandler {
	return func(r *xhttp.Request, v any) xhttp.Response {
		log.WithContext(r.Context()).Errorw("bff panic", "error", v, "stacktrace", xsync.TakeStackTrace(0))
		return s.BffOptions().FormatResponse(xerrors.InternalServerError(v))
	}
}

// 注册BFF路由
func registerBFFRoutes(server bff.Server) {
	// bff分组
	bffGroup := server.Group(
		xhttp.GroupMiddleware(translateErrorResponse, encodeResponseHashIds, decodeRequestHashIds, startSession),
	)
	// 认证分组
	authGroup := bffGroup.Group(xhttp.GroupMiddleware(authenticate, authorize))
	// 游客分组
	guestGroup := bffGroup.Group(xhttp.GroupMiddleware(guest))

	// iam接口
	bffiampb.RegisterIamBff(authGroup, &iam.Iam{})
	bffiampb.RegisterIamGuestBff(guestGroup, &iam.Guest{})

	// notify接口
	bffnotifypb.RegisterNotifyBff(authGroup, &notify.Notify{})
	bffnotifypb.RegisterNotifyGuestBff(guestGroup, &notify.Guest{})

	// search接口
	bffsearchpb.RegisterSearchBff(authGroup, &search.Search{})
	bffsearchpb.RegisterSearchGuestBff(guestGroup, &search.Guest{})

	// tag接口
	bfftagpb.RegisterTagGuestBff(guestGroup, &tag.Tag{})

	// team接口
	bffteampb.RegisterTeamBff(authGroup, &team.Team{})

	// support接口
	bffsupportpb.RegisterSupportGuestBff(guestGroup, &support.Guest{})

	// ai接口
	bffaipb.RegisterAiBff(authGroup, &ai.Ai{})
	bffaipb.RegisterAiGuestBff(guestGroup, &ai.Guest{})

	// 特殊中间件
	router := server.BffOptions().Router
	router.FindRoute("IamGuest.SendAuthOtp").Middleware(captchaRateLimit)
	router.FindRoute("Iam.SendTfaOtp").Middleware(captchaRateLimit)
}

// HTTP路由
func registerHTTPRoutes(router xhttp.Router) {
	// cms
	router.AddRoute(http.MethodGet, "/cms/get_help_center_menu", xhttp.HandlerFunc(cms.GetHelpCenterTree)).Middleware(startSession, guest)

	// 腾讯云回调
	router.AddRoute(http.MethodPost, "/notify/tcloud_sms_callback", xhttp.HandlerFunc(notify.HandleTcloudSmsCallback))
	router.AddRoute(http.MethodPost, "/notify/tcloud_ses_callback", xhttp.HandlerFunc(notify.HandleTcloudSesCallback))
	router.AddRoute(http.MethodPost, "/review/tcloud_cos_ci_callback", xhttp.HandlerFunc(review.HandleTcloudCosCiCallback))

	// 企微回调
	router.AddRoute(http.MethodGet, "/notify/work_weixin_callback", xhttp.HandlerFunc(notify.HandleWecomCallback))
	router.AddRoute(http.MethodPost, "/notify/work_weixin_callback", xhttp.HandlerFunc(notify.HandleWecomCallbackHandler))

	// 企微服务商数据回调-用于接收托管企业微信应用的用户消息
	router.AddRoute(http.MethodGet, "/notify/open_data_work_weixin_callback", xhttp.HandlerFunc(notify.HandleWecomDataCallback))
	router.AddRoute(http.MethodPost, "/notify/open_data_work_weixin_callback", xhttp.HandlerFunc(notify.HandleWecomDataCallbackHandler))

	// 企微服指令数据回调-系统将会把此应用的授权变更事件以及ticket参数推送给此URL
	router.AddRoute(http.MethodGet, "/notify/open_ticket_work_weixin_callback", xhttp.HandlerFunc(notify.HandleWecomDataCallback))
	router.AddRoute(http.MethodPost, "/notify/open_ticket_work_weixin_callback", xhttp.HandlerFunc(notify.HandleWecomTicketCallbackHandler))

	// whatsapp回调
	router.AddRoute(http.MethodPost, "/notify/whatsapp_callback", xhttp.HandlerFunc(notify.HandleWhatsappCallbackHandler))
	router.AddRoute(http.MethodPost, "/notify/whatsapp_business_callback", xhttp.HandlerFunc(notify.HandleWhatsappBusinessCallbackHandler))

	// 小程序知识库用户检查
	router.AddRoute(http.MethodPost, "/ai/get_mini_program_doc_user", xhttp.HandlerFunc(ai.GetMiniProgramDocUser)).Middleware(startSession, guest)
	// 小程序code换token
	router.AddRoute(http.MethodPost, "/ai/get_mini_program_code2token", xhttp.HandlerFunc(ai.GetMiniProgramCode2Token)).Middleware(startSession, guest)

	// 获取小程序webview用户绑定状态
	router.AddRoute(http.MethodPost, "/ai/get_user_bind_webview_token", xhttp.HandlerFunc(ai.GetUserBindWebviewToken)).Middleware(startSession, guest)

	// 带订阅的chat接口
	router.AddRoute(http.MethodPost, "/ai/chat_subscribe", xhttp.HandlerFunc(ai.ChatSubscribe)).Middleware(startSession, guest, decodeRequestHashIds, authenticate)
	// 重新发送chat接口
	router.AddRoute(http.MethodPost, "/ai/resend_chat_subscribe", xhttp.HandlerFunc(ai.ResendChatSubscribe)).Middleware(startSession, guest, decodeRequestHashIds, authenticate)
	// search_chat订阅接口
	router.AddRoute(http.MethodPost, "/ai/search_chat_proxy", xhttp.HandlerFunc(ai.SearchChatProxy)).Middleware(startSession, authenticate)
}
