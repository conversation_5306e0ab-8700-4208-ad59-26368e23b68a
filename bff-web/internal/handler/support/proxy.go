package support

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	bffpb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/support"
	mclient "github.com/asim/go-micro/v3/client"
)

// Proxy 代理网站
func (g Guest) Proxy(ctx context.Context, req *bffpb.ReqProxy, rsp *bffpb.RspProxy) error {
	if len(req.Urls) == 0 {
		return nil
	}
	res, err := client.AiNational.FetchHtmlTitles(ctx, &aipb.ReqFetchHtmlTitles{Urls: req.Urls}, mclient.WithRequestTimeout(60*time.Second))
	if err != nil {
		return err
	}
	for url, title := range res.UrlWithTitles {
		rsp.Contents = append(rsp.Contents, &bffpb.RspProxy_Content{Url: url, Title: title})
	}
	return nil
}
