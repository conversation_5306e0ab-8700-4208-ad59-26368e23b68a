package ai

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsession"
	"e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/client"
	ailogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/ai"
	iamlogic "e.coding.net/tencent-ssv/tanlive/services/bff-web/internal/logic/iam"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	bffaipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/bff-web/ai"
	iampb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/iam"
	mclient "github.com/asim/go-micro/v3/client"
)

var RagVisRpcTimeOut = func() time.Duration {
	d, err := time.ParseDuration(config.GetStringOr("llm.ragvis.timeout", "2m"))
	if err == nil {
		return d
	}
	return time.Second * 60 * 2
}

// ListNebulaAssistants 获取知识星云助手列表
func (a Ai) ListNebulaAssistants(ctx context.Context, req *bffaipb.ReqListNebulaAssistants, rsp *bffaipb.RspListNebulaAssistants) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	scene := aipb.ReqGetAssistants_VISIBLE_SCENE_IN_NEBULA

	pbReq := &aipb.ReqGetAssistants{
		Filter: &aipb.ReqGetAssistants_Filter{
			TeamId:  []uint64{me.FirmId},
			UserId:  []uint64{me.Id},
			IsDraft: basepb.BoolEnum_BOOL_ENUM_FALSE,
		},
		OrderBy: []*basepb.OrderBy{
			{Column: "update_date", Desc: true},
		},
		Page: &basepb.Paginator{
			Limit:  req.Limit,
			Offset: req.Offset,
		},
		Relation: &aipb.ReqGetAssistants_Relation{
			Collection: true,
		},
		WithTotalCount: true,
		VisibleScene:   scene,
	}
	if iamlogic.IsTeamUser(ctx) {
		// 团队用户
		pbReq.Filter.UserId = nil
	} else {
		// 个人用户
		pbReq.Filter.TeamId = nil
	}

	result, err := client.AiNational.GetAssistants(ctx, pbReq)
	if err != nil {
		return err
	}

	for _, v := range result.Assistants {
		rsp.Assistants = append(rsp.Assistants, &aipb.AssistantV2{
			Id: v.Assistant.Id,
			Config: &aipb.AssistantConfig{
				Name:           v.Assistant.Config.Name,
				NameEn:         v.Assistant.Config.NameEn,
				CollectionLang: v.Assistant.Config.CollectionLang,
			},
		})
	}
	rsp.TotalCount = result.TotalCount

	return nil
}

// ListNebulaContributors 获取知识星云助手下的贡献者筛选项
func (a Ai) ListNebulaContributors(ctx context.Context, req *bffaipb.ReqListNebulaContributors, rsp *bffaipb.RspListNebulaContributors) error {
	// 先校验
	aids, err := ailogic.RewriteAssistantIds(ctx)
	if err != nil {
		return err
	}
	pbReq := &aipb.ReqListContributor{
		Or: &aipb.ReqListContributor_OrGroup{
			ScopedAssistantId: aids,
		},
		WithAssistantId: true,
	}
	// 没有指定助手，查询账号下所有的贡献者
	if len(req.GetAssistantId()) == 0 {
		contributor, err := ailogic.GetUserAsContributor(ctx)
		if err != nil {
			return err
		}
		pbReq.Or.Contributor = contributor
	}

	pbRsp, err := client.AiNational.ListContributor(ctx, pbReq)
	if err != nil {
		return err
	}
	info, err := ailogic.GeAiDoctContributorShowInfo(ctx, pbRsp.Contributors...)
	if err != nil {
		return err
	}
	// 运营端贡献者合并为一条
	hasMgmt := false
	for _, v := range info {
		if v.Type == base.IdentityType_IDENTITY_TYPE_MGMT {
			if hasMgmt {
				continue
			}
			hasMgmt = true
		}
		rsp.Contributors = append(rsp.Contributors, v)
	}

	return nil
}

// CreateNebulaTask 创建知识星云任务
func (a Ai) CreateNebulaTask(ctx context.Context, req *bffaipb.ReqCreateNebulaTask, rsp *bffaipb.RspCreateNebulaTask) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	if req.Lang == "" {
		req.Lang = "bge-m3"
	}

	assistantIds := req.AssistantId
	queryAssistantIds := req.QueryAssistantId

	if len(req.AssistantId) > 0 {
		assistantIds, err := ailogic.RewriteAssistantIds(ctx, req.AssistantId...)
		if err != nil {
			log.WithContext(ctx).Errorw("CreateNebulaTask RewriteAssistantIds err", "err", err, "ids", assistantIds)
			return err
		}

	}

	if len(req.QueryAssistantId) > 0 {
		queryAssistantIds, err := ailogic.RewriteAssistantIds(ctx, req.QueryAssistantId...)
		if err != nil {
			log.WithContext(ctx).Errorw("CreateNebulaTask RewriteAssistantIds err", "err", err, "ids", queryAssistantIds)
			return err
		}
	}

	var selectAll bool

	assistants, err := ailogic.GetManagedAssistants(ctx)
	if err != nil {
		return err
	}

	// 如果用户数据为空，则获取用户可管理助手
	if len(assistantIds) == 0 && len(queryAssistantIds) == 0 && !req.HasK && !req.HasQ {
		assistantIds = assistants
		queryAssistantIds = assistants
	}

	if req.HasK && len(req.AssistantId) == 0 {
		assistantIds = assistants
		selectAll = true
	}

	if req.HasQ && len(req.QueryAssistantId) == 0 {
		queryAssistantIds = assistants
	}

	reqQuest := &aipb.ReqCreateNebulaTask{
		AssistantId:      assistantIds,
		QueryAssistantId: queryAssistantIds,
		UserId:           me.Id,
		TeamId:           me.FirmId,
		Lang:             req.Lang,
		AsTeam:           iamlogic.IsTeamUser(ctx),
		FilterText:       req.FilterText,
		SelectAll:        selectAll,

		ClusteringMethod: req.ClusteringMethod,
		MinSamplesRange:  req.MinSamplesRange,
		EpsRange:         req.EpsRange,
		NClustersRange:   req.NClustersRange,
	}

	task, err := client.AiNational.CreateNebulaTask(ctx, reqQuest)
	if err != nil {
		return err
	}

	rsp.Uuid = task.Uuid

	return nil
}

// DescribeNebulaTask 获取知识星云任务详情
func (a Ai) DescribeNebulaTask(ctx context.Context, req *bffaipb.ReqDescribeNebulaTask, rsp *bffaipb.RspDescribeNebulaTask) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	if req.Lang == "" {
		req.Lang = "bge-m3"
	}

	if len(req.AssistantId) > 0 {
		assistantIds, err := ailogic.RewriteAssistantIds(ctx, req.AssistantId...)
		if err != nil {
			log.WithContext(ctx).Errorw("CreateNebulaTask RewriteAssistantIds err", "err", err, "ids", assistantIds)
			return err
		}

	}

	if len(req.QueryAssistantId) > 0 {
		queryAssistantIds, err := ailogic.RewriteAssistantIds(ctx, req.QueryAssistantId...)
		if err != nil {
			log.WithContext(ctx).Errorw("CreateNebulaTask RewriteAssistantIds err", "err", err, "ids", queryAssistantIds)
			return err
		}

	}

	assistantIds := req.AssistantId
	queryAssistantIds := req.QueryAssistantId

	task, err := client.AiNational.DescribeNebulaTask(ctx, &aipb.ReqDescribeNebulaTask{
		Uuid:             req.Uuid,
		AsTeam:           iamlogic.IsTeamUser(ctx),
		AssistantId:      assistantIds,
		QueryAssistantId: queryAssistantIds,
		UserId:           me.Id,
		TeamId:           me.FirmId,
		Lang:             req.Lang,
	})
	if err != nil {
		return err
	}

	rsp.State = task.State
	rsp.StartDate = task.CreateDate
	rsp.EndDate = task.EndDate
	rsp.CalcuResult = task.CalcuResult
	rsp.Uuid = task.Uuid
	rsp.FilterText = task.FilterText
	rsp.ConnectInfo = task.ConnectInfo
	rsp.ClusterList = task.ClusterList

	return nil
}

// DescribeNebulaTaskList 获取知识星云任务列表
func (a Ai) DescribeNebulaTaskList(ctx context.Context, req *bffaipb.ReqDescribeNebulaTaskList, rsp *bffaipb.RspDescribeNebulaTaskList) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	tasks, err := client.AiNational.DescribeNebulaTaskList(ctx, &aipb.ReqDescribeNebulaTaskList{
		Uuid:   req.Uuid,
		UserId: me.Id,
		TeamId: me.FirmId,
		AsTeam: iamlogic.IsTeamUser(ctx),
		Page: &basepb.Paginator{
			Limit:  req.Limit,
			Offset: req.Offset,
		},
	})
	if err != nil {
		return err
	}

	for _, v := range tasks.Tasks {
		rsp.Tasks = append(rsp.Tasks, &bffaipb.RspDescribeNebulaTaskList_Task{
			Uuid:       v.Uuid,
			State:      v.State,
			Lang:       v.Lang,
			StartDate:  v.CreateDate,
			EndDate:    v.EndDate,
			FilterText: v.FilterText,
			IsRead:     v.IsRead,
		})
	}

	rsp.TotalCount = tasks.Total
	rsp.UnreadNum = tasks.UnreadNum
	return nil
}

// DescribeNebulaProjection 获取知识星云任务向量
func (a Ai) DescribeNebulaProjection(ctx context.Context, req *bffaipb.ReqDescribeNebulaProjection,
	rsp *bffaipb.RspDescribeNebulaProjection,
) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)
	projection, err := client.AiNational.DescribeNebulaProjection(ctx, &aipb.ReqDescribeNebulaProjection{
		Uuid:  req.Uuid,
		Query: req.Query, UserId: me.Id, TeamId: me.FirmId,
	}, mclient.WithRequestTimeout(RagVisRpcTimeOut()))
	if err != nil {
		return err
	}

	rsp.Projection = projection.Projection

	return nil
}

// DescribeNebulaData 查询知识星云数据
func (a Ai) DescribeNebulaData(ctx context.Context, req *bffaipb.ReqDescribeNebulaData,
	rsp *bffaipb.RspDescribeNebulaData,
) error {
	me := xsession.UserFromContext[iampb.UserInfo](ctx)

	data, err := client.AiNational.DescribeNebulaData(ctx, &aipb.ReqDescribeNebulaData{
		ContentHash: req.ContentHash, Id: req.Id, UserId: me.Id, TeamId: me.FirmId,
	},
		mclient.WithRequestTimeout(RagVisRpcTimeOut()))
	if err != nil {
		return err
	}

	rsp.Content = data.Content
	rsp.QueryName = data.QueryName

	return nil
}
