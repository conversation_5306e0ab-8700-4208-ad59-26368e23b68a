{"consumes": ["application/json"], "produces": ["application/json"], "swagger": "2.0", "info": {"title": "tanlive/bff-mgmt/mgmt/bff.proto", "version": "version not set"}, "paths": {"/ai/accept_feedback": {"post": {"tags": ["AiBff"], "summary": "采用用户反馈", "operationId": "AiBff_AcceptFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqAcceptFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAcceptFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/auto_chunk_doc": {"post": {"tags": ["AiBff"], "summary": "自动文档分段", "operationId": "AiBff_AutoChunkDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqAutoChunkDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspAutoChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/batch_create_assistant": {"post": {"tags": ["AiBff"], "summary": "批量创建助手", "operationId": "AiBff_BatchCreateAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchCreateAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchCreateAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/batch_update_assistant": {"post": {"tags": ["AiBff"], "summary": "批量更新助手", "operationId": "AiBff_BatchUpdateAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUpdateAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/batch_update_docs": {"post": {"tags": ["AiBff"], "summary": "批量更新doc的特定字段值", "operationId": "AiBff_BatchUpdateDocAttr", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqBatchUpdateDocAttr"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspBatchUpdateDocAttr"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/clone_doc": {"post": {"tags": ["AiBff"], "summary": "克隆QA/文本/文件", "operationId": "AiBff_CloneDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCloneDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCloneDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_doc_query": {"post": {"tags": ["AiBff"], "summary": "创建doc查询", "operationId": "AiBff_CreateDocQuery", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateDocQuery"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocQuery"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_qas": {"post": {"tags": ["AiBff"], "summary": "创建QA", "operationId": "AiBff_CreateQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_system_doc_copy": {"post": {"tags": ["AiBff"], "summary": "创建系统文档副本（人工修改）", "operationId": "AiBff_CreateSystemDocCopy", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateSystemDocCopy"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateSystemDocCopy"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/create_text_files": {"post": {"tags": ["AiBff"], "summary": "创建文本或文件", "operationId": "AiBff_CreateTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/delete_docs": {"post": {"tags": ["AiBff"], "summary": "删除Doc，包括QA，文本或文件", "operationId": "AiBff_DeleteQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteDocs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDeleteDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/delete_system_doc": {"post": {"tags": ["AiBff"], "summary": "删除系统文档", "operationId": "AiBff_DeleteSystemDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteSystemDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDeleteSystemDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/disable_system_doc": {"post": {"tags": ["AiBff"], "summary": "停用系统文档", "operationId": "AiBff_DisableSystemDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDisableSystemDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDisableSystemDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/enable_system_doc": {"post": {"tags": ["AiBff"], "summary": "启用系统文档", "operationId": "AiBff_EnableSystemDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqEnableSystemDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspEnableSystemDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/get_qa_tip": {"post": {"tags": ["AiBff"], "summary": "查询QA的知识提示（问题超长，内容重复）等信息", "operationId": "AiBff_GetQaTip", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetQaTip"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetQaTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/get_text_file": {"post": {"tags": ["AiBff"], "summary": "id查询文本/文件详情", "operationId": "AiBff_GetTextFile", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetTextFile"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetTextFile"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/get_text_file_tip": {"post": {"tags": ["AiBff"], "summary": "查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息", "operationId": "AiBff_GetTextFileTip", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetTextFileTip"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetTextFileTip"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/import_qas": {"post": {"tags": ["AiBff"], "summary": "导入文本/文件", "operationId": "AiBff_ImportQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqImportQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspImportQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_assistant": {"post": {"tags": ["AiBff"], "summary": "查询ai助手列表", "operationId": "AiBff_ListAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_collection": {"post": {"tags": ["AiBff"], "summary": "查询collection用户端列表", "operationId": "AiBff_ListCollection", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCollection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_contributor": {"post": {"tags": ["AiBff"], "summary": "查询贡献者列表", "operationId": "AiBff_ListContributor", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListContributor"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListContributor"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_doc_by_ref": {"post": {"tags": ["AiBff"], "summary": "根据文档ref_id查询文档", "operationId": "AiBff_ListDocByRef", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListDocByRef"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListDocByRef"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_filename": {"post": {"tags": ["AiBff"], "summary": "查询文件列表", "operationId": "AiBff_ListCollectionFileName", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListCollectionFileName"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCollectionFileName"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_operator": {"post": {"tags": ["AiBff"], "summary": "查询更新人列表", "operationId": "AiBff_ListOperator", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListOperator"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListOperator"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_qa": {"post": {"tags": ["AiBff"], "summary": "查询QA列表", "operationId": "AiBff_ListQA", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListQA"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListQA"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/list_text_files": {"post": {"tags": ["AiBff"], "summary": "查询文本或文件列表", "operationId": "AiBff_ListTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/onoff_docs": {"post": {"tags": ["AiBff"], "summary": "启用/禁用doc", "operationId": "AiBff_OnOffDocs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqOnOffDocs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspOnOffDocs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/reparse_text_files": {"post": {"tags": ["AiBff"], "summary": "重新解析文件", "operationId": "AiBff_ReparseTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqReparseTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspReparseTextFiles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/search_collection": {"post": {"tags": ["AiBff"], "summary": "collection向量查询", "operationId": "AiBff_SearchCollection", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchCollection"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchCollection"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/update_qas": {"post": {"tags": ["AiBff"], "summary": "更新QA", "operationId": "AiBff_UpdateQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/update_text_files": {"post": {"tags": ["AiBff"], "summary": "更新文本或文件", "operationId": "AiBff_UpdateTextFiles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateTextFiles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/collection/validate_qas": {"post": {"tags": ["AiBff"], "summary": "校验待创建QA", "operationId": "AiBff_ValidateQAs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqValidateQAs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspValidateQAs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_assistant": {"post": {"tags": ["AiBff"], "summary": "创建助手", "operationId": "AiBff_CreateAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateAssistant"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_assistant_sender": {"post": {"tags": ["AiBff"], "summary": "创建助手发送方设置", "operationId": "AiBff_CreateDocShareConfigSender", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateDocShareConfigSender"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/create_assistant_share": {"post": {"tags": ["AiBff"], "summary": "创建文档分享（支持助手、个人、团队）", "operationId": "AiBff_CreateAssistantShare", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqCreateAssistantShare"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateDocShare"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/delete_assistant": {"post": {"tags": ["AiBff"], "summary": "删除助手", "operationId": "AiBff_DeleteAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/delete_custom_labels": {"post": {"tags": ["AiBff"], "summary": "删除自定义标签", "operationId": "AiBff_DeleteCustomLabels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDeleteCustomLabels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_chat_region_code": {"post": {"tags": ["AiBff"], "summary": "获取所有会话的地区编码", "operationId": "AiBff_DescribeChatRegionCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChatRegionCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChatRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_chat_suggest_log": {"post": {"tags": ["AiBff"], "summary": "获取建议问题日志", "operationId": "AiBff_DescribeChatSuggestLog", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeChatSuggestLog"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeChatSuggestLog"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_export_tasks": {"post": {"tags": ["AiBff"], "summary": "查询导出任务列表", "operationId": "AiBff_DescribeExportTasks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeExportTasks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeExportTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/describe_feedback_region_code": {"post": {"tags": ["AiBff"], "summary": "获取所有教学反馈的地区编码", "operationId": "AiBff_DescribeFeedbackRegionCode", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqDescribeFeedbackRegionCode"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspDescribeFeedbackRegionCode"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/find_feedback": {"post": {"tags": ["AiBff"], "summary": "查询用户反馈详情", "operationId": "AiBff_FindFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqFindFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspFindFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistant_logs_page": {"post": {"tags": ["AiBff"], "summary": "查询助手日志分页列表", "operationId": "AiBff_GetAssistantLogsPage", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantLogsPage"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantLogsPage"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistant_options": {"post": {"tags": ["AiBff"], "summary": "获取助手下拉选项", "operationId": "AiBff_GetAssistantOptions", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_assistants_page": {"post": {"tags": ["AiBff"], "summary": "查询助手分页列表", "operationId": "AiBff_GetAssistantsPage", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetAssistantsPage"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetAssistantsPage"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_chat_detail": {"post": {"tags": ["AiBff"], "summary": "AI对话详情", "operationId": "AiBff_GetChatDetail", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChatDetail"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChatDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_chat_message_detail": {"post": {"tags": ["AiBff"], "summary": "获取消息详情", "operationId": "AiBff_GetChatMessageDetail", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChatMessageDetail"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChatMessageDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_chunk_doc_tasks": {"post": {"tags": ["AiBff"], "summary": "查询文档分段任务列表", "operationId": "AiBff_GetChunkDocTasks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetChunkDocTasks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetChunkDocTasks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_custom_labels": {"post": {"tags": ["AiBff"], "summary": "获取自定义标签列表", "operationId": "AiBff_ListCustomLabel", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListCustomLabel"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListCustomLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_doc_chunks": {"post": {"tags": ["AiBff"], "summary": "查询文档分段信息", "operationId": "AiBff_GetDocChunks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetDocChunks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetDocChunks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_doc_embedding_models": {"post": {"tags": ["AiBff"], "summary": "查询文档的向量化模型", "operationId": "AiBff_GetDocEmbeddingModels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetDocEmbeddingModels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetDocEmbeddingModels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_feedback_logs": {"post": {"tags": ["AiBff"], "summary": "查询用户反馈日志列表", "operationId": "AiBff_GetFeedbackLogs", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetFeedbackLogs"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetFeedbackLogs"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/get_feedbacks": {"post": {"tags": ["AiBff"], "summary": "查询用户反馈列表", "operationId": "AiBff_GetFeedbacks", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqGetFeedbacks"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspGetFeedbacks"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_assistant_sender": {"post": {"tags": ["AiBff"], "summary": "查询助手发送方设置", "operationId": "AiBff_ListeDocShareConfigSender", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListeDocShareConfigSender"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListeDocShareConfigSender"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_chat": {"post": {"tags": ["AiBff"], "summary": "AI对话管理列表", "operationId": "AiBff_ListChat", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListChat"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_chat_live_agent": {"post": {"tags": ["AiBff"], "summary": "获取人工坐席列表", "operationId": "AiBff_ListChatLiveAgent", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListChatLiveAgent"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/list_shared_assistant": {"post": {"tags": ["AiBff"], "summary": "查询可分享的助手列表", "operationId": "AiBff_ListAssistantCanShareDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqListAssistantCanShareDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspListAssistantCanShareDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/manual_chunk_doc": {"post": {"tags": ["AiBff"], "summary": "手动文档分段", "operationId": "AiBff_ManualChunkDoc", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqManualChunkDoc"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspManualChunkDoc"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/modify_custom_labels": {"post": {"tags": ["AiBff"], "summary": "插入或更新自定义标签", "operationId": "AiBff_ModifyCustomLabels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqModifyCustomLabels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspModifyCustomLabels"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/proxy_chat_url": {"post": {"tags": ["AiBff"], "summary": "获取助手url网页title", "operationId": "AiBff_ProxyChatHtmlUrl", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqProxyChatHtmlUrl"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspProxyChatHtmlUrl"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/search_chat": {"post": {"tags": ["AiBff"], "summary": "搜索chat", "operationId": "AiBff_SearchChat", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSearchChat"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSearchChat"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/switch_chat_live_agent": {"post": {"tags": ["AiBff"], "summary": "切换人工坐席", "operationId": "AiBff_SwitchChatLiveAgent", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSwitchChatLiveAgent"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspSwitchChatLiveAgent"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/sync_fix_message_collection": {"post": {"tags": ["AiBff"], "summary": "修复message collection", "operationId": "AiBff_SyncFixChatMessageCollection", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqSyncFixChatMessageCollection"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/update_assistant": {"post": {"tags": ["AiBff"], "summary": "更新助手", "operationId": "AiBff_UpdateAssistant", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateAssistant"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/update_assistant_notice_conf": {"post": {"tags": ["AiBff"], "summary": "更新助手的横幅信息", "operationId": "AiBff_UpdateAssistantNoticeConf", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateChatNoticeConf"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/update_object_custom_labels": {"post": {"tags": ["AiBff"], "summary": "更新对象的自定义标签", "operationId": "AiBff_UpdateObjectCustomLabels", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpdateObjectCustomLabels"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/ai/upsert_mgmt_feedback": {"post": {"tags": ["AiBff"], "summary": "创建/更新碳LIVE反馈", "operationId": "AiBff_UpsertMgmtFeedback", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/aiReqUpsertMgmtFeedback"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/aiRspCreateFeedback"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/graph/create_process_engine": {"post": {"tags": ["GraphBff"], "summary": "流程引擎添加", "operationId": "GraphBff_CreateProcessEngine", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqCreateProcessEngine"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/graph/delete_process_engine": {"post": {"tags": ["GraphBff"], "summary": "流程引擎删除", "operationId": "GraphBff_DeleteProcessEngine", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqDeleteProcessEngine"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/graph/describe_process_list": {"post": {"tags": ["GraphBff"], "summary": "流程配置列表", "operationId": "GraphBff_DescribeListProcess", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqDescribeListProcess"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/graphRspDescribeListProcess"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/graph/modify_enable_version": {"post": {"tags": ["GraphBff"], "summary": "版本启用", "operationId": "GraphBff_ModifyEnableVersion", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqModifyEnableVersion"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/graph/modify_process_engine": {"post": {"tags": ["GraphBff"], "summary": "流程引擎编辑", "operationId": "GraphBff_ModifyProcessEngine", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/graphReqModifyProcessEngine"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/disable_user": {"post": {"tags": ["IamBff"], "summary": "冻结用户", "operationId": "IamBff_DisableUser", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqDisableUser"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspDisableUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/iam/enable_user": {"post": {"tags": ["IamBff"], "summary": "解冻用户", "operationId": "IamBff_EnableUser", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/iamReqEnableUser"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/iamRspEnableUser"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/mgmt/search_users": {"post": {"tags": ["MgmtBff"], "summary": "搜索运营端用户列表", "operationId": "MgmtBff_SearchUsers", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqSearchUsers"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspSearchUsers"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/op/login": {"post": {"tags": ["Au<PERSON><PERSON><PERSON>"], "summary": "登录", "operationId": "AuthBff_Login", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqLogin"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspLogin"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/op/logout": {"post": {"tags": ["Au<PERSON><PERSON><PERSON>"], "summary": "登出", "operationId": "AuthBff_Logout", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/op/role_get": {"post": {"tags": ["MgmtBff"], "summary": "查询角色列表", "operationId": "MgmtBff_GetRoles", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqGetRoles"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspGetRoles"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/op/text_translate": {"post": {"tags": ["MgmtBff"], "summary": "翻译", "operationId": "MgmtBff_TextTranslate", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgmtReqTextTranslate"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgmtRspTextTranslate"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/describe_product_atlas_search_option": {"post": {"tags": ["SearchBff"], "summary": "查询产品-图谱搜索筛选项", "operationId": "SearchBff_DescribeProductAtlasSearchOptions", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeTeamAtlasSearchOptions"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeTeamAtlasSearchOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/describe_search_prompt": {"post": {"tags": ["SearchBff"], "summary": "查询修改搜索提示语", "operationId": "SearchBff_DescribeSearchPrompts", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeSearchPrompts"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeSearchPrompts"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/describe_team_atlas_search_option": {"post": {"tags": ["SearchBff"], "summary": "查询团队-图谱搜索筛选项", "operationId": "SearchBff_DescribeTeamAtlasSearchOptions", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqDescribeTeamAtlasSearchOptions"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/searchRspDescribeTeamAtlasSearchOptions"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/modify_product_atlas_search_option": {"post": {"tags": ["SearchBff"], "summary": "修改产品-图谱搜索筛选项", "operationId": "SearchBff_ModifyProductAtlasSearchOption", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqModifyTeamAtlasSearchOption"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/modify_search_prompt": {"post": {"tags": ["SearchBff"], "summary": "修改搜索提示语", "operationId": "SearchBff_ModifySearchPrompt", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqModifySearchPrompt"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/search/modify_team_atlas_search_option": {"post": {"tags": ["SearchBff"], "summary": "修改团队-图谱搜索筛选项", "operationId": "SearchBff_ModifyTeamAtlasSearchOption", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/searchReqModifyTeamAtlasSearchOption"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/support/get_hash_ids": {"post": {"tags": ["SupportBff"], "operationId": "SupportBff_GetHashIds", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supportReqGetHashIds"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supportRspGetHashIds"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/support/proxy": {"post": {"tags": ["SupportBff"], "summary": "代理请求访问网页", "operationId": "SupportBff_Proxy", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supportReqProxy"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supportRspProxy"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/batch_import_system_tag": {"post": {"tags": ["TagBff"], "summary": "批量导入标签", "operationId": "TagBff_BatchImportSystemTag", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqBatchImportSystemTag"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/create_personal_tag_binding": {"post": {"tags": ["TagBff"], "summary": "创建个人用户标签绑定", "operationId": "TagBff_CreatePersonalTagBinding", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqCreatePersonalTagBinding"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/create_team_user_tag_binding": {"post": {"tags": ["TagBff"], "summary": "创建团队个人用户标签绑定", "operationId": "TagBff_CreateTeamUserTagBinding", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqCreateTeamUserTagBinding"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/delete_system_tag": {"post": {"tags": ["TagBff"], "summary": "删除系统标签", "operationId": "TagBff_DeleteSystemTag", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDeleteSystemTag"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/describe_assistant_tag_binding_infos": {"post": {"tags": ["TagBff"], "summary": "查询助手-用户自动打标标签关联详情", "operationId": "TagBff_DescribeAssistantTagBindingInfos", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeAssistantTagBindingInfos"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeAssistantTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/describe_atlas_tag_binding_infos": {"post": {"tags": ["TagBff"], "summary": "查询图谱标签关联详情", "operationId": "TagBff_DescribeAtlasTagBindingInfos", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeAtlasTagBindingInfos"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeAtlasTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/describe_product_tag_binding_infos": {"post": {"tags": ["TagBff"], "summary": "查询产品标签关联详情", "operationId": "TagBff_DescribeProductTagBindingInfos", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeProductTagBindingInfos"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeProductTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/describe_resource_tag_binding_infos": {"post": {"tags": ["TagBff"], "summary": "查询资源标签关联详情", "operationId": "TagBff_DescribeResourceTagBindingInfos", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeResourceTagBindingInfos"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeResourceTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/describe_system_tags_by_index": {"post": {"tags": ["TagBff"], "summary": "获取系统索引标签列表", "operationId": "TagBff_DescribeSystemTagsByIndex", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeSystemTagsByIndex"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeSystemTagsByIndex"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/describe_team_tag_binding_infos": {"post": {"tags": ["TagBff"], "summary": "查询团队标签关联详情", "operationId": "TagBff_DescribeTeamTagBindingInfos", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqDescribeTeamTagBindingInfos"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspDescribeTeamTagBindingInfos"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/edit_system_tag": {"post": {"tags": ["TagBff"], "summary": "编辑系统标签", "operationId": "TagBff_EditSystemTag", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqEditSystemTag"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/get_notify_data_by_user_label": {"post": {"tags": ["TagBff"], "summary": "获取被用户标签绑定的数据信息", "operationId": "TagBff_GetNotifyDataByUserLabel", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqGetNotifyDataByUserLabel"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspGetNotifyDataByUserLabel"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/get_system_tags": {"post": {"tags": ["TagBff"], "summary": "获取全量系统标签", "operationId": "TagBff_GetSystemTags", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqGetSystemTags"}}], "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/tagRspGetSystemTags"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/merge_system_tags": {"post": {"tags": ["TagBff"], "summary": "合并系统标签", "operationId": "TagBff_MergeSystemTags", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqMergeSystemTags"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}, "/tag/update_system_tag": {"post": {"tags": ["TagBff"], "summary": "更新系统标签", "operationId": "TagBff_UpdateSystemTag", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/tagReqUpdateSystemTag"}}], "responses": {"200": {"description": "A successful response.", "schema": {"type": "object"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}}}}, "definitions": {"AiAssistantNoticeConfNotice": {"type": "object", "properties": {"en": {"type": "string"}, "zh": {"type": "string"}}}, "AssistantKefuReplyCustom": {"type": "object", "properties": {"text": {"type": "string", "title": "显示文本"}, "url": {"type": "string", "title": "url地址"}}}, "AssistantKefuReplyReply": {"type": "object", "properties": {"img_url": {"type": "string", "title": "图片地址"}, "reply_message": {"type": "string", "title": "回复消息"}}}, "ChatLiveAgentInfoLiveAgentInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "nickname": {"type": "string"}}}, "FeedbackCommentFile": {"type": "object", "title": "文件", "properties": {"file_name": {"type": "string", "title": "文件名称"}, "file_path": {"type": "string", "title": "路径"}}}, "ReqDescribeTeamAtlasSearchOptionsFilter": {"type": "object", "properties": {"language": {"type": "string", "title": "语言类型zh、en"}, "status": {"$ref": "#/definitions/baseDisableState"}}}, "ReqListChatFilter": {"type": "object", "properties": {"chat_titles": {"type": "array", "title": "对话内容", "items": {"type": "string"}}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务 1 否 2 是"}, "nicknames": {"type": "array", "items": {"type": "string"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "region_codes": {"type": "array", "title": "国家或地区编码", "items": {"type": "string"}}, "reject_job_result": {"type": "integer", "format": "int64", "title": "筛选审核 1 违规 2 敏感 3 正常"}, "user_ids": {"type": "array", "title": "用户id", "items": {"type": "string", "format": "uint64"}}}}, "ReqListTextFilesSearch": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件名搜索"}, "text": {"type": "string", "title": "文本内容搜索"}, "ugc_title": {"type": "string"}}}, "RspDescribeAssistantTagBindingInfosInfo": {"type": "object", "properties": {"channel": {"title": "渠道", "$ref": "#/definitions/aiAssistantChannel"}, "create_by": {"title": "创建人", "$ref": "#/definitions/baseIdentity"}, "create_date": {"type": "string", "format": "date-time"}, "enabled": {"type": "boolean", "title": "是否启用"}, "id": {"type": "string", "format": "uint64"}, "is_draft": {"type": "boolean", "title": "是否草稿"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "RspDescribeTeamAtlasSearchOptionsTeamSearchOption": {"type": "object", "properties": {"atlas_bind_num": {"type": "integer", "format": "int64"}, "atlas_deleted": {"type": "boolean"}, "atlas_draft_id": {"type": "string", "format": "uint64"}, "atlas_name": {"type": "string"}, "atlas_state": {"$ref": "#/definitions/baseUgcState"}, "option": {"$ref": "#/definitions/searchSearchOption"}}}, "RspListDocByRefDoc": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "file_name": {"type": "string"}, "question": {"type": "string"}, "ref": {"type": "string"}, "text": {"type": "string"}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "url": {"type": "string"}}}, "RspLoginUserInfo": {"type": "object", "properties": {"remark_name": {"type": "string", "title": "备注名"}, "user_id": {"type": "string", "format": "uint64", "title": "用户ID"}, "user_name": {"type": "string", "title": "用户名"}}}, "RspOnOffDocsQaContainsMatchCount": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "cnt": {"type": "string", "format": "uint64"}}}, "RspOnOffDocsRepeatCollection": {"type": "object", "properties": {"file_name": {"type": "object", "additionalProperties": {"type": "string"}}, "id": {"type": "string", "format": "uint64"}}}, "RspProxyChatHtmlUrlContent": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}}}, "RspProxyContent": {"type": "object", "properties": {"title": {"type": "string"}, "url": {"type": "string"}}}, "RspValidateQAsErr": {"type": "object", "properties": {"code": {"$ref": "#/definitions/errorsAiError"}, "id": {"type": "string", "format": "uint64"}, "message": {"type": "string"}}}, "aiAiAssistantNoticeConf": {"type": "object", "title": "AiAssistantNoticeConf ai助手横幅提示配置", "properties": {"channel": {"type": "array", "items": {"$ref": "#/definitions/aiAssistantChannel"}}, "enable": {"type": "boolean", "title": "是否启用"}, "notice": {"$ref": "#/definitions/AiAssistantNoticeConfNotice"}, "range_time": {"$ref": "#/definitions/baseTimeRange"}}}, "aiAiRecordType": {"type": "integer", "format": "int32", "title": "- 1: 用户咨询\n - 2: 助手回答文本消息\n - 3: 助手回答菜单消息\n - 4: 助手回答建议问题菜单消息\n - 5: 助手回答贡献知识的小程序\n - 6: 发送转人工菜单信息\n - 7: 助手回答图片\n - 8: 助手回答音频\n - 9: 助手回答视频\n - 10: 助手回答文件", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}, "aiAskSuggestionMode": {"description": "- 1: 模式1：根据历史问答生成问题建议\n - 2: 模式2：根据历史问答生成，并仅显示知识库中有相关知识的问题建议\n - 3: 模式3：根据问题在知识库中已命中的知识，生成问题建议\n - 4: 模式4：根据问题在知识库中尚未命中但排名靠前的知识，生成问题建议", "type": "integer", "format": "int32", "title": "问题建议模式", "enum": [1, 2, 3, 4]}, "aiAssistant": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollection"}}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "name_en": {"type": "string"}, "search_debug": {"type": "boolean"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "关键词召回匹配目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "text_weight": {"type": "number", "format": "float"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int32"}, "website_route": {"type": "string"}}}, "aiAssistantAction": {"description": "- 1: 创建\n - 2: 保存草稿\n - 3: 发布\n - 4: 删除", "type": "integer", "format": "int32", "title": "助手操作类型", "enum": [1, 2, 3, 4]}, "aiAssistantAllowlistConfig": {"type": "object", "title": "助手白名单配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "phones": {"type": "array", "title": "手机列表", "items": {"type": "string"}}, "type": {"title": "白名单类型", "$ref": "#/definitions/aiAssistantAllowlistType"}}}, "aiAssistantAllowlistType": {"description": "- 1: 手机号码\n - 2: 微信昵称", "type": "integer", "format": "int32", "title": "助手白名单类型", "enum": [1, 2]}, "aiAssistantAskSuggestionConfig": {"type": "object", "title": "问题建议配置", "properties": {"count": {"type": "integer", "format": "int32", "title": "问题建议数量"}, "enabled": {"type": "boolean", "title": "是否启用"}, "mode": {"title": "问题建议模式", "$ref": "#/definitions/aiAskSuggestionMode"}, "model": {"type": "string", "title": "模型"}, "prompt": {"type": "string", "title": "提示词"}, "times": {"type": "integer", "format": "int64", "title": "问题建议倍数"}}}, "aiAssistantChanges": {"type": "object", "title": "助手字段变化", "properties": {"fields": {"type": "array", "title": "变动字段", "items": {"type": "string"}}, "new": {"title": "变化后的配置", "$ref": "#/definitions/aiAssistantConfig"}, "old": {"title": "变化前的配置", "$ref": "#/definitions/aiAssistantConfig"}}}, "aiAssistantChannel": {"description": "- 1: 碳LIVE-微信\n - 2: 碳LIVE-Web\n - 3: 碳LIVE-应用\n - 4: 碳LIVE-WhatsApp\n - 5: 第三方机构-微信\n - 6: 碳LIVE-小程序", "type": "integer", "format": "int32", "title": "助手渠道", "enum": [1, 2, 3, 4, 5, 6]}, "aiAssistantChatOrSqlConfig": {"type": "object", "title": "ChatOrSql配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}}, "aiAssistantChunkConfig": {"type": "object", "title": "分段配置", "properties": {"max_char_count": {"type": "integer", "format": "int32", "title": "最大字符数"}, "max_char_lang": {"type": "string", "title": "最大字符语言"}, "min_char_count": {"type": "integer", "format": "int32", "title": "最小字符数"}, "min_char_lang": {"type": "string", "title": "最小字符语言"}, "overlap_count": {"type": "integer", "format": "int32", "title": "重合字符数"}, "overlap_lang": {"type": "string", "title": "重合字符语言"}}}, "aiAssistantChunks": {"type": "object", "title": "助手的分段信息", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "chunks": {"type": "array", "title": "分段列表", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}}}}, "aiAssistantConfig": {"type": "object", "title": "助手配置", "properties": {"admins": {"type": "array", "title": "助手管理员", "items": {"type": "object", "$ref": "#/definitions/baseIdentity"}}, "allowlist_config": {"title": "白名单配置", "$ref": "#/definitions/aiAssistantAllowlistConfig"}, "ask_suggestion_config": {"title": "问题建议配置", "$ref": "#/definitions/aiAssistantAskSuggestionConfig"}, "brief_intro": {"type": "string", "title": "一句话介绍"}, "channel": {"title": "渠道", "$ref": "#/definitions/aiAssistantChannel"}, "chat_or_sql_config": {"title": "ChatOrSql配置", "$ref": "#/definitions/aiAssistantChatOrSqlConfig"}, "chunk_config": {"title": "分段配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "clean_chunks": {"type": "boolean", "title": "自动过滤"}, "close_search": {"type": "boolean", "title": "是否关闭搜索增强"}, "collection_lang": {"type": "string", "title": "知识库语言"}, "collection_name": {"type": "string", "title": "知识库名称（自动生成）"}, "detail_intro": {"type": "string", "title": "助手介绍"}, "doc_top_n": {"type": "integer", "format": "int32", "title": "知识库Top_N"}, "enabled": {"type": "boolean", "title": "是否启用"}, "field_manage_config": {"title": "参数管理权限", "$ref": "#/definitions/aiAssistantFieldManageConfig"}, "history_rounds": {"type": "integer", "format": "int32", "title": "对话轮数"}, "miniprogram_channel_config": {"title": "小程序渠道配置", "$ref": "#/definitions/aiAssistantMiniprogramChannelConfig"}, "miss_reply": {"type": "string", "title": "未命中知识库自动回复"}, "model": {"type": "string", "title": "对话模型"}, "name": {"type": "string", "title": "AI助手名"}, "name_en": {"type": "string", "title": "AI助手名（英文）"}, "prompt_prefix": {"type": "string", "title": "提示词"}, "question_type_config": {"title": "问题分类配置", "$ref": "#/definitions/aiAssistantQuestionTypeConfig"}, "search_debug": {"type": "boolean", "title": "搜索测试"}, "search_engine": {"type": "string", "title": "搜索引擎"}, "search_top_n": {"type": "integer", "format": "int32", "title": "互联网Top_N"}, "show_in_list": {"type": "boolean", "title": "是否在助手列表展示"}, "show_think": {"type": "boolean", "title": "是否展示思考过程"}, "tanlive_app_channel_config": {"title": "Web渠道配置", "$ref": "#/definitions/aiAssistantTanliveAppChannelConfig"}, "tanlive_web_channel_config": {"title": "碳LIVE Web渠道配置", "$ref": "#/definitions/aiAssistantTanliveWebChannelConfig"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "QA关键词召回目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "text_weight": {"type": "number", "format": "float", "title": "关键词搜索权重"}, "threshold": {"type": "number", "format": "float", "title": "对话阈值"}, "use_region_code": {"type": "string", "title": "使用地区（空不限制）"}, "user_label_config": {"title": "助手用户打标配置", "$ref": "#/definitions/aiAssistantUserLabelConfig"}, "visible_chain_config": {"title": "链路查询", "$ref": "#/definitions/aiAssistantVisibleChainConfig"}, "weixin_channel_config": {"title": "微信渠道配置", "$ref": "#/definitions/aiAssistantWeixinChannelConfig"}, "whatsapp_channel_config": {"title": "WhatsApp渠道配置", "$ref": "#/definitions/aiAssistantWhatsappChannelConfig"}}}, "aiAssistantFieldManageConfig": {"type": "object", "title": "字段管理配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用（该字段暂不使用）"}, "readable": {"type": "array", "title": "可读字段（为空全部可读）", "items": {"type": "string"}}, "writable": {"type": "array", "title": "可写字段（为空全部不可写）", "items": {"type": "string"}}}}, "aiAssistantGraphParseConfig": {"type": "object", "title": "图谱解析配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "model": {"type": "string", "title": "模型"}}}, "aiAssistantInteractiveCode": {"type": "object", "title": "互动暗号详情", "properties": {"content": {"type": "string", "title": "内容"}, "interactive_code": {"title": "互动暗号", "$ref": "#/definitions/aiInteractiveCode"}, "lang": {"type": "string", "title": "语言"}, "show_in_welcome": {"type": "boolean", "title": "是否在欢迎语里显示"}}}, "aiAssistantInteractiveCodeConfig": {"type": "object", "title": "互动暗号配置", "properties": {"codes": {"type": "array", "title": "暗号配置", "items": {"type": "object", "$ref": "#/definitions/aiAssistantInteractiveCode"}}, "send_interactive_code": {"type": "boolean", "title": "开启回复发送暗号"}}}, "aiAssistantKefuConfig": {"type": "object", "title": "人工客服配置", "properties": {"after_remind_message": {"type": "string", "title": "转人工后的提示语"}, "before_remind_enabled": {"type": "boolean", "title": "是否启用转人工前的提示语"}, "before_remind_message": {"type": "string", "title": "转人工前的提示语"}, "enabled": {"type": "boolean", "title": "是否启用"}, "reply": {"title": "客服自动回复配置", "$ref": "#/definitions/aiAssistantKefuReply"}, "staffs": {"type": "array", "title": "员工信息", "items": {"type": "object", "$ref": "#/definitions/aiAssistantKefuStaff"}}}}, "aiAssistantKefuReply": {"type": "object", "title": "助手客服自动回复", "properties": {"custom": {"type": "array", "title": "自定义配置列表", "items": {"type": "object", "$ref": "#/definitions/AssistantKefuReplyCustom"}}, "enable_custom": {"type": "boolean", "title": "是否启用自定义配置"}, "mainland_en": {"title": "大陆英文", "$ref": "#/definitions/AssistantKefuReplyReply"}, "mainland_zh": {"title": "大陆中文", "$ref": "#/definitions/AssistantKefuReplyReply"}, "non_mainland_en": {"title": "非中国大陆英文", "$ref": "#/definitions/AssistantKefuReplyReply"}, "non_mainland_zh": {"title": "非中国大陆中文", "$ref": "#/definitions/AssistantKefuReplyReply"}}}, "aiAssistantKefuStaff": {"type": "object", "title": "人工客服信息", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键"}, "nickname": {"type": "string", "title": "昵称"}, "username": {"type": "string", "title": "账号"}}}, "aiAssistantLog": {"type": "object", "title": "助手日志", "properties": {"action": {"title": "操作类型", "$ref": "#/definitions/aiAssistantAction"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "changes": {"title": "配置变化", "$ref": "#/definitions/aiAssistantChanges"}, "create_by": {"title": "操作人", "$ref": "#/definitions/baseIdentity"}, "create_date": {"type": "string", "format": "date-time", "title": "操作时间"}, "id": {"type": "string", "format": "uint64", "title": "日志ID"}}}, "aiAssistantMiniprogramChannelConfig": {"type": "object", "title": "助手小程序渠道配置", "properties": {"assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"title": "图谱解析配置", "$ref": "#/definitions/aiAssistantGraphParseConfig"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服", "$ref": "#/definitions/aiAssistantKefuConfig"}, "miniprogram_config": {"title": "小程序配置", "$ref": "#/definitions/aiAssistantMiniprogramConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantMiniprogramConfig": {"type": "object", "title": "小程序配置", "properties": {"schema": {"type": "string", "title": "小程序schema"}, "share_image": {"type": "string", "title": "分享图片"}, "share_title": {"type": "string", "title": "分享标题"}, "url": {"type": "string", "title": "二维码URL"}}}, "aiAssistantPresetQuestion": {"type": "object", "title": "预设问题详情", "properties": {"content": {"type": "string", "title": "内容"}, "lang": {"type": "string", "title": "语言"}}}, "aiAssistantPresetQuestionConfig": {"type": "object", "title": "预设问题配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "questions": {"type": "array", "title": "配置列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantPresetQuestion"}}}}, "aiAssistantQuestionTypeConfig": {"type": "object", "title": "问题类型配置", "properties": {"chat_model": {"type": "string", "title": "聊天问题的模型"}, "complex_model": {"type": "string", "title": "复杂问题的模型"}, "enabled": {"type": "boolean", "title": "是否启用"}, "prompt": {"type": "string", "title": "简单、复杂问题提示词"}, "simple_model": {"type": "string", "title": "简单问题的模型"}}}, "aiAssistantRatingScaleReply": {"type": "object", "title": "满意度回复详情", "properties": {"content": {"type": "string", "title": "内容"}, "lang": {"type": "string", "title": "语言"}, "rating_scale": {"title": "评价等级", "$ref": "#/definitions/aiRatingScale"}}}, "aiAssistantRatingScaleReplyConfig": {"type": "object", "title": "满意度回复配置", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "replies": {"type": "array", "title": "满意度回复配置", "items": {"type": "object", "$ref": "#/definitions/aiAssistantRatingScaleReply"}}}}, "aiAssistantTanliveAppChannelConfig": {"type": "object", "title": "碳LIVE应用助手渠道配置", "properties": {"app_id": {"type": "string", "title": "应用ID"}, "assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"title": "图谱解析配置", "$ref": "#/definitions/aiAssistantGraphParseConfig"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服", "$ref": "#/definitions/aiAssistantKefuConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantTanliveWebChannelConfig": {"type": "object", "title": "碳LIVE Web助手渠道配置", "properties": {"assistant_lang": {"type": "string", "title": "助手语言（已废弃，请使用system_languages）"}, "avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "feedback_enabled": {"type": "boolean", "title": "用户教学反馈"}, "graph_parse_config": {"title": "图谱解析配置", "$ref": "#/definitions/aiAssistantGraphParseConfig"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服", "$ref": "#/definitions/aiAssistantKefuConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "nickname_en": {"type": "string", "title": "助手昵称（英文）"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "switch_assistant_id": {"type": "string", "format": "uint64", "title": "切换助手ID"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "website_config": {"title": "网站配置", "$ref": "#/definitions/aiAssistantWebsiteConfig"}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantUserLabelConfig": {"type": "object", "title": "助手用户打标配置", "properties": {"tag_ids": {"type": "array", "title": "绑定标签id", "items": {"type": "string", "format": "uint64"}}, "tag_names": {"type": "array", "title": "绑定标签的名称", "items": {"type": "string"}}}}, "aiAssistantV2": {"type": "object", "title": "助手信息", "properties": {"batch_no": {"type": "string", "title": "批次号"}, "config": {"title": "配置详情", "$ref": "#/definitions/aiAssistantConfig"}, "create_by": {"title": "创建人", "$ref": "#/definitions/baseIdentity"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "id": {"type": "string", "format": "uint64", "title": "主键"}, "is_draft": {"type": "boolean", "title": "是否为草稿"}, "update_by": {"title": "更新人", "$ref": "#/definitions/baseIdentity"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}}}, "aiAssistantVisibleChainConfig": {"type": "object", "title": "链路查询", "properties": {"enabled": {"type": "boolean", "title": "是否启用"}, "visible": {"type": "array", "title": "可见字段", "items": {"type": "string"}}}}, "aiAssistantWebsiteConfig": {"type": "object", "title": "网站配置", "properties": {"route_path": {"type": "string", "title": "路由路径"}, "title": {"type": "string", "title": "标题"}}}, "aiAssistantWeixinChannelConfig": {"type": "object", "title": "微信渠道配置", "properties": {"avatar_url": {"type": "string", "title": "头像（默认头像留空）"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "interactive_code_config": {"title": "互动暗号配置", "$ref": "#/definitions/aiAssistantInteractiveCodeConfig"}, "kefu_config": {"title": "人工客服配置", "$ref": "#/definitions/aiAssistantKefuConfig"}, "nickname": {"type": "string", "title": "助手昵称"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "weixin_develop_config": {"title": "微信开发配置", "$ref": "#/definitions/aiAssistantWeixinDevelopConfig"}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}}}, "aiAssistantWeixinDevelopConfig": {"type": "object", "title": "微信开发配置", "properties": {"corp_id": {"type": "string", "title": "企业ID"}, "kf_url": {"type": "string", "title": "客服URL"}, "open_kfid": {"type": "string", "title": "客服账号ID"}}}, "aiAssistantWelcomeMessage": {"type": "object", "title": "欢迎语详情", "properties": {"content": {"type": "string", "title": "内容"}, "lang": {"type": "string", "title": "语言"}}}, "aiAssistantWelcomeMessageConfig": {"type": "object", "title": "欢迎语配置", "properties": {"messages": {"type": "array", "title": "配置列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantWelcomeMessage"}}}}, "aiAssistantWhatsappChannelConfig": {"type": "object", "title": "Whatsapp助手渠道配置", "properties": {"avatar_url": {"type": "string", "title": "头像"}, "chat_idle_duration": {"type": "integer", "format": "int32", "title": "会话闲置超时时间（分钟）"}, "nickname": {"type": "string", "title": "昵称"}, "preset_question_config": {"title": "预设问题配置", "$ref": "#/definitions/aiAssistantPresetQuestionConfig"}, "rating_scale_reply_config": {"title": "满意度回复配置", "$ref": "#/definitions/aiAssistantRatingScaleReplyConfig"}, "system_lang": {"type": "string", "title": "系统语言（已废弃，请使用system_languages）"}, "system_languages": {"type": "array", "title": "系统语言", "items": {"type": "string"}}, "welcome_message_config": {"title": "欢迎语配置", "$ref": "#/definitions/aiAssistantWelcomeMessageConfig"}, "whatsapp_develop_config": {"title": "WhatsApp开发配置", "$ref": "#/definitions/aiAssistantWhatsappDevelopConfig"}}}, "aiAssistantWhatsappDevelopConfig": {"type": "object", "title": "WhatsApp开发配置", "properties": {"business_number": {"type": "string", "title": "Business number"}}}, "aiAutoChunkPara": {"type": "object", "title": "自动分段参数", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "chunk_config": {"title": "分段配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "dry_run": {"type": "boolean", "title": "仅预览"}}}, "aiChatCurrentState": {"type": "integer", "format": "int32", "title": "- 1: 当前会话未结束\n - 2: 当前会话已经被其他会话替代-已结束\n - 3: 当前会话已经超过可聊天规定时限-已结束", "enum": [1, 2, 3]}, "aiChatLiveAgentInfo": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话ID"}, "current_live_agent": {"title": "当前正在会话中的客服", "$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo"}, "live_agents": {"type": "array", "title": "人工客服列表", "items": {"type": "object", "$ref": "#/definitions/ChatLiveAgentInfoLiveAgentInfo"}}}}, "aiChatMessageContentFilterItem": {"type": "object", "properties": {"field": {"type": "string"}, "tags": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageTag"}}, "value": {"type": "string"}}}, "aiChatMessageFile": {"type": "object", "title": "文件信息", "properties": {"id": {"type": "string", "format": "uint64", "title": "文件ID"}, "parsed_url": {"type": "string", "title": "解析后的URL"}, "state": {"$ref": "#/definitions/aiChatMessageFileState"}, "url": {"type": "string", "title": "文件URL"}}}, "aiChatMessageFileState": {"type": "integer", "format": "int32", "title": "- 1: 文件解析中\n - 2: 文件解析成功\n - 3: 文件解析失败", "enum": [1, 2, 3]}, "aiChatMessageLog": {"type": "object", "properties": {"code": {"type": "string", "format": "uint64"}, "config_snapshot": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "enhancement": {"type": "string"}, "fetch_resp_time": {"type": "string", "format": "date-time"}, "gpt": {"type": "string"}, "message_id": {"type": "string", "format": "uint64"}, "ref": {"type": "string"}, "request_text": {"type": "string"}, "sql_query": {"type": "string"}, "start_time": {"type": "string", "format": "date-time"}, "type": {"$ref": "#/definitions/aiChatMessageType"}}}, "aiChatMessageOperator": {"type": "object", "properties": {"hash_id": {"type": "string"}, "operation_params": {"type": "string"}, "operation_type": {"$ref": "#/definitions/aiChatOperationType"}, "stop_chunk_state": {"type": "integer", "format": "int32"}, "stop_text": {"type": "string"}, "stop_think": {"type": "string"}}}, "aiChatMessageState": {"type": "integer", "format": "int32", "title": "- 1: 消息未发送标识\n - 2: 消息已经发送标识\n - 3: 默认消息已经发送标识\n - 4: 努力思考\n - 5: 整理答案\n - 6: 停止回答\n - 7: 回答推流中\n - 8: 推流全部完成\n - 9: 思考过程推流\n - 10: 切片推流完成\n - 12: 参考资料消息\n - 13: 建议问题", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13]}, "aiChatMessageTask": {"type": "object", "properties": {"order": {"type": "integer", "format": "int32"}, "pipeline_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiPipelineTaskState"}, "task_id": {"type": "string", "format": "uint64"}}}, "aiChatMessageType": {"type": "integer", "format": "int32", "title": "- 1: 用户消息\n - 2: 数据库查询\n - 3: collection查询\n - 4: 搜索引擎查询\n - 5: 系统错误\n - 6: 敏感信息错误\n - 7: 超时错误\n - 8: 取消回复\n - 9: 预定会话聊天回复\n - 10: 人工客服消息\n - 11: 多模态消息\n - 12: 清空上下文\n - 13: 建议问题\n - 14: 转人工二维码\n - 15: 回答草稿", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "aiChatModelOption": {"type": "object", "title": "聊天模型选项", "properties": {"disable_in_console": {"type": "boolean", "title": "是否在用户后台禁用"}, "model": {"type": "string", "title": "模型"}, "only_non_stream": {"type": "boolean", "title": "仅支持非流式"}, "support_think": {"type": "boolean", "title": "是否支持思考"}}}, "aiChatOperationType": {"description": "- 1: 正常回答\n - 2: 停止回答\n - 3: 停止思考\n - 4: 重新回答", "type": "integer", "format": "int32", "title": "会话操作类型", "enum": [1, 2, 3, 4]}, "aiChatSendRecordInfo": {"type": "object", "properties": {"content": {"type": "string", "title": "记录内容"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "id": {"type": "string", "format": "uint64", "title": "记录id"}, "image_url": {"type": "array", "items": {"type": "string"}}, "message_id": {"type": "string", "format": "uint64", "title": "消息id"}, "message_rating_scale": {"title": "记录对应的消息的评价信息", "$ref": "#/definitions/aiRatingScale"}, "message_type": {"title": "消息类型", "$ref": "#/definitions/aiChatMessageType"}, "record_type": {"title": "类型 1 用户询问 2 助手回答-text 3 助手回答-menu 4 助手回答建议问题菜单消息", "$ref": "#/definitions/aiAiRecordType"}, "reject_reason": {"type": "string"}, "send_date": {"type": "string", "format": "date-time", "title": "发送时间"}, "show_type": {"type": "integer", "format": "int32", "title": "显示状态"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}}}, "aiChatSuggestLog": {"type": "object", "properties": {"collections": {"type": "string"}, "config_snapshot": {"type": "string"}, "create_date": {"type": "string", "format": "date-time"}, "gpt": {"type": "string"}, "message_id": {"type": "string", "format": "uint64"}, "request_type": {"type": "string"}}}, "aiChatSupportType": {"type": "integer", "format": "int32", "title": "- 1: ai聊天\n - 2: 客服聊天", "enum": [1, 2]}, "aiChatType": {"description": "- 1: web\n - 2: 微信公众号\n - 3: whatsapp\n - 4: 小程序", "type": "integer", "format": "int32", "title": "后续会改为使用助手表的channel，如果是新功能就不要使用这个枚举了", "enum": [1, 2, 3, 4]}, "aiChunkItem": {"type": "object", "title": "文档分段信息", "properties": {"content": {"type": "string", "title": "分段内容"}, "len": {"type": "integer", "format": "int64", "title": "内容长度"}, "start": {"type": "integer", "format": "int64", "title": "起始索引位置"}}}, "aiCollection": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}}}, "aiCollectionQA": {"type": "object", "properties": {"answer": {"type": "string", "title": "答案"}, "assistants": {"type": "array", "title": "用户端", "items": {"type": "object", "$ref": "#/definitions/aiAssistant"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "create_date": {"type": "string", "format": "date-time"}, "has_repeated": {"type": "boolean", "title": "是否有重复"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "id": {"type": "string", "format": "uint64"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string", "title": "问题"}, "question_oversize": {"type": "boolean", "title": "问题是否超长"}, "reference": {"type": "array", "title": "参考资料", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"title": "状态(不包含助手对应的状态)", "$ref": "#/definitions/aiDocState"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "update_date": {"type": "string", "format": "date-time"}, "version_lag": {"type": "string", "format": "uint64", "title": "同步至ai侧的版本滞后数，为0代表已同步"}}}, "aiCollectionTextFile": {"type": "object", "properties": {"assistants": {"type": "array", "title": "用户端", "items": {"type": "object", "$ref": "#/definitions/aiAssistant"}}, "content_state": {"title": "内容状态", "$ref": "#/definitions/aiDocContentState"}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "copies": {"type": "array", "title": "副本列表", "items": {"type": "object", "$ref": "#/definitions/aiCollectionTextFile"}}, "create_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "create_date": {"type": "string", "format": "date-time"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "data_source_state": {"type": "integer", "format": "int64", "title": "外部数据源信息"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "has_over_sized_tables": {"type": "boolean", "title": "知识提示\n是否有超长标题表格"}, "has_repeated": {"type": "boolean", "title": "是否内容重复（租户内）"}, "hit_count": {"type": "integer", "format": "int64", "title": "命中次数"}, "id": {"type": "string", "format": "uint64"}, "is_copy": {"type": "boolean", "title": "是否为副本"}, "is_system": {"type": "boolean", "title": "是否为系统数据"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "parse_progress": {"type": "number", "format": "float", "title": "解析进度，0.5 = 50%"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "shared_states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"title": "状态(不包含助手对应的状态)", "$ref": "#/definitions/aiDocState"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "text": {"type": "string", "title": "文件/文本内容"}, "ugc_hashid": {"type": "string", "title": "ugc的hashid"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "ugc_title": {"type": "string", "title": "UGC标题"}, "ugc_type": {"title": "ugc类型", "$ref": "#/definitions/baseDataType"}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "update_date": {"type": "string", "format": "date-time"}, "url": {"type": "string", "title": "文件url"}, "version_lag": {"type": "string", "format": "uint64", "title": "同步至ai侧的版本滞后数，为0代表已同步"}}}, "aiContributor": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "贡献者所在的助手", "items": {"type": "string", "format": "uint64"}}, "full_name": {"type": "string", "title": "team full name"}, "id": {"type": "string", "format": "uint64", "title": "账户id\n个人账户: 个人账户id\n团队账户: 团队账户id\n运营端: 运营端账户id"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "level": {"title": "共创等级", "$ref": "#/definitions/teamTeamLevel"}, "text": {"type": "string", "title": "自定义纯文本 或者 个人/团队/运营端账户名称"}, "type": {"$ref": "#/definitions/baseIdentityType"}}}, "aiCustomLabel": {"type": "object", "title": "AI对话的标签", "properties": {"create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "id": {"type": "string", "format": "uint64", "title": "标签id"}, "key": {"type": "string", "title": "标签key"}, "next_label_id": {"type": "string", "format": "uint64"}, "next_label_name": {"type": "string"}, "type": {"title": "标签类型", "$ref": "#/definitions/aiCustomLabelType"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "value": {"$ref": "#/definitions/aiLabelValue"}}}, "aiCustomLabelObjectType": {"description": "- 1: AI 对话\n - 2: 知识库文档文本与文件\n - 3: 知识库文档QA\n - 4: 腾讯云文档导入的文本文件\n - 5: SQL数据导入的文本文件", "type": "integer", "format": "int32", "title": "标签对象类型", "enum": [1, 2, 3, 4, 5]}, "aiCustomLabelType": {"description": "- 1: 任意纯文本\n - 2: 字符串枚举(单选)\n - 3: 字符串枚举(多选)\n - 4: int\n - 5: uint\n - 6: float\n - 7: 年\n - 8: 年月\n - 9: 年月日\n - 10: 日期时间(年月日和时间)\n - 11: 时间", "type": "integer", "format": "int32", "title": "标签类型", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, "aiDocAssistantState": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "is_shared": {"type": "integer", "format": "int64", "title": "1否 2是"}, "state": {"$ref": "#/definitions/aiDocState"}}}, "aiDocChunkTask": {"type": "object", "title": "文段分段任务", "properties": {"id": {"type": "string", "format": "uint64", "title": "任务ID"}, "state": {"title": "任务状态", "$ref": "#/definitions/aiDocChunkTaskState"}}}, "aiDocChunkTaskState": {"description": "- 1: 执行中\n - 2: 已完成", "type": "integer", "format": "int32", "title": "文档分段任务状态", "enum": [1, 2]}, "aiDocContentState": {"description": "- 1: 同步中\n - 2: 有更新\n - 3: 已下架\n - 4: 已删除\n - 5: 人工", "type": "integer", "format": "int32", "title": "文档内容状态", "enum": [1, 2, 3, 4, 5]}, "aiDocDataSource": {"description": "- 1: tanlive ugc数据\n - 2: tanlive 知识库数据\n - 3: 腾讯云文档\n - 4: SQL 数据", "type": "integer", "format": "int32", "title": "知识的数据来源", "enum": [1, 2, 3, 4]}, "aiDocEmbeddingState": {"type": "integer", "format": "int32", "title": "- 1: 同步中\n - 2: 同步完成\n - 3: 删除中", "enum": [1, 2, 3]}, "aiDocFileDownloadAsRef": {"description": "- 1: 可下载\n - 2: 仅显示文件名\n - 3: 直接发送\n - 4: 隐藏", "type": "integer", "format": "int32", "title": "文本文件是否能作为参考资料下载", "enum": [1, 2, 3, 4]}, "aiDocMatchPattern": {"description": "- 1: 大模型召回\n - 2: 完全匹配\n - 3: 忽略标点匹配\n - 4: 未命中\n - 5: 包含关键字", "type": "integer", "format": "int32", "title": "匹配模式", "enum": [1, 2, 3, 4, 5]}, "aiDocParseMode": {"description": "- 1: 智能解析\n - 2: 文件解析\n - 3: 图像解析\n - 4: 表格解析", "type": "integer", "format": "int32", "title": "文档解析模式", "enum": [1, 2, 3, 4]}, "aiDocReference": {"type": "object", "title": "参考文献", "properties": {"id": {"type": "string", "format": "uint64", "title": "引用doc的参考文献"}, "name": {"type": "string"}, "show_type": {"title": "仅用于控制对话展示", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "text": {"type": "string", "title": "纯文本参考文献"}, "url": {"type": "string"}}}, "aiDocState": {"description": "- 1: 启用\n - 2: 禁用\n - 3: 解析中\n - 4: 解析失败\n - 5: 文件上传中\n - 6: 文件上传成功\n - 7: 删除中\n - 8: 解除了助手绑定（在助手中已删除）\n - 9: 重新解析中", "type": "integer", "format": "int32", "title": "知识库文档状态", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, "aiDocType": {"description": "- 1: QA\n - 2: 文本\n - 3: 文件", "type": "integer", "format": "int32", "title": "文档类型", "enum": [1, 2, 3]}, "aiEmbeddingModelCount": {"type": "object", "title": "向量化模型计数", "properties": {"collection_lang": {"type": "string", "title": "向量化模型"}, "count": {"type": "integer", "format": "int64", "title": "数量"}, "embedding_model_name": {"type": "string", "title": "向量化模型名称"}}}, "aiEmbeddingModelOption": {"type": "object", "title": "向量化模型选项", "properties": {"embedding_vector_length": {"type": "integer", "format": "int32", "title": "embedding向量长度"}, "en_default": {"title": "英文默认配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "en_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（上限）"}, "en_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个英文字符约对应多少个token（下限）"}, "model": {"type": "string", "title": "模型名称"}, "name": {"type": "string", "title": "名称"}, "recommended": {"type": "boolean", "title": "是否推荐"}, "tech_overlap_max_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最大长度（token）"}, "tech_overlap_min_tokens": {"type": "integer", "format": "int32", "title": "技术overlap最小长度（token）"}, "tech_seg_max_tokens": {"type": "integer", "format": "int32", "title": "技术最大分段长度（token）"}, "tech_seg_min_tokens": {"type": "integer", "format": "int32", "title": "技术最小分段长度（token）"}, "user_overlap_tokens": {"type": "integer", "format": "int32", "title": "用户overlap长度默认值（token）"}, "user_seg_max_tokens": {"type": "integer", "format": "int32", "title": "用户最大分段长度默认值（token）"}, "user_seg_min_tokens": {"type": "integer", "format": "int32", "title": "用户最小分段长度默认值（token）"}, "zh_default": {"title": "中文默认配置", "$ref": "#/definitions/aiAssistantChunkConfig"}, "zh_max_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（上限）"}, "zh_min_tokens_per_char": {"type": "number", "format": "float", "title": "1个中文字符约对应多少个token（下限）"}}}, "aiEventChatMessage": {"type": "object", "properties": {"answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "answers": {"type": "array", "title": "关联的回答", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "assistant_id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_final_query": {"type": "string"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "end_time": {"type": "string", "format": "date-time"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "id": {"type": "string", "format": "uint64"}, "image_url": {"type": "array", "items": {"type": "string"}}, "is_agent_command": {"type": "boolean", "title": "是否是agent回答"}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "lang": {"type": "string"}, "last_operation_type": {"title": "最后一次操作", "$ref": "#/definitions/aiChatOperationType"}, "link": {"type": "string"}, "process_time": {"$ref": "#/definitions/baseTimeRange"}, "prompt_type": {"type": "string", "title": "问题类型"}, "question_id": {"type": "string", "format": "uint64"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "reject_reason": {"type": "string"}, "show_type": {"type": "integer", "format": "int32"}, "sql_query": {"type": "string"}, "start_time": {"type": "string", "format": "date-time"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "suggest_count": {"type": "integer", "format": "int32"}, "suggest_questions": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "text": {"type": "string"}, "think": {"type": "string"}, "think_duration": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "wait_answer": {"type": "boolean"}}}, "aiExportTask": {"type": "object", "properties": {"create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "extra_info": {"type": "string"}, "fields_snapshot": {"type": "string"}, "filter_snapshot": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string", "format": "date-time"}, "max_batch_size": {"type": "integer", "format": "int32", "title": "单次请求最大数量"}, "max_response_threshold": {"type": "integer", "format": "int32", "title": "返回数据最大阈值"}, "operation_type": {"$ref": "#/definitions/aiTaskOperationType"}, "paths": {"type": "array", "items": {"type": "string"}}, "state": {"$ref": "#/definitions/aiExportTaskState"}, "type": {"$ref": "#/definitions/aiExportTaskType"}, "url": {"type": "string"}}}, "aiExportTaskState": {"type": "integer", "format": "int32", "title": "- 1: 导出中\n - 2: 已完成\n - 3: 失败", "enum": [1, 2, 3]}, "aiExportTaskType": {"type": "integer", "format": "int32", "title": "- 1: QA1\n - 2: 文本文件\n - 3: 会话消息\n - 4: 会话", "enum": [1, 2, 3, 4]}, "aiFeedback": {"type": "object", "title": "用户反馈", "properties": {"answer": {"type": "string", "title": "答案"}, "answer_id": {"type": "string", "format": "uint64", "title": "原始答案ID"}, "answer_rating": {"title": "回答评价", "$ref": "#/definitions/aiFeedbackAnswerRating"}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "assistant_name": {"type": "string", "title": "助手名称"}, "chat_id": {"type": "string", "format": "uint64", "title": "对话ID"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "create_identity": {"title": "创建人身份", "$ref": "#/definitions/baseIdentity"}, "has_mgmt_feedback": {"type": "boolean", "title": "是否有碳LIVE反馈"}, "has_op_feedback": {"type": "boolean", "title": "是否有运营反馈"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "hit_expected_doc": {"type": "boolean", "title": "是否命中预期知识"}, "id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "message_id": {"type": "string", "format": "uint64", "title": "消息ID"}, "mgmt_comment": {"title": "碳LIVE备注", "$ref": "#/definitions/aiFeedbackComment"}, "mgmt_feedback": {"title": "碳LIVE反馈", "$ref": "#/definitions/aiFeedbackComment"}, "mgmt_feedback_at": {"type": "string", "format": "date-time", "title": "碳LIVE反馈时间"}, "mgmt_feedback_by": {"title": "碳LIVE反馈人", "$ref": "#/definitions/baseIdentity"}, "op_comment": {"title": "分析备注", "$ref": "#/definitions/aiFeedbackComment"}, "op_feedback_at": {"type": "string", "format": "date-time", "title": "运营反馈时间"}, "op_feedback_by": {"title": "运营反馈人", "$ref": "#/definitions/baseIdentity"}, "question": {"type": "string", "title": "问题"}, "question_id": {"type": "string", "format": "uint64", "title": "原始问题ID"}, "state": {"title": "状态", "$ref": "#/definitions/aiFeedbackState"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "update_identity": {"title": "更新人身份", "$ref": "#/definitions/baseIdentity"}, "user_feedback_at": {"type": "string", "format": "date-time", "title": "用户反馈时间"}, "user_feedback_by": {"title": "用户反馈人", "$ref": "#/definitions/baseIdentity"}}}, "aiFeedbackAction": {"description": "- 1: 已读（已废弃）\n - 2: 采用\n - 3: 创建用户反馈\n - 4: 创建运营反馈\n - 5: 创建碳LIVE反馈\n - 6: 更新用户反馈\n - 7: 更新运营反馈\n - 8: 更新碳LIVE反馈", "type": "integer", "format": "int32", "title": "反馈操作", "enum": [1, 2, 3, 4, 5, 6, 7, 8]}, "aiFeedbackAnswerRating": {"description": "- 1: 好\n - 2: 坏", "type": "integer", "format": "int32", "title": "回答评价", "enum": [1, 2]}, "aiFeedbackComment": {"type": "object", "title": "反馈备注", "properties": {"content": {"type": "string", "title": "内容"}, "files": {"type": "array", "title": "文件列表", "items": {"type": "object", "$ref": "#/definitions/FeedbackCommentFile"}}}}, "aiFeedbackLog": {"type": "object", "title": "反馈操作日志", "properties": {"action": {"title": "操作", "$ref": "#/definitions/aiFeedbackAction"}, "create_date": {"type": "string", "format": "date-time", "title": "操作时间"}, "create_identity": {"title": "操作人", "$ref": "#/definitions/baseIdentity"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "id": {"type": "string", "format": "uint64", "title": "主键"}}}, "aiFeedbackReference": {"type": "object", "title": "反馈参考文献", "properties": {"create_by": {"type": "string", "format": "uint64", "title": "创建人"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "file_name": {"type": "string", "title": "文件名称"}, "file_path": {"type": "string", "title": "文件路径"}, "id": {"type": "string", "format": "uint64", "title": "参考文献ID"}, "text": {"type": "string", "title": "文本内容"}, "type": {"title": "类型", "$ref": "#/definitions/aiReferenceType"}, "update_by": {"type": "string", "format": "uint64", "title": "更新人"}, "update_date": {"type": "string", "format": "date-time", "title": "更新时间"}, "url": {"type": "string", "title": "URL链接"}}}, "aiFeedbackState": {"description": "- 1: 未读\n - 2: 已读\n - 3: 已采用", "type": "integer", "format": "int32", "title": "反馈状态", "enum": [1, 2, 3]}, "aiFullAssistant": {"type": "object", "title": "完整助手信息", "properties": {"assistant": {"title": "助手详情", "$ref": "#/definitions/aiAssistantV2"}, "terms_confirmed": {"type": "boolean", "title": "是否确认协议"}}}, "aiFullFeedbackLog": {"type": "object", "title": "完整反馈", "properties": {"feedback": {"title": "反馈", "$ref": "#/definitions/aiFeedback"}, "log": {"title": "日志", "$ref": "#/definitions/aiFeedbackLog"}, "original_question": {"title": "原始问题", "$ref": "#/definitions/tanliveaiChatMessage"}}}, "aiInteractiveCode": {"description": "- 1: 人工\n - 2: 重新回答\n - 3: 清空上下文\n - 4: 读配料表\n - 5: 读检测报告\n - 6: 贡献知识库", "type": "integer", "format": "int32", "title": "互动暗号", "enum": [1, 2, 3, 4, 5, 6]}, "aiInteractiveCodeOption": {"type": "object", "title": "互动暗号选项", "properties": {"code": {"title": "编号", "$ref": "#/definitions/aiInteractiveCode"}, "default_en": {"type": "string", "title": "默认英文值"}, "default_pre_en": {"type": "string", "title": "默认英文前缀"}, "default_pre_zh": {"type": "string", "title": "默认中文前缀"}, "default_zh": {"type": "string", "title": "默认中文值"}, "deletable": {"type": "boolean", "title": "是否可删除"}, "text": {"type": "string", "title": "文本"}}}, "aiLabelFilter": {"type": "object", "properties": {"eq": {"$ref": "#/definitions/aiLabelValue"}, "gte": {"$ref": "#/definitions/aiLabelValue"}, "id": {"type": "string", "format": "uint64", "title": "标签id"}, "in": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelValue"}}, "like": {"$ref": "#/definitions/aiLabelValue"}, "lte": {"$ref": "#/definitions/aiLabelValue"}, "op": {"$ref": "#/definitions/aiLabelFilterOp"}}}, "aiLabelFilterOp": {"type": "integer", "format": "int32", "title": "- 1: 等于\n - 2: IN\n - 3: 大于等于\n - 4: 小于等于\n - 5: LIKE模糊搜索", "enum": [1, 2, 3, 4, 5]}, "aiLabelValue": {"type": "object", "title": "标签取值", "properties": {"datetime_value": {"type": "string", "format": "int64", "title": "日期时间(年月日和时间)"}, "enum_m_value": {"type": "string", "title": "字符串枚举(多选)"}, "enum_value": {"type": "string", "title": "字符串枚举(单选)"}, "float_value": {"type": "number", "format": "double", "title": "浮点型"}, "int_value": {"type": "string", "format": "int64", "title": "整型值"}, "text_value": {"type": "string", "title": "任意纯文本"}, "time_value": {"type": "string", "format": "int64", "title": "时间"}, "uint_value": {"type": "string", "format": "uint64", "title": "无符号整形"}, "y_value": {"type": "string", "format": "int64", "title": "年"}, "ym_value": {"type": "string", "format": "int64", "title": "年月"}, "ymd_value": {"type": "string", "format": "int64", "title": "年月日"}}}, "aiListDocFilterType": {"type": "integer", "format": "int32", "title": "- 1: qa\n - 2: 文本/文件\n - 3: 系统数据", "enum": [1, 2, 3]}, "aiManualChunkPara": {"type": "object", "title": "文档手动分段参数", "properties": {"assistant_chunks": {"type": "array", "title": "新分段列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}}}}, "aiMessageCollectionSnapshot": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "end_time": {"type": "string", "format": "date-time"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiSearchCollectionItem"}}, "message_id": {"type": "string", "format": "uint64"}, "start_time": {"type": "string", "format": "date-time"}}}, "aiMessageDocSnapshot": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "message_id": {"type": "string", "format": "uint64"}}}, "aiMessageTag": {"type": "object", "properties": {"data_type": {"type": "integer", "format": "int32"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "taggable_id": {"type": "string", "format": "uint64"}, "taggable_type": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}}}, "aiMessageUgc": {"type": "object", "properties": {"cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgcCard"}}, "filter": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageContentFilterItem"}}, "is_ugc_link": {"type": "boolean"}, "ugc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "ugc_type": {"$ref": "#/definitions/baseDataType"}}}, "aiMessageUgcCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "logo_url": {"type": "string"}, "name": {"type": "string"}, "tags": {"type": "string"}}}, "aiOrderByLabel": {"type": "object", "properties": {"desc": {"type": "boolean"}, "id": {"type": "string", "format": "uint64"}}}, "aiPipelineTaskState": {"type": "integer", "format": "int32", "title": "- 1: 进行中\n - 2: 已完成\n - 3: 失败", "enum": [1, 2, 3]}, "aiQuestionAskType": {"type": "integer", "format": "int32", "title": "- 1: 正常问答\n - 2: 重新回答(包括了用户输入的\"重新回答\"或者，用户输入了同样的问题)\n - 3: 继续回答(包括被错误识别为了转人工之后确认为继续回答，或者 发送条数到达上限后的继续回答)\n - 4: 预设问答\n - 5: 预设隐藏回答\n - 6: 文件问答\n - 7: 语音问答\n - 8: 图片问答\n - 9: 撤回问答（用户在微信端撤回消息）\n - 10: 匹配到QA的问答", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}, "aiRatingScale": {"description": "- 1: 满意\n - 2: 一般\n - 3: 不满意", "type": "integer", "format": "int32", "title": "评价等级", "enum": [1, 2, 3]}, "aiReferenceType": {"description": "- 1: URL\n - 2: 文本\n - 3: 文件", "type": "integer", "format": "int32", "title": "参考文献类型", "enum": [1, 2, 3]}, "aiReqAcceptFeedback": {"type": "object", "properties": {"feedback_ids": {"type": "array", "title": "反馈ID", "items": {"type": "string", "format": "uint64"}}}}, "aiReqAutoChunkDoc": {"type": "object", "properties": {"auto_para": {"title": "自动分段参数", "$ref": "#/definitions/aiAutoChunkPara"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}}}, "aiReqBatchCreateAssistant": {"type": "object", "properties": {"configs": {"type": "array", "title": "配置列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantConfig"}}, "is_draft": {"type": "boolean", "title": "是否保存为草稿"}}}, "aiReqBatchUpdateAssistant": {"type": "object", "properties": {"batch_no": {"type": "string", "title": "批次号\n如果指定了批次号，items里的助手必须都属于该批次号，且批次号内的助手必须为草稿，允许新增、删除；未指定批次号时items里的助手必须为非草稿，仅允许更新"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿（已发布的助手忽略该参数）"}, "items": {"type": "array", "title": "助手列表", "items": {"type": "object", "$ref": "#/definitions/aiReqBatchUpdateAssistantItem"}}}}, "aiReqBatchUpdateAssistantItem": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "config": {"title": "配置详情", "$ref": "#/definitions/aiAssistantConfig"}, "mask": {"type": "string", "title": "Field mask"}}}, "aiReqBatchUpdateDocAttr": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "修改关联的助手，增加的助手启用/禁用状态和已有的助手状态一致", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "download_as_ref": {"title": "参考资料下载方式", "$ref": "#/definitions/aiDocFileDownloadAsRef"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "mask": {"type": "string"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "query_id": {"type": "string", "format": "uint64"}, "reference": {"type": "array", "title": "参考资料", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}}}, "aiReqCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqCreateAssistant": {"type": "object", "properties": {"config": {"title": "配置列表", "$ref": "#/definitions/aiAssistantConfig"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿"}}}, "aiReqCreateAssistantShare": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "doc_id": {"type": "string", "format": "uint64"}, "query_id": {"type": "string", "format": "uint64"}, "team_id": {"type": "array", "title": "分享给团队的ID列表", "items": {"type": "string", "format": "uint64"}}, "user_id": {"type": "array", "title": "分享给个人的ID列表", "items": {"type": "string", "format": "uint64"}}}}, "aiReqCreateDocQuery": {"type": "object", "properties": {"doc": {"$ref": "#/definitions/aiReqListTextFiles"}, "qa": {"$ref": "#/definitions/aiReqListQA"}}}, "aiReqCreateDocShareConfigSender": {"type": "object", "properties": {"share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_team_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqCreateQA": {"type": "object", "properties": {"answer": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"$ref": "#/definitions/aiDocState"}}}, "aiReqCreateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateQA"}}}}, "aiReqCreateSystemDocCopy": {"type": "object", "properties": {"items": {"type": "array", "title": "创建列表", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateSystemDocCopyItem"}}}}, "aiReqCreateSystemDocCopyItem": {"type": "object", "properties": {"doc_id": {"description": "// 文档内容\nstring text = 2 [(validator) = \"required\"];\n// 助手\nrepeated uint64 assistant_id = 3 [(validator) = \"omitempty,dive,required\"];\n// 贡献者\nrepeated tanlive.ai.Contributor contributor = 4 [(validator) = \"omitempty,dive,required\"];\n// 是否显示贡献者\nuint32 show_contributor = 5 [(validator) = \"required,oneof=1 2\"];", "type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqCreateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqCreateTextFilesItem"}}}}, "aiReqCreateTextFilesItem": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"title": "数据源", "$ref": "#/definitions/aiDocDataSource"}, "file_name": {"type": "string", "title": "文件/文本名称"}, "parse_mode": {"title": "解析模式", "$ref": "#/definitions/aiDocParseMode"}, "parsed_url": {"type": "string", "title": "文件解析后地址"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "state": {"title": "状态 1: 启用 2: 停用 状态设置只对文本有效", "$ref": "#/definitions/aiDocState"}, "text": {"type": "string", "title": "文件/文本内容"}, "type": {"type": "integer", "format": "int64", "title": "类型 2:文本 3:文件"}, "ugc_id": {"type": "string", "format": "uint64", "title": "ugc的id"}, "ugc_type": {"title": "ugc类型", "$ref": "#/definitions/baseDataType"}, "url": {"type": "string", "title": "文件url"}}}, "aiReqDeleteAssistant": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "batch_no": {"type": "string", "title": "批次号"}}}, "aiReqDeleteCustomLabels": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqDeleteDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqDeleteSystemDoc": {"type": "object", "properties": {"doc_id": {"type": "array", "title": "文档ID", "items": {"type": "string", "format": "uint64"}}}}, "aiReqDescribeChatRegionCode": {"type": "object", "properties": {"assistant_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "aiReqDescribeChatSuggestLog": {"type": "object", "properties": {"message_id": {"type": "string", "format": "uint64"}}}, "aiReqDescribeExportTasks": {"type": "object", "properties": {"operation_type": {"$ref": "#/definitions/aiTaskOperationType"}, "type": {"type": "array", "items": {"$ref": "#/definitions/aiExportTaskType"}}}}, "aiReqDescribeFeedbackRegionCode": {"type": "object", "properties": {"region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "aiReqDisableSystemDoc": {"type": "object", "properties": {"doc_id": {"type": "array", "title": "文档ID", "items": {"type": "string", "format": "uint64"}}}}, "aiReqEnableSystemDoc": {"type": "object", "properties": {"doc_id": {"type": "array", "title": "文档ID", "items": {"type": "string", "format": "uint64"}}}}, "aiReqFindFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "用户反馈ID"}}}, "aiReqGetAssistantLogsPage": {"type": "object", "properties": {"action": {"type": "array", "title": "操作类型", "items": {"$ref": "#/definitions/aiAssistantAction"}}, "assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "create_date": {"title": "操作时间范围", "$ref": "#/definitions/baseTimeRange"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}}}, "aiReqGetAssistantsPage": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "assistant_name": {"type": "string", "title": "助手名称"}, "batch_no": {"type": "string", "title": "批次号"}, "channel": {"type": "array", "title": "渠道", "items": {"$ref": "#/definitions/aiAssistantChannel"}}, "collection_lang": {"type": "array", "title": "知识库语言", "items": {"type": "string"}}, "create_date": {"title": "创建时间范围", "$ref": "#/definitions/baseTimeRange"}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "enabled": {"title": "是否启用", "$ref": "#/definitions/baseBoolEnum"}, "is_draft": {"title": "是否为草稿", "$ref": "#/definitions/baseBoolEnum"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "model": {"type": "array", "title": "模型", "items": {"type": "string"}}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "order_by": {"type": "array", "title": "排序：create_date、update_date", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "route_path": {"type": "string", "title": "路由"}, "search_engine": {"type": "array", "title": "搜索引擎", "items": {"type": "string"}}, "team_id": {"type": "array", "title": "所属团队ID", "items": {"type": "string", "format": "uint64"}}, "terms_confirmed": {"title": "是否确认协议", "$ref": "#/definitions/baseBoolEnum"}, "user_id": {"type": "array", "title": "所属用户ID", "items": {"type": "string", "format": "uint64"}}}}, "aiReqGetChatDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "chat_id"}, "keyword": {"type": "string", "title": "消息内容搜索关键词"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "question_id": {"type": "string", "format": "uint64", "title": "问题ID"}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "send_range": {"title": "创建时间区间", "$ref": "#/definitions/baseTimeRange"}}}, "aiReqGetChatMessageDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "region": {"$ref": "#/definitions/baseRegion"}}}, "aiReqGetChunkDocTasks": {"type": "object", "properties": {"doc_id": {"type": "array", "title": "文档ID", "items": {"type": "string", "format": "uint64"}}}}, "aiReqGetDocChunks": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手ID", "items": {"type": "string", "format": "uint64"}}, "doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqGetDocEmbeddingModels": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqGetFeedbackLogs": {"type": "object", "properties": {"action": {"type": "array", "title": "操作类型", "items": {"$ref": "#/definitions/aiFeedbackAction"}}, "create_date_range": {"title": "操作时间区间", "$ref": "#/definitions/baseTimeRange"}, "create_identity": {"title": "操作人", "$ref": "#/definitions/baseIdentity"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "order_by": {"type": "array", "title": "排序", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "aiReqGetFeedbacks": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "助手", "items": {"type": "string", "format": "uint64"}}, "create_by": {"type": "array", "title": "上传用户ID", "items": {"type": "string", "format": "uint64"}}, "create_date_range": {"title": "创建时间区间", "$ref": "#/definitions/baseTimeRange"}, "feedback_id": {"type": "array", "title": "反馈ID", "items": {"type": "string", "format": "uint64"}}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}, "order_by": {"type": "array", "title": "排序", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "region_codes": {"type": "array", "items": {"type": "string"}}}}, "aiReqGetQaTip": {"type": "object", "title": "获取QA知识提示请求", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqGetTextFile": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "aiReqGetTextFileTip": {"type": "object", "title": "获取文件文本知识提示请求", "properties": {"id": {"type": "string", "format": "uint64", "title": "文档ID"}}}, "aiReqImportQA": {"type": "object", "properties": {"answer": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "force_create": {"type": "boolean", "title": "是否强制新建，如果为true则不进行重复校验，直接创建新的QA"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "mask": {"type": "string", "title": "用于指定哪些字段需要更新"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "share_assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}}}, "aiReqImportQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqImportQA"}}}}, "aiReqListAssistant": {"type": "object", "properties": {"language": {"type": "string", "title": "语言，为空默认zh"}, "limit": {"type": "integer", "format": "int64"}, "name": {"type": "string", "title": "搜索名称关键词"}, "offset": {"type": "integer", "format": "int64"}, "type": {"$ref": "#/definitions/aiChatType"}}}, "aiReqListAssistantCanShareDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}, "language": {"type": "string", "title": "语言，为空默认zh"}, "name": {"type": "string", "title": "搜索名称关键词"}}}, "aiReqListChat": {"type": "object", "properties": {"assistant_ids": {"type": "array", "title": "助手id", "items": {"type": "string", "format": "uint64"}}, "create_date_range": {"title": "创建时间区间", "$ref": "#/definitions/baseTimeRange"}, "filter": {"$ref": "#/definitions/ReqListChatFilter"}, "labels": {"type": "array", "title": "自定义标签kv对", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}, "order_by_label": {"title": "自定义标签排序，只能当个标签排序", "$ref": "#/definitions/aiOrderByLabel"}, "update_date_range": {"title": "处理时间区间", "$ref": "#/definitions/baseTimeRange"}}}, "aiReqListChatLiveAgent": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64"}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "aiReqListCollectionFileName": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "full_search": {"type": "array", "title": "文件名精确匹配搜索", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "search": {"type": "string", "title": "文件名模糊搜索匹配"}}}, "aiReqListContributor": {"type": "object", "properties": {"data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "aiReqListCustomLabel": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "limit": {"type": "integer", "format": "int64"}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}, "offset": {"type": "integer", "format": "int64"}}}, "aiReqListDocByRef": {"type": "object", "properties": {"ref": {"type": "array", "items": {"type": "string"}}}}, "aiReqListOperator": {"type": "object", "properties": {"creator": {"type": "boolean", "title": "是否为创建人，false代表更新人，true代表创建人"}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "search": {"type": "string"}, "type": {"$ref": "#/definitions/aiListDocFilterType"}}}, "aiReqListQA": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "在助手中", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "embedding_state": {"title": "向量状态", "$ref": "#/definitions/aiDocEmbeddingState"}, "excluded_assistant_id": {"type": "array", "title": "不在助手中", "items": {"type": "string", "format": "uint64"}}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "limit": {"type": "integer", "format": "int64"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "order_by_label": {"title": "自定义标签排序，只能当个标签排序", "$ref": "#/definitions/aiOrderByLabel"}, "search": {"type": "string"}, "share_team_id": {"type": "array", "title": "分享的团队id", "items": {"type": "string", "format": "uint64"}}, "share_user_id": {"type": "array", "title": "分享的用户id", "items": {"type": "string", "format": "uint64"}}, "show_contributor": {"type": "integer", "format": "int64"}, "state": {"$ref": "#/definitions/aiDocState"}, "tip_filter": {"$ref": "#/definitions/aiReqListQATipFilter"}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}}}, "aiReqListQATipFilter": {"type": "object", "title": "知识提示过滤条件，用来筛选问题超长等问题的记录", "properties": {"warning": {"type": "boolean"}}}, "aiReqListTextFiles": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "content_state": {"type": "array", "title": "内容状态", "items": {"$ref": "#/definitions/aiDocContentState"}}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "create_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "data_source": {"title": "数据源", "$ref": "#/definitions/aiDocDataSource"}, "data_source_state": {"type": "integer", "format": "int64", "title": "数据源同步状态"}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "embedding_state": {"title": "向量状态", "$ref": "#/definitions/aiDocEmbeddingState"}, "excluded_assistant_id": {"type": "array", "title": "不在助手中", "items": {"type": "string", "format": "uint64"}}, "group_repeated": {"type": "boolean", "title": "重复 doc 紧凑排序"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "is_system": {"type": "boolean", "title": "是否为系统文档"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiLabelFilter"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/baseOrderBy"}}, "order_by_label": {"title": "自定义标签排序，只能当个标签排序", "$ref": "#/definitions/aiOrderByLabel"}, "parse_mode": {"type": "array", "items": {"$ref": "#/definitions/aiDocParseMode"}}, "parse_state": {"title": "查询解析失败的数据", "$ref": "#/definitions/aiDocState"}, "search": {"$ref": "#/definitions/ReqListTextFilesSearch"}, "show_contributor": {"type": "integer", "format": "int64"}, "state": {"$ref": "#/definitions/aiDocState"}, "tip_filter": {"$ref": "#/definitions/aiReqListTextFilesTipFilter"}, "ugc_type": {"type": "array", "title": "UGC模块", "items": {"$ref": "#/definitions/baseDataType"}}, "update_by": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanliveaiOperator"}}, "with_copies": {"type": "boolean", "title": "是否查询副本"}}}, "aiReqListTextFilesTipFilter": {"type": "object", "title": "知识提示过滤条件", "properties": {"warning": {"type": "boolean", "title": "警告条件组"}}}, "aiReqListeDocShareConfigSender": {"type": "object", "properties": {"language": {"type": "string", "title": "语言，为空默认zh"}, "name": {"type": "string", "title": "搜索名称关键词"}}}, "aiReqManualChunkDoc": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "文档ID"}, "manual_para": {"title": "手动分段参数", "$ref": "#/definitions/aiManualChunkPara"}, "new_text": {"type": "string", "title": "新文本（如果文本未改动不需要传值）"}}}, "aiReqModifyCustomLabels": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "object_type": {"$ref": "#/definitions/aiCustomLabelObjectType"}}}, "aiReqOnOffDocs": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "query_id": {"type": "string", "format": "uint64"}, "state": {"$ref": "#/definitions/aiDocState"}}}, "aiReqProxyChatHtmlUrl": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "urls": {"type": "array", "items": {"type": "string"}}}}, "aiReqReparseTextFiles": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "parse_mode": {"$ref": "#/definitions/aiDocParseMode"}, "query_id": {"type": "string", "format": "uint64"}}}, "aiReqSearchChat": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手id"}, "clean_chunks": {"type": "boolean"}, "question_id": {"type": "string", "format": "uint64"}, "temperature": {"type": "number", "format": "float", "title": "温度"}, "text": {"type": "string"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "关键词召回匹配目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int32", "title": "关键词召回条数"}, "threshold": {"type": "number", "format": "float", "title": "阈值"}, "top_n": {"type": "integer", "format": "int64", "title": "top n"}}}, "aiReqSearchCollection": {"type": "object", "properties": {"assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "clean_chunks": {"type": "boolean"}, "doc_type": {"$ref": "#/definitions/aiDocType"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "search": {"type": "string"}, "temperature": {"type": "number", "format": "float"}, "text_recall_pattern": {"title": "关键词召回模式", "$ref": "#/definitions/aiTextRecallPattern"}, "text_recall_query": {"title": "关键词召回匹配目标", "$ref": "#/definitions/aiTextRecallQuery"}, "text_recall_slop": {"type": "integer", "format": "int32", "title": "关键词召回允许平移距离允许平移距离"}, "text_recall_top_n": {"type": "integer", "format": "int64"}, "text_weight": {"type": "number", "format": "float"}, "threshold": {"type": "number", "format": "float"}, "top_n": {"type": "integer", "format": "int64"}}}, "aiReqSwitchChatLiveAgent": {"type": "object", "properties": {"chat_id": {"type": "string", "format": "uint64", "title": "会话id"}, "live_agent_id": {"type": "string", "format": "uint64", "title": "人工客服id"}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}}}, "aiReqSyncFixChatMessageCollection": {"type": "object", "properties": {"chat_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiReqUpdateAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}, "config": {"title": "配置详情", "$ref": "#/definitions/aiAssistantConfig"}, "is_draft": {"type": "boolean", "title": "是否保存为草稿（已发布的助手忽略该参数）"}, "mask": {"type": "string", "title": "Field mask"}}}, "aiReqUpdateChatNoticeConf": {"type": "object", "properties": {"conf": {"$ref": "#/definitions/aiAiAssistantNoticeConf"}}}, "aiReqUpdateObjectCustomLabels": {"type": "object", "properties": {"id": {"type": "array", "title": "打标签的对象 id", "items": {"type": "string", "format": "uint64"}}, "labels": {"type": "array", "title": "自定义标签", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "object_type": {"title": "打标签的对象类型", "$ref": "#/definitions/aiCustomLabelObjectType"}}}, "aiReqUpdateQA": {"type": "object", "properties": {"answer": {"type": "string"}, "contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "id": {"type": "string", "format": "uint64"}, "mask": {"type": "string"}, "match_patterns": {"type": "array", "title": "匹配模式", "items": {"$ref": "#/definitions/aiDocMatchPattern"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}}}, "aiReqUpdateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqUpdateQA"}}}}, "aiReqUpdateTextFile": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "download_as_ref": {"$ref": "#/definitions/aiDocFileDownloadAsRef"}, "file_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "mask": {"type": "string"}, "parsed_url": {"type": "string", "title": "解析后的文件url"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "show_contributor": {"type": "integer", "format": "int64", "title": "是否显示贡献者"}, "states": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocAssistantState"}}, "text": {"type": "string"}, "ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "url": {"type": "string"}}}, "aiReqUpdateTextFiles": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqUpdateTextFile"}}}}, "aiReqUpsertMgmtFeedback": {"type": "object", "properties": {"answer_id": {"type": "string", "format": "uint64", "title": "通过答案ID更新或创建反馈"}, "answer_rating": {"title": "AI回答评价", "$ref": "#/definitions/aiFeedbackAnswerRating"}, "feedback_id": {"type": "string", "format": "uint64", "title": "通过反馈ID更新反馈"}, "mgmt_comment": {"title": "碳LIVE内部备注", "$ref": "#/definitions/aiFeedbackComment"}, "mgmt_doc_id": {"type": "array", "title": "预期命中的知识", "items": {"type": "string", "format": "uint64"}}, "mgmt_feedback": {"title": "碳LIVE反馈", "$ref": "#/definitions/aiFeedbackComment"}}}, "aiReqValidateQAs": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiReqValidateQAsItem"}}}}, "aiReqValidateQAsItem": {"type": "object", "properties": {"answer": {"type": "string"}, "assistant_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "contributor": {"type": "array", "title": "贡献者", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "question": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "state": {"$ref": "#/definitions/aiDocState"}}}, "aiRspAcceptFeedback": {"type": "object", "properties": {"results": {"type": "array", "title": "结果", "items": {"type": "object", "$ref": "#/definitions/aiRspAcceptFeedbackResult"}}}}, "aiRspAcceptFeedbackResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}, "feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}}}, "aiRspAutoChunkDoc": {"type": "object", "properties": {"chunks": {"type": "array", "title": "分段列表", "items": {"type": "object", "$ref": "#/definitions/aiChunkItem"}}}}, "aiRspBatchCreateAssistant": {"type": "object", "properties": {"assistant_id": {"type": "array", "title": "助手ID列表", "items": {"type": "string", "format": "uint64"}}, "batch_no": {"type": "string", "title": "批次号"}}}, "aiRspBatchUpdateDocAttr": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspCloneDoc": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspCreateAssistant": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手ID"}}}, "aiRspCreateDocQuery": {"type": "object", "properties": {"is_empty": {"type": "boolean"}, "query_id": {"type": "string", "format": "uint64"}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspCreateDocShare": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspCreateFeedback": {"type": "object", "properties": {"feedback_id": {"type": "string", "format": "uint64", "title": "反馈ID"}}}, "aiRspCreateSystemDocCopy": {"type": "object", "properties": {"results": {"type": "array", "title": "结果列表", "items": {"type": "object", "$ref": "#/definitions/aiRspCreateSystemDocCopyResult"}}}}, "aiRspCreateSystemDocCopyResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspCreateTextFiles": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "aiRspDeleteDocs": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspDeleteSystemDoc": {"type": "object", "properties": {"results": {"type": "array", "title": "结果列表", "items": {"type": "object", "$ref": "#/definitions/aiRspDeleteSystemDocResult"}}}}, "aiRspDeleteSystemDocResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspDescribeChatRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "aiRspDescribeChatSuggestLog": {"type": "object", "properties": {"logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatSuggestLog"}}}}, "aiRspDescribeExportTasks": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiExportTask"}}}}, "aiRspDescribeFeedbackRegionCode": {"type": "object", "properties": {"region_codes": {"type": "array", "items": {"type": "string"}}}}, "aiRspDisableSystemDoc": {"type": "object", "properties": {"results": {"type": "array", "title": "结果列表", "items": {"type": "object", "$ref": "#/definitions/aiRspDisableSystemDocResult"}}}}, "aiRspDisableSystemDocResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspEnableSystemDoc": {"type": "object", "properties": {"results": {"type": "array", "title": "结果列表", "items": {"type": "object", "$ref": "#/definitions/aiRspEnableSystemDocResult"}}}}, "aiRspEnableSystemDocResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码"}}}, "aiRspFindFeedback": {"type": "object", "properties": {"expected_docs": {"type": "array", "title": "预期命中的知识", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "expected_mgmt_docs": {"type": "array", "title": "预期命中的知识（碳LIVE运营）", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "feedback": {"title": "反馈详情", "$ref": "#/definitions/aiFeedback"}, "mgmt_user_cards": {"type": "array", "title": "运营端用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}}, "original_answer": {"title": "原始回答", "$ref": "#/definitions/tanliveaiChatMessage"}, "original_question": {"title": "原始问题", "$ref": "#/definitions/tanliveaiChatMessage"}, "references": {"type": "array", "title": "参考文献", "items": {"type": "object", "$ref": "#/definitions/aiFeedbackReference"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}}}}, "aiRspGetAssistantLogsPage": {"type": "object", "properties": {"logs": {"type": "array", "title": "日志列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantLog"}}, "mgmt_user_cards": {"type": "array", "title": "运营端用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}}}}, "aiRspGetAssistantOptions": {"type": "object", "properties": {"ask_suggestion_model": {"type": "array", "title": "问题建议模型", "items": {"type": "string"}}, "chat_model": {"type": "array", "title": "对话模型", "items": {"type": "string"}}, "chat_model_v2": {"type": "array", "title": "对话模型", "items": {"type": "object", "$ref": "#/definitions/aiChatModelOption"}}, "chat_or_sql_model": {"type": "array", "title": "ChatOrSql模型", "items": {"type": "string"}}, "embedding_model": {"type": "array", "title": "向量化模型", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelOption"}}, "graph_parse_mode": {"type": "array", "title": "解析图谱模型", "items": {"type": "string"}}, "interactive_code": {"type": "array", "title": "互动暗号", "items": {"type": "object", "$ref": "#/definitions/aiInteractiveCodeOption"}}, "mini_white_url": {"type": "array", "title": "小程序URL白名单", "items": {"type": "string"}}, "search_engine": {"type": "array", "title": "搜索引擎", "items": {"type": "string"}}, "search_engine_v2": {"type": "array", "title": "对话模型", "items": {"type": "object", "$ref": "#/definitions/aiSearchEngineOption"}}, "visible_chain": {"type": "array", "title": "链路查询", "items": {"type": "object", "$ref": "#/definitions/aiVisibleChainOption"}}}}, "aiRspGetAssistantsPage": {"type": "object", "properties": {"assistants": {"type": "array", "title": "助手列表", "items": {"type": "object", "$ref": "#/definitions/aiFullAssistant"}}, "mgmt_user_cards": {"type": "array", "title": "运营端用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_account": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}}}}, "aiRspGetChatDetail": {"type": "object", "properties": {"chat_detail": {"$ref": "#/definitions/tanlivebff_mgmtaiChatDetail"}, "totalCount": {"type": "integer", "format": "int64"}}}, "aiRspGetChatMessageDetail": {"type": "object", "properties": {"clean_chunks": {"type": "boolean"}, "collection_items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiSearchCollectionItem"}}, "collection_time": {"$ref": "#/definitions/baseTimeRange"}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "message": {"$ref": "#/definitions/aiEventChatMessage"}}}, "aiRspGetChunkDocTasks": {"type": "object", "properties": {"tasks": {"type": "array", "title": "任务列表", "items": {"type": "object", "$ref": "#/definitions/aiDocChunkTask"}}}}, "aiRspGetDocChunks": {"type": "object", "properties": {"assistant_chunks": {"type": "array", "title": "助手的分段列表", "items": {"type": "object", "$ref": "#/definitions/aiAssistantChunks"}}}}, "aiRspGetDocEmbeddingModels": {"type": "object", "properties": {"embedding_models": {"type": "array", "title": "向量化模型列表", "items": {"type": "object", "$ref": "#/definitions/aiEmbeddingModelCount"}}}}, "aiRspGetFeedbackLogs": {"type": "object", "properties": {"items": {"type": "array", "title": "日志列表", "items": {"type": "object", "$ref": "#/definitions/aiFullFeedbackLog"}}, "mgmt_user_cards": {"type": "array", "title": "运营端用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}}}}, "aiRspGetFeedbacks": {"type": "object", "properties": {"items": {"type": "array", "title": "反馈列表", "items": {"type": "object", "$ref": "#/definitions/aiRspGetFeedbacksItem"}}, "mgmt_user_cards": {"type": "array", "title": "运营端用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanlivemgmtUserCard"}}, "team_cards": {"type": "array", "title": "团队卡片列表", "items": {"type": "object", "$ref": "#/definitions/teamTeamCard"}}, "total_count": {"type": "integer", "format": "int64", "title": "总数"}, "user_cards": {"type": "array", "title": "用户卡片列表", "items": {"type": "object", "$ref": "#/definitions/tanliveiamUserCard"}}}}, "aiRspGetFeedbacksItem": {"type": "object", "properties": {"expected_docs": {"type": "array", "title": "预期命中的知识", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "expected_mgmt_docs": {"type": "array", "title": "预期命中的知识（碳LIVE运营）", "items": {"type": "object", "$ref": "#/definitions/tanliveaiChatMessageDoc"}}, "feedback": {"title": "反馈详情", "$ref": "#/definitions/aiFeedback"}, "original_answer": {"title": "原始回答", "$ref": "#/definitions/tanliveaiChatMessage"}, "original_question": {"title": "原始问题", "$ref": "#/definitions/tanliveaiChatMessage"}, "references": {"type": "array", "title": "参考文献", "items": {"type": "object", "$ref": "#/definitions/aiFeedbackReference"}}}}, "aiRspGetQaTip": {"type": "object", "title": "获取QA知识提示响应", "properties": {"question_over_size": {"type": "boolean", "title": "问题超长提示"}, "repeated": {"type": "array", "title": "内容重复信息", "items": {"type": "string"}}}}, "aiRspGetTextFile": {"type": "object", "properties": {"item": {"$ref": "#/definitions/aiCollectionTextFile"}}}, "aiRspGetTextFileTip": {"type": "object", "title": "获取文件文本知识提示响应", "properties": {"repeated": {"type": "array", "title": "内容重复信息", "items": {"type": "string"}}, "state": {"title": "解析状态", "$ref": "#/definitions/aiDocState"}, "table_over_size": {"type": "array", "title": "表头过长的表格", "items": {"type": "object", "$ref": "#/definitions/aiTextFileTipTableOverSize"}}}}, "aiRspImportQAs": {"type": "object"}, "aiRspListAssistant": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiAssistant"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListAssistantCanShareDoc": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListAssistantCanShareDocSharedAssistant"}}}}, "aiRspListAssistantCanShareDocSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "aiRspListChat": {"type": "object", "properties": {"chats": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiChat"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListChatLiveAgent": {"type": "object", "properties": {"chat_live_agent": {"$ref": "#/definitions/aiChatLiveAgentInfo"}}}, "aiRspListCollection": {"type": "object", "properties": {"collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollection"}}}}, "aiRspListCollectionFileName": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListCollectionFileNameItem"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListCollectionFileNameItem": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "文件id"}, "name": {"type": "string", "title": "文件名称"}, "url": {"type": "string", "title": "文件绑定的url/path"}}}, "aiRspListContributor": {"type": "object", "properties": {"contributors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}}}, "aiRspListCustomLabel": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListDocByRef": {"type": "object", "properties": {"docs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspListDocByRefDoc"}}}}, "aiRspListOperator": {"type": "object", "properties": {"operators": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiOperator"}}}}, "aiRspListQA": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionQA"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListTextFiles": {"type": "object", "properties": {"fail_parse_count": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiCollectionTextFile"}}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspListeDocShareConfigSender": {"type": "object", "properties": {"assistants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiRspListeDocShareConfigSenderSharedAssistant"}}}}, "aiRspListeDocShareConfigSenderSharedAssistant": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_selected": {"type": "boolean"}, "name": {"type": "string"}, "name_en": {"type": "string"}}}, "aiRspManualChunkDoc": {"type": "object", "properties": {"task_id": {"type": "string", "format": "uint64", "title": "任务ID"}}}, "aiRspModifyCustomLabels": {"type": "object"}, "aiRspOnOffDocs": {"type": "object", "properties": {"async": {"type": "boolean"}, "pre_repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspOnOffDocsRepeatCollection"}}, "qa_num_exceed": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspOnOffDocsQaContainsMatchCount"}}, "repeat_collections": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspOnOffDocsRepeatCollection"}}}}, "aiRspProxyChatHtmlUrl": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyChatHtmlUrlContent"}}}}, "aiRspReparseTextFiles": {"type": "object", "properties": {"async": {"type": "boolean"}}}, "aiRspSearchChat": {"type": "object", "properties": {"is_only_search": {"type": "boolean", "title": "是否仅搜索"}, "is_op": {"type": "boolean", "title": "是否是推送运营端"}, "message": {"$ref": "#/definitions/aiEventChatMessage"}, "user_id": {"type": "string", "format": "uint64"}}}, "aiRspSearchCollection": {"type": "object", "properties": {"end": {"type": "string", "format": "date-time"}, "item": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtaiSearchCollectionItem"}}, "match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "start": {"type": "string", "format": "date-time"}, "total_count": {"type": "integer", "format": "int64"}}}, "aiRspSwitchChatLiveAgent": {"type": "object", "properties": {"state": {"title": "切换结果", "$ref": "#/definitions/aiSwitchChatState"}}}, "aiRspValidateQAs": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspValidateQAsErr"}}}}, "aiSearchCollectionType": {"type": "integer", "format": "int32", "title": "- 1: 向量搜索\n - 2: 文本搜索", "enum": [1, 2]}, "aiSearchEngineOption": {"type": "object", "title": "搜索引擎选项", "properties": {"name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}, "value": {"type": "string", "title": "值"}}}, "aiSwitchChatState": {"type": "integer", "format": "int32", "title": "- 1: 切换成功\n - 2: 会话已结束\n - 3: 人工坐席离线\n - 4: 会话信息错误（需要重新开启一个会话或者再问一个问题）\n - 5: 人工坐席不存在", "enum": [1, 2, 3, 4, 5]}, "aiTaskOperationType": {"type": "integer", "format": "int32", "title": "- 1: 导出\n - 2: 导入", "enum": [1, 2]}, "aiTextFileTipTableOverSize": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64", "title": "助手 id"}, "assistant_name": {"type": "string", "title": "助手中文名称"}, "assistant_name_en": {"type": "string", "title": "助手英文名称"}, "header": {"type": "string", "title": "表头"}, "table_title": {"type": "string", "title": "表格标题"}}}, "aiTextRecallPattern": {"description": "- 1: 短语匹配\n - 2: 字匹配\n - 3: 英文模糊匹配", "type": "integer", "format": "int32", "title": "关键词召回模式", "enum": [1, 2, 3]}, "aiTextRecallQuery": {"description": "- 1: 在知识库的\"QA\"中召回\n - 2: 仅在知识库的\"Q\"中召回", "type": "integer", "format": "int32", "title": "QA关键词召回目标", "enum": [1, 2]}, "aiVisibleChainOption": {"type": "object", "title": "链路查询选项", "properties": {"field": {"type": "string", "title": "字段"}, "name": {"type": "string", "title": "名称"}, "name_en": {"type": "string", "title": "英文名称"}, "uncheck": {"type": "boolean", "title": "是否默认不选中"}}}, "baseBoolEnum": {"description": "- 1: false\n - 2: true", "type": "integer", "format": "int32", "title": "bool值的枚举", "enum": [1, 2]}, "baseCrudType": {"description": "- 1: 创建\n - 2: 读取\n - 3: 更新\n - 4: 删除", "type": "integer", "format": "int32", "title": "数据操作类型", "enum": [1, 2, 3, 4]}, "baseDataType": {"description": "- 1: 团队\n - 2: 产品\n - 3: 资源\n - 4: 图谱\n - 5: 定向推送\n - 6: 用户个人\n - 7: 图谱AI\n - 8: 帮助中心文档\n - 9: AI助手", "type": "integer", "format": "int32", "title": "数据类型", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, "baseDisableState": {"description": "- 1: 启用\n - 2: 禁用", "type": "integer", "format": "int32", "title": "禁用状态", "enum": [1, 2]}, "baseIdentity": {"type": "object", "title": "身份", "properties": {"extra_id": {"type": "string", "format": "uint64", "title": "额外ID（团队类型表示用户ID）"}, "identity_id": {"type": "string", "format": "uint64", "title": "身份ID"}, "identity_type": {"title": "身份类型", "$ref": "#/definitions/baseIdentityType"}, "name": {"type": "string", "title": "名字"}}}, "baseIdentityType": {"description": "- 1: 门户端用户\n - 2: 团队\n - 3: 运营端用户\n - 4: 自定义", "type": "integer", "format": "int32", "title": "身份类型", "enum": [1, 2, 3, 4]}, "baseOrderBy": {"type": "object", "title": "排序", "properties": {"column": {"type": "string", "title": "列名"}, "desc": {"type": "boolean", "title": "是否倒序"}}}, "baseRegion": {"description": "- 1: 国内\n - 2: 海外", "type": "integer", "format": "int32", "title": "地域", "enum": [1, 2]}, "baseTcloudCaptcha": {"type": "object", "title": "腾讯云验证码参数", "properties": {"randstr": {"type": "string", "title": "前端回调函数返回的随机字符串"}, "ticket": {"type": "string", "title": "前端回调函数返回的用户验证票据"}}}, "baseTimeRange": {"type": "object", "title": "时间范围", "properties": {"end": {"type": "string", "format": "date-time", "title": "结束时间"}, "start": {"type": "string", "format": "date-time", "title": "开始时间"}}}, "baseUgcState": {"description": "- 1: 草稿。用户前台：草稿；运营后台：草稿\n - 2: 自动审核中。用户前台：处理中；运营后台：处理中\n - 3: 人工审核中（用户前台：处理中；运营后台：待处理）\n - 4: 人工审核中-可疑（用户前台：处理中；运营后台：可疑，待处理）\n - 5: 审核已通过（用户前台：下架存档；运营后台：下架存档）\n - 6: 已发布（用户前台：已发布；运营后台：已发布）\n - 7: 审核已驳回（用户前台：已驳回；运营后台：已驳回）\n - 8: 申诉中（用户前台：申诉中；运营后台：申诉待处理）", "type": "integer", "format": "int32", "title": "UGC状态", "enum": [1, 2, 3, 4, 5, 6, 7, 8]}, "errorsAiError": {"description": "- 16001: QA中的问题已经存在\n - 16002: 用户反馈已被采用\n - 16003: 用户反馈已标记已读\n - 16004: 用户反馈状态不允许被采用\n - 16005: 用户chat不存在\n - 16006: 非法的文档状态转换\n - 16007: 问题审核失败\n - 16008: 非法的AI租户\n - 16009: 非法的文档内容状态\n - 16010: doc中的文本/文件已经存在\n - 16011: 非法的自定义列转换\n - 16012: 助手不存在\n - 16013: 助手名称已存在\n - 16014: 助手英文名称已存在\n - 16015: 助手路由已存在\n - 16016: 助手已禁用\n - 16017: 助手当前功能已禁用\n - 16018: 助手应用ID已存在\n - 16019: 小程序code无效\n - 16020: 不是知识的贡献者\n - 16021: 助手客服用户名重复\n - 16022: 文档有运行中的分段任务\n - 16023: doc外部源Token已过期\n - 16024: 文件剪存文件夹不存在", "type": "integer", "format": "int32", "title": "AI服务错误\n范围：[16000, 17000)", "enum": [16001, 16002, 16003, 16004, 16005, 16006, 16007, 16008, 16009, 16010, 16011, 16012, 16013, 16014, 16015, 16016, 16017, 16018, 16019, 16020, 16021, 16022, 16023, 16024]}, "graphProcessInfo": {"type": "object", "properties": {"category": {"type": "integer", "format": "int64", "title": "流程类别 1图谱 2资源 3团队 4产品"}, "create_date": {"type": "string", "format": "date-time", "title": "创建时间"}, "creator": {"type": "string", "title": "创建人"}, "id": {"type": "string", "format": "uint64"}, "is_online_version": {"type": "integer", "format": "int64"}, "lang": {"type": "integer", "format": "int64"}, "process_name": {"type": "string", "title": "流程名称"}, "remark": {"type": "string", "title": "备注"}, "update_date": {"type": "string", "format": "date-time", "title": "修改时间"}, "update_user": {"type": "string"}, "yaml_config": {"type": "string"}}}, "graphReqCreateProcessEngine": {"type": "object", "title": "流程引擎添加请求", "properties": {"category": {"type": "integer", "format": "int64", "title": "流程类别 1图谱 2资源 3团队 4产品"}, "lang": {"type": "integer", "format": "int64"}, "process_name": {"type": "string", "title": "流程名称"}, "remark": {"type": "string", "title": "备注"}, "yaml_config": {"type": "string", "title": "YAML配置"}}}, "graphReqDeleteProcessEngine": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "graphReqDescribeListProcess": {"type": "object", "title": "流程配置列表请求", "properties": {"id": {"type": "string", "format": "uint64"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}, "search_name": {"type": "string", "title": "搜索名称"}}}, "graphReqModifyEnableVersion": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_online_version": {"type": "boolean", "title": "1启用 2禁用"}}}, "graphReqModifyProcessEngine": {"type": "object", "title": "流程引擎编辑请求", "properties": {"category": {"type": "integer", "format": "int64", "title": "流程类别"}, "id": {"type": "string", "format": "uint64"}, "lang": {"type": "integer", "format": "int64"}, "process_name": {"type": "string", "title": "流程名称"}, "remark": {"type": "string", "title": "备注"}, "yaml_config": {"type": "string", "title": "YAML配置"}}}, "graphRspDescribeListProcess": {"type": "object", "title": "流程配置列表响应", "properties": {"processes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/graphProcessInfo"}}, "total_count": {"type": "integer", "format": "int64"}}}, "iamReqDisableUser": {"type": "object", "properties": {"user_id": {"type": "array", "title": "用户ID", "items": {"type": "string", "format": "uint64"}}}}, "iamReqEnableUser": {"type": "object", "properties": {"user_id": {"type": "array", "title": "用户ID", "items": {"type": "string", "format": "uint64"}}}}, "iamRspDisableUser": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamRspDisableUserResult"}}}}, "iamRspDisableUserResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "iamRspEnableUser": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/iamRspEnableUserResult"}}}}, "iamRspEnableUserResult": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "iamUserInfo": {"type": "object", "title": "用户信息", "properties": {"city": {"type": "string", "title": "市"}, "country": {"type": "string", "title": "国家"}, "firm_id": {"type": "string", "format": "uint64", "title": "团队ID"}, "id": {"type": "string", "format": "uint64", "title": "用户ID"}, "identity_set": {"title": "身份", "$ref": "#/definitions/baseIdentityType"}, "image": {"type": "string", "title": "头像"}, "level": {"type": "string", "title": "等级"}, "nick_name": {"type": "string", "title": "用户昵称"}, "phone": {"type": "string", "title": "手机号"}, "phone_hash": {"type": "string", "title": "手机号哈希"}, "province": {"type": "string", "title": "省"}, "region_code": {"type": "string", "title": "地区编码"}, "timezone": {"type": "string", "title": "时区"}, "union_id": {"type": "string", "title": "微信unionID"}, "username": {"type": "string", "title": "用户名"}}}, "mgmtOpUser": {"type": "object", "title": "运营端用户", "properties": {"create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "email": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "last_update_by": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string", "format": "date-time"}, "phone_num": {"type": "string"}, "remark_name": {"type": "string"}, "state": {"type": "integer", "format": "int32"}, "username": {"type": "string"}}}, "mgmtReqGetRoles": {"type": "object", "properties": {"name": {"type": "string"}, "state": {"type": "string", "format": "uint64"}, "type": {"type": "string", "format": "uint64"}}}, "mgmtReqLogin": {"type": "object", "properties": {"password": {"type": "string", "title": "密码"}, "tcloud_captcha": {"title": "腾讯云验证码参数", "$ref": "#/definitions/baseTcloudCaptcha"}, "username": {"type": "string", "title": "用户名"}}}, "mgmtReqSearchUsers": {"type": "object", "properties": {"keyword": {"type": "string", "title": "搜索关键词"}, "limit": {"type": "integer", "format": "int64", "title": "分页大小"}, "offset": {"type": "integer", "format": "int64", "title": "分页偏移量"}}}, "mgmtReqTextTranslate": {"type": "object", "properties": {"source_language": {"type": "string"}, "target_language": {"type": "string"}, "text": {"type": "string"}}}, "mgmtRspGetRoles": {"type": "object", "properties": {"roles": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivebff_mgmtmgmtOpRole"}}}}, "mgmtRspLogin": {"type": "object", "properties": {"user_info": {"$ref": "#/definitions/RspLoginUserInfo"}}}, "mgmtRspSearchUsers": {"type": "object", "properties": {"total_count": {"type": "integer", "format": "int64", "title": "总数"}, "users": {"type": "array", "title": "用户列表", "items": {"type": "object", "$ref": "#/definitions/mgmtOpUser"}}}}, "mgmtRspTextTranslate": {"type": "object", "properties": {"text": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}, "message": {"type": "string"}}}, "searchReqDescribeSearchPrompts": {"type": "object", "properties": {"language": {"type": "string", "title": "语言类型zh、en"}}}, "searchReqDescribeTeamAtlasSearchOptions": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/ReqDescribeTeamAtlasSearchOptionsFilter"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "array", "items": {"type": "string"}}}}, "searchReqModifySearchPrompt": {"type": "object", "properties": {"content": {"type": "string", "title": "可以为空，代表将内容置空"}, "id": {"type": "string", "format": "int64"}}}, "searchReqModifyTeamAtlasSearchOption": {"type": "object", "properties": {"operation": {"$ref": "#/definitions/baseCrudType"}, "option": {"$ref": "#/definitions/searchSearchOption"}}}, "searchRspDescribeSearchPrompts": {"type": "object", "properties": {"prompts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/searchSearchPrompt"}}}}, "searchRspDescribeTeamAtlasSearchOptions": {"type": "object", "properties": {"options": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspDescribeTeamAtlasSearchOptionsTeamSearchOption"}}, "total_count": {"type": "integer", "format": "int64"}}}, "searchSearchOption": {"type": "object", "title": "搜索筛选项", "properties": {"id": {"type": "string", "format": "int64"}, "language": {"type": "string"}, "name": {"type": "string"}, "refer_id": {"type": "string", "format": "int64"}, "status": {"$ref": "#/definitions/baseDisableState"}, "weight": {"type": "integer", "format": "int32"}}}, "searchSearchPrompt": {"type": "object", "title": "搜索提示语", "properties": {"content": {"type": "string"}, "id": {"type": "string", "format": "int64"}, "language": {"type": "string"}, "refer_type": {"type": "string", "title": "搜索对应的筛选项类型"}, "target_type": {"type": "string", "title": "搜索对应的模块类型"}}}, "supportReqGetHashIds": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "supportReqProxy": {"type": "object", "properties": {"urls": {"type": "array", "items": {"type": "string"}}}}, "supportRspGetHashIds": {"type": "object", "properties": {"hash_ids": {"type": "array", "items": {"type": "string"}}}}, "supportRspProxy": {"type": "object", "properties": {"contents": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspProxyContent"}}}}, "tagReqBatchImportSystemTag": {"type": "object", "properties": {"notify_type": {"title": "定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1", "$ref": "#/definitions/tagTagCreateType"}, "remarks": {"type": "string", "title": "备注"}, "tag_index": {"type": "array", "title": "导入标签索引数组", "items": {"type": "object", "$ref": "#/definitions/tagReqBatchImportSystemTagTagIndex"}}, "taggable_type": {"title": "标签类型", "$ref": "#/definitions/tagTaggableType"}, "type": {"title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1", "$ref": "#/definitions/tagTagCreateType"}}}, "tagReqBatchImportSystemTagTagIndex": {"type": "object", "properties": {"index_name": {"type": "string", "title": "索引名称"}, "tag_name": {"type": "string", "title": "标签名称"}}}, "tagReqCreatePersonalTagBinding": {"type": "object", "properties": {"tag_ids": {"type": "array", "title": "绑定现有的标签", "items": {"type": "string", "format": "uint64"}}, "tag_names": {"type": "array", "title": "新增的自创标签", "items": {"type": "string"}}, "user_id": {"type": "string", "format": "uint64"}}}, "tagReqCreateTeamUserTagBinding": {"type": "object", "properties": {"tag_ids": {"type": "array", "title": "绑定现有的标签", "items": {"type": "string", "format": "uint64"}}, "tag_names": {"type": "array", "title": "新增的自创标签", "items": {"type": "string"}}, "team_id": {"type": "string", "format": "uint64"}}}, "tagReqDeleteSystemTag": {"type": "object", "properties": {"tag_index_ids": {"type": "array", "title": "标签索引数组，会删除索引下的所有标签", "items": {"type": "string", "format": "uint64"}}, "taggable_type": {"title": "标签索引类型", "$ref": "#/definitions/tagTaggableType"}}}, "tagReqDescribeAssistantTagBindingInfos": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeAtlasTagBindingInfos": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/tagReqDescribeAtlasTagBindingInfosFilter"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}}, "tagReqDescribeAtlasTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeProductTagBindingInfos": {"type": "object", "title": "ReqDescribeProductTagBindingInfos 查询产品技术-面向用户标签关联详情", "properties": {"filter": {"$ref": "#/definitions/tagReqDescribeProductTagBindingInfosFilter"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}}, "tagReqDescribeProductTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeResourceTagBindingInfos": {"type": "object", "title": "ReqDescribeResourceTagBindingInfos 查询资源相关标签关联详情", "properties": {"filter": {"$ref": "#/definitions/tagReqDescribeResourceTagBindingInfosFilter"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}}, "tagReqDescribeResourceTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqDescribeSystemTagsByIndex": {"type": "object", "properties": {"filter": {"$ref": "#/definitions/tagReqDescribeSystemTagsByIndexFilter"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "order_by": {"type": "string"}}}, "tagReqDescribeSystemTagsByIndexFilter": {"type": "object", "properties": {"index_ids": {"type": "array", "title": "标签索引id", "items": {"type": "string", "format": "uint64"}}, "language": {"type": "string", "title": "语言类型"}, "notify_type": {"title": "定向推送标签类型（我来自标签时） 1 系统 ；2 自创", "$ref": "#/definitions/tagTagCreateType"}, "tag_name": {"type": "string", "title": "标签名称"}, "taggable_type": {"title": "标签索引类型", "$ref": "#/definitions/tagTaggableType"}, "type": {"title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创", "$ref": "#/definitions/tagTagCreateType"}}}, "tagReqDescribeTeamTagBindingInfos": {"type": "object", "title": "ReqDescribeTeamTagBindingInfos 查询团队类型标签关联详情", "properties": {"filter": {"$ref": "#/definitions/tagReqDescribeTeamTagBindingInfosFilter"}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}}, "tagReqDescribeTeamTagBindingInfosFilter": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}}}, "tagReqEditSystemTag": {"type": "object", "properties": {"en_tag": {"title": "英文标签信息", "$ref": "#/definitions/tanlivebff_mgmttagTag"}, "index_id": {"type": "string", "format": "uint64", "title": "标签索引id 为0 则创建，否则更新对应数据"}, "index_name": {"type": "string", "title": "标签索引名称"}, "taggable_type": {"title": "标签索引类型", "$ref": "#/definitions/tagTaggableType"}, "zh_tag": {"title": "中文标签信息", "$ref": "#/definitions/tanlivebff_mgmttagTag"}}}, "tagReqGetNotifyDataByUserLabel": {"type": "object", "properties": {"tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "taggable_type": {"$ref": "#/definitions/tagTaggableType"}}}, "tagReqGetSystemTags": {"type": "object", "properties": {"language": {"type": "string", "title": "语言类型zh、en"}, "notify_type": {"title": "定向推送用的筛选条件 1 系统标签", "$ref": "#/definitions/tagTagCreateType"}, "order_by": {"type": "array", "title": "排序", "items": {"type": "string"}}, "tag_name": {"type": "string", "title": "标签名称模糊搜索"}, "taggable_type": {"title": "标签索引类型", "$ref": "#/definitions/tagTaggableType"}, "with_assistant_user_tag": {"type": "boolean", "title": "是否包含 助手用户标签"}}}, "tagReqMergeSystemTags": {"type": "object", "properties": {"en_tag": {"title": "合并后英文标签信息", "$ref": "#/definitions/tanlivebff_mgmttagTag"}, "index_id": {"type": "string", "format": "uint64", "title": "合并后的标签索引id"}, "merge_index_ids": {"type": "array", "title": "需要合并标签索引数组", "items": {"type": "string", "format": "uint64"}}, "taggable_type": {"title": "合并的标签类型", "$ref": "#/definitions/tagTaggableType"}, "zh_tag": {"title": "合并后的中文标签信息", "$ref": "#/definitions/tanlivebff_mgmttagTag"}}}, "tagReqUpdateSystemTag": {"type": "object", "properties": {"notify_type": {"title": "定向推送标签类型（我来自标签时） 1 系统 ；2 自创", "$ref": "#/definitions/tagTagCreateType"}, "tag_id": {"type": "string", "format": "uint64", "title": "标签id"}, "taggable_type": {"title": "标签索引类型", "$ref": "#/definitions/tagTaggableType"}, "type": {"title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创", "$ref": "#/definitions/tagTagCreateType"}, "weight": {"type": "integer", "format": "int64", "title": "标签权重"}}}, "tagRspDescribeAssistantTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RspDescribeAssistantTagBindingInfosInfo"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeAtlasTagBindingInfos": {"type": "object", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeAtlasTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeAtlasTagBindingInfosBindingObject": {"type": "object", "properties": {"atlas_id": {"type": "string", "format": "uint64", "title": "图谱id"}, "atlas_introduction": {"type": "string", "title": "简述"}, "atlas_name": {"type": "string", "title": "标题"}, "atlas_types": {"type": "array", "title": "图谱类型", "items": {"type": "string"}}, "create_date": {"type": "string", "title": "创建时间"}, "id": {"type": "string", "format": "uint64"}, "pub_name": {"type": "array", "title": "发布方", "items": {"type": "string"}}}}, "tagRspDescribeProductTagBindingInfos": {"type": "object", "title": "RspDescribeProductTagBindingInfos 查询产品技术-面向用户类型标签关联详情回复", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeProductTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeProductTagBindingInfosBindingObject": {"type": "object", "properties": {"brief_intro": {"type": "string", "title": "一句话简介"}, "create_date": {"type": "string", "title": "创建时间"}, "id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64", "title": "产品id"}, "product_name": {"type": "string", "title": "产品名称"}, "product_types": {"type": "array", "title": "产品类型", "items": {"type": "string"}}, "team_short_name": {"type": "string", "title": "团队简称"}}}, "tagRspDescribeResourceTagBindingInfos": {"type": "object", "title": "RspDescribeResourceTagBindingInfos 查询资源相关标签关联详情回复", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeResourceTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeResourceTagBindingInfosBindingObject": {"type": "object", "properties": {"create_date": {"type": "string", "title": "创建时间"}, "id": {"type": "string", "format": "uint64"}, "originator_name": {"type": "array", "title": "主办方", "items": {"type": "string"}}, "res_id": {"type": "string", "format": "uint64", "title": "资源id"}, "res_introduction": {"type": "string", "title": "资源简述"}, "res_name": {"type": "string", "title": "资源标题"}, "res_types": {"type": "array", "title": "资源类型", "items": {"type": "string"}}}}, "tagRspDescribeSystemTagsByIndex": {"type": "object", "properties": {"tag_set": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tanlivetagTagIndex"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeTeamTagBindingInfos": {"type": "object", "title": "RspDescribeTeamTagBindingInfos 查询团队类型标签关联详情回复", "properties": {"binding_objects": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspDescribeTeamTagBindingInfosBindingObject"}}, "total_count": {"type": "integer", "format": "int64"}}}, "tagRspDescribeTeamTagBindingInfosBindingObject": {"type": "object", "properties": {"contributors": {"type": "array", "title": "贡献者", "items": {"type": "string"}}, "create_date": {"type": "string", "title": "创建时间"}, "holder_id": {"type": "string", "format": "uint64", "title": "团队持有人id"}, "holder_name": {"type": "string", "title": "团队持有人"}, "id": {"type": "string", "format": "uint64"}, "team_id": {"type": "string", "format": "uint64", "title": "团队id"}, "team_name": {"type": "string", "title": "团队名称"}, "team_nature": {"type": "string", "title": "团队属性"}, "team_short_name": {"type": "string", "title": "团队简称"}, "team_types": {"type": "array", "title": "团队类型", "items": {"type": "string"}}, "verify_status": {"type": "string", "title": "认证状态"}}}, "tagRspGetNotifyDataByUserLabel": {"type": "object", "properties": {"data_infos": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspGetNotifyDataByUserLabelDataInfo"}}}}, "tagRspGetNotifyDataByUserLabelDataInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "is_international": {"type": "boolean"}, "name": {"type": "string"}}}, "tagRspGetSystemTags": {"type": "object", "properties": {"tag_set": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/tagRspGetSystemTagsTag"}}}}, "tagRspGetSystemTagsTag": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "name": {"type": "string", "title": "标签名称"}}}, "tagTagCreateType": {"description": "- 1: 系统标签\n - 2: 自创标签", "type": "integer", "format": "int32", "title": "TagCreateType 标签创建类型枚举", "enum": [1, 2]}, "tagTagInfo": {"type": "object", "properties": {"allow_delete": {"type": "boolean", "title": "是否允许删除"}, "citation_num": {"type": "integer", "format": "int64", "title": "引用数量"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uint64", "title": "标签id"}, "is_tmt": {"type": "boolean", "title": "是否tmt"}, "language": {"type": "string", "title": "语言类型"}, "last_update_by": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string", "format": "date-time"}, "name": {"type": "string", "title": "标签名称"}, "notify_type": {"title": "定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创", "$ref": "#/definitions/tagTagCreateType"}, "type": {"title": "标签类型（多语言选项或注册用） 1 系统 ；2 自创", "$ref": "#/definitions/tagTagCreateType"}, "weight": {"type": "integer", "format": "int64", "title": "标签权重"}}}, "tagTaggableType": {"description": "- 1: 1 个人信息-我来自\n - 2: 2 团队类型\n - 3: 3 团队属性\n - 4: 4 团队行业\n - 5: 5 资源受众行业\n - 6: 6 产品技术-面向用户（场景）\n - 7: 7 资源类型\n - 8: 8 图谱-适用行业\n - 9: 9 图谱-适用场景\n - 10: 10 图谱类型\n - 11: 11 产品行业认可 --2.2迭代将 21资源-产品行业认可合并入11\n - 12: 12 团队行业认可 --2.2迭代将 22资源-团队行业认可合并入12\n - 14: 14 产品适用行业\n - 23: 23 资源系列\n - 101: 101 团队发展阶段\n - 102: 102 团队产品类型\n - 103: 103 受众团队融资阶段\n - 200: 200 后端用于业务及联的特殊类型，前端忽略\n - 201: 201 个人用户-用户标签\n - 202: 202 团队用户-用户标签\n - 203: 203 AI助手-给用户打标", "type": "integer", "format": "int32", "title": "标签类型", "enum": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 23, 101, 102, 103, 200, 201, 202, 203]}, "tanliveaiChatMessage": {"type": "object", "properties": {"answer_draft_id": {"type": "string", "format": "uint64", "title": "answer草稿id"}, "answer_index": {"type": "integer", "format": "int32", "title": "多任务索引"}, "ask_type": {"$ref": "#/definitions/aiQuestionAskType"}, "assistant_id": {"type": "string", "format": "uint64"}, "chat_id": {"type": "string", "format": "uint64"}, "collection_snapshot": {"$ref": "#/definitions/aiMessageCollectionSnapshot"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string", "format": "date-time"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "doc_names": {"type": "array", "items": {"type": "string"}}, "doc_snapshot": {"title": "doc 快照", "$ref": "#/definitions/aiMessageDocSnapshot"}, "end_time": {"type": "string", "format": "date-time"}, "feedback_id": {"type": "string", "format": "uint64", "title": "教学反馈ID"}, "files": {"type": "array", "title": "问题附件", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageFile"}}, "final_query": {"type": "string"}, "final_search_query": {"type": "string"}, "has_user_feedback": {"type": "boolean", "title": "是否有用户反馈"}, "history_ignore_id": {"type": "string", "format": "uint64"}, "id": {"type": "string", "format": "uint64"}, "image_url": {"type": "array", "items": {"type": "string"}}, "is_file_ready": {"type": "boolean", "title": "文件解析成功"}, "lang": {"type": "string"}, "last_operation_type": {"title": "最后一次操作", "$ref": "#/definitions/aiChatOperationType"}, "last_operator": {"title": "最后一次操作", "$ref": "#/definitions/aiChatMessageOperator"}, "link": {"type": "string"}, "live_agent_name": {"type": "string"}, "logs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiChatMessageLog"}}, "prompt_prefix": {"type": "string"}, "prompt_type": {"type": "string", "title": "问题类型"}, "publish_hash_id": {"type": "string", "title": "推送消息hash id"}, "question_id": {"type": "string", "format": "uint64"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "ref_file_names": {"type": "array", "items": {"type": "string"}}, "reject_reason": {"type": "string"}, "show_type": {"type": "integer", "format": "int32"}, "sql_query": {"type": "string"}, "start_time": {"type": "string", "format": "date-time"}, "state": {"$ref": "#/definitions/aiChatMessageState"}, "suggest_question": {"type": "array", "items": {"type": "string"}}, "suggestion_mode": {"$ref": "#/definitions/aiAskSuggestionMode"}, "task": {"title": "当前所执行的任务", "$ref": "#/definitions/aiChatMessageTask"}, "text": {"type": "string"}, "think": {"type": "string", "title": "深度思考"}, "think_duration": {"type": "integer", "format": "int32"}, "type": {"$ref": "#/definitions/aiChatMessageType"}, "ugcs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiMessageUgc"}}, "wait_answer": {"type": "boolean"}}}, "tanliveaiChatMessageDoc": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "data_type": {"type": "integer", "format": "int64"}, "doc_match_pattern": {"$ref": "#/definitions/aiDocMatchPattern"}, "file_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "index_text": {"type": "string"}, "rag_filename": {"type": "string"}, "reference": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiDocReference"}}, "text": {"type": "string"}, "ugc_id": {"type": "string", "format": "uint64"}, "ugc_type": {"$ref": "#/definitions/baseDataType"}, "update_by": {"$ref": "#/definitions/tanliveaiOperator"}, "url": {"type": "string"}}}, "tanliveaiOperator": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户账户: 用户id\n团队账户: 用户id\n运营端账户: 运营端用户id"}, "type": {"$ref": "#/definitions/baseIdentityType"}, "user_id": {"type": "string", "format": "uint64", "title": "type为团队用户时，可选的传入个人的 id"}}}, "tanliveaiSearchCollectionItem": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "doc_name": {"type": "string"}, "doc_type": {"title": "文件类型", "$ref": "#/definitions/aiDocType"}, "file_name": {"type": "string"}, "id": {"type": "string"}, "is_related": {"type": "boolean", "title": "是否相关"}, "question": {"type": "string"}, "ref_name": {"type": "string"}, "ref_url": {"type": "string"}, "score": {"type": "number", "format": "float"}, "text": {"type": "string"}, "type": {"title": "召回类型", "$ref": "#/definitions/aiSearchCollectionType"}, "update_by": {"$ref": "#/definitions/tanliveaiOperator"}, "url": {"type": "string"}}}, "tanlivebff_mgmtaiChat": {"type": "object", "properties": {"assistant_id": {"type": "string", "format": "uint64"}, "assistant_name": {"type": "string"}, "avg_duration": {"type": "number", "format": "float"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "create_date": {"type": "string", "format": "date-time"}, "doc_hits": {"type": "number", "format": "float"}, "id": {"type": "string", "format": "uint64"}, "is_manual": {"type": "integer", "format": "int32", "title": "是否转过人工服务"}, "labels": {"type": "array", "title": "自定义标签kv对", "items": {"type": "object", "$ref": "#/definitions/aiCustomLabel"}}, "question_cnt": {"type": "integer", "format": "int64", "title": "对话中问题数量"}, "rating_scale": {"$ref": "#/definitions/aiRatingScale"}, "region_code": {"type": "string"}, "reject_job_result": {"type": "integer", "format": "int64"}, "support_type": {"title": "当前服务状态", "$ref": "#/definitions/aiChatSupportType"}, "title": {"type": "string"}, "update_date": {"type": "string", "format": "date-time"}}}, "tanlivebff_mgmtaiChatDetail": {"type": "object", "properties": {"assistant_avatar": {"type": "string", "title": "微信客服助手头像"}, "assistant_id": {"type": "string", "format": "uint64"}, "chat_state": {"$ref": "#/definitions/aiChatCurrentState"}, "chat_type": {"$ref": "#/definitions/aiChatType"}, "create_by": {"$ref": "#/definitions/iamUserInfo"}, "create_date": {"type": "string", "format": "date-time"}, "finish_date": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uint64"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiEventChatMessage"}}, "records": {"type": "array", "title": "非web端时通过次字段返回消息详情", "items": {"type": "object", "$ref": "#/definitions/aiChatSendRecordInfo"}}, "region": {"title": "地区", "$ref": "#/definitions/baseRegion"}, "support_type": {"title": "当前服务状态", "$ref": "#/definitions/aiChatSupportType"}, "title": {"type": "string"}}}, "tanlivebff_mgmtaiOperator": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户id"}, "team_name": {"type": "string", "title": "用户所属团队名称"}, "type": {"$ref": "#/definitions/baseIdentityType"}, "user_id": {"type": "string", "format": "uint64", "title": "用户id，只有为团队用户时，才需要"}, "username": {"type": "string", "title": "用户名称"}}}, "tanlivebff_mgmtaiSearchCollectionItem": {"type": "object", "properties": {"contributor": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/aiContributor"}}, "data_source": {"$ref": "#/definitions/aiDocDataSource"}, "doc_id": {"type": "string", "format": "uint64"}, "doc_name": {"type": "string"}, "doc_type": {"title": "文件类型", "$ref": "#/definitions/aiDocType"}, "file_name": {"type": "string"}, "id": {"type": "string"}, "is_related": {"type": "boolean", "title": "是否相关"}, "question": {"type": "string"}, "score": {"type": "number", "format": "float"}, "text": {"type": "string"}, "type": {"title": "召回类型", "$ref": "#/definitions/aiSearchCollectionType"}, "update_by": {"$ref": "#/definitions/tanlivebff_mgmtaiOperator"}, "url": {"type": "string"}}}, "tanlivebff_mgmtmgmtOpRole": {"type": "object", "title": "角色", "properties": {"account_count": {"type": "string", "format": "uint64"}, "could_del": {"type": "boolean"}, "create_by": {"type": "string", "format": "uint64"}, "create_date": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "last_update_by": {"type": "string", "format": "uint64"}, "last_update_date": {"type": "string"}, "role_name": {"type": "string"}, "state": {"type": "string", "format": "uint64"}, "type": {"type": "string", "format": "uint64"}}}, "tanlivebff_mgmttagTag": {"type": "object", "properties": {"is_tmt": {"type": "boolean", "title": "是否tmt"}, "name": {"type": "string", "title": "标签名称"}, "notify_type": {"title": "定向推送标签类型（编辑我来自标签时） 1 系统 ；2 自创，默认1", "$ref": "#/definitions/tagTagCreateType"}, "type": {"title": "// 语言\n string language = 3 ;\n标签类型（多语言选项或注册用） 1 系统 ；2 自创，默认1", "$ref": "#/definitions/tagTagCreateType"}, "weight": {"type": "integer", "format": "int64", "title": "标签id\n uint64 id = 1 ;\n标签权重"}}}, "tanliveiamUserCard": {"type": "object", "title": "用户卡片", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "image": {"type": "string", "title": "头像"}, "level": {"type": "string", "title": "等级"}, "username": {"type": "string", "title": "用户名"}}}, "tanlivemgmtUserCard": {"type": "object", "title": "运营端用户卡片", "properties": {"id": {"type": "string", "format": "uint64", "title": "用户ID"}, "remark_name": {"type": "string", "title": "备注名"}, "username": {"type": "string", "title": "用户名"}}}, "tanlivetagTagIndex": {"type": "object", "properties": {"en_tag": {"title": "英文标签信息", "$ref": "#/definitions/tagTagInfo"}, "id": {"type": "string", "format": "uint64", "title": "标签索引id"}, "name": {"type": "string", "title": "标签索引名称"}, "zh_tag": {"title": "中文标签信息", "$ref": "#/definitions/tagTagInfo"}}}, "teamTeamCard": {"type": "object", "title": "团队卡片", "properties": {"brief_intro": {"type": "string", "title": "一句话介绍"}, "full_name": {"type": "string", "title": "主体名称"}, "id": {"type": "string", "format": "uint64", "title": "团队ID"}, "is_published": {"type": "boolean", "title": "是否已发布"}, "is_verified": {"type": "boolean", "title": "是否认证"}, "level": {"title": "共创等级", "$ref": "#/definitions/teamTeamLevel"}, "logo_url": {"type": "string", "title": "团队LOGO"}, "short_name": {"type": "string", "title": "简称"}}}, "teamTeamLevel": {"description": "- 1: CONTRIBUTOR\n - 2: COMMITTER\n - 3: MAIN<PERSON>INER", "type": "integer", "format": "int32", "title": "团队共创等级", "enum": [1, 2, 3]}}, "tags": [{"name": "Au<PERSON><PERSON><PERSON>"}, {"name": "MgmtBff"}]}