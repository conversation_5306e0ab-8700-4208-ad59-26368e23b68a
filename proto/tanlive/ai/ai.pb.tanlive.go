// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package ai

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Url": "required,max=1024,url",
	}, &UrlReference{})
}

func (x *UrlReference) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text": "required,max=1024",
	}, &TextReference{})
}

func (x *TextReference) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FilePath": "required,max=1024",
		"FileName": "required,max=4096",
	}, &FileReference{})
}

func (x *FileReference) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"TextValue": "max=512",
	}, &LabelValue{})
}

func (x *LabelValue) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Value": "required,omitempty",
	}, &CustomLabel{})
}

func (x *CustomLabel) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Url":  "required",
		"Text": "required",
	}, &AssistantKefuReply_Custom{})
}

func (x *AssistantKefuReply_Custom) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required,dive,required",
		"Chunks":      "required,dive,required",
	}, &AssistantChunks{})
}

func (x *AssistantChunks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Len": "min=1",
	}, &ChunkItem{})
}

func (x *ChunkItem) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantChunks": "omitempty,dive,required",
	}, &ManualChunkPara{})
}

func (x *ManualChunkPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required,dive,required",
		"ChunkConfig": "required",
	}, &AutoChunkPara{})
}

func (x *AutoChunkPara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Channel": "required",
	}, &AiAssistantNoticeConf{})
}

func (x *AiAssistantNoticeConf) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Zh": "required",
		"En": "required",
	}, &AiAssistantNoticeConf_Notice{})
}

func (x *AiAssistantNoticeConf_Notice) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ChatMessageContentFilterItem) MaskInLog() any {
	if x == nil {
		return (*ChatMessageContentFilterItem)(nil)
	}

	y := proto.Clone(x).(*ChatMessageContentFilterItem)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*MessageTag)
		}
	}

	return y
}

func (x *ChatMessageContentFilterItem) MaskInRpc() any {
	if x == nil {
		return (*ChatMessageContentFilterItem)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*MessageTag)
		}
	}

	return y
}

func (x *ChatMessageContentFilterItem) MaskInBff() any {
	if x == nil {
		return (*ChatMessageContentFilterItem)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*MessageTag)
		}
	}

	return y
}

func (x *ChatMessageContent) MaskInLog() any {
	if x == nil {
		return (*ChatMessageContent)(nil)
	}

	y := proto.Clone(x).(*ChatMessageContent)
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Filter[k] = vv.MaskInLog().(*ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Ugcs[k] = vv.MaskInLog().(*MessageUgc)
		}
	}

	return y
}

func (x *ChatMessageContent) MaskInRpc() any {
	if x == nil {
		return (*ChatMessageContent)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Filter[k] = vv.MaskInRpc().(*ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Ugcs[k] = vv.MaskInRpc().(*MessageUgc)
		}
	}

	return y
}

func (x *ChatMessageContent) MaskInBff() any {
	if x == nil {
		return (*ChatMessageContent)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Filter[k] = vv.MaskInBff().(*ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Ugcs[k] = vv.MaskInBff().(*MessageUgc)
		}
	}

	return y
}

func (x *MessageUgc) MaskInLog() any {
	if x == nil {
		return (*MessageUgc)(nil)
	}

	y := proto.Clone(x).(*MessageUgc)
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Filter[k] = vv.MaskInLog().(*ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Cards[k] = vv.MaskInLog().(*MessageUgcCard)
		}
	}

	return y
}

func (x *MessageUgc) MaskInRpc() any {
	if x == nil {
		return (*MessageUgc)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Filter[k] = vv.MaskInRpc().(*ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Cards[k] = vv.MaskInRpc().(*MessageUgcCard)
		}
	}

	return y
}

func (x *MessageUgc) MaskInBff() any {
	if x == nil {
		return (*MessageUgc)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Filter[k] = vv.MaskInBff().(*ChatMessageContentFilterItem)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Cards[k] = vv.MaskInBff().(*MessageUgcCard)
		}
	}

	return y
}

func (x *ChatPushMessage) MaskInLog() any {
	if x == nil {
		return (*ChatPushMessage)(nil)
	}

	y := proto.Clone(x).(*ChatPushMessage)
	if v, ok := any(y.HashMsg).(interface{ MaskInLog() any }); ok {
		y.HashMsg = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *ChatPushMessage) MaskInRpc() any {
	if x == nil {
		return (*ChatPushMessage)(nil)
	}

	y := x
	if v, ok := any(y.HashMsg).(interface{ MaskInRpc() any }); ok {
		y.HashMsg = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *ChatPushMessage) MaskInBff() any {
	if x == nil {
		return (*ChatPushMessage)(nil)
	}

	y := x
	if v, ok := any(y.HashMsg).(interface{ MaskInBff() any }); ok {
		y.HashMsg = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *ChatMessage) MaskInLog() any {
	if x == nil {
		return (*ChatMessage)(nil)
	}

	y := proto.Clone(x).(*ChatMessage)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Ugcs[k] = vv.MaskInLog().(*MessageUgc)
		}
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInLog() any }); ok {
		y.CollectionSnapshot = v.MaskInLog().(*MessageCollectionSnapshot)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*ChatMessageLog)
		}
	}
	if v, ok := any(y.Task).(interface{ MaskInLog() any }); ok {
		y.Task = v.MaskInLog().(*ChatMessageTask)
	}
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*ChatMessageFile)
		}
	}
	if v, ok := any(y.LastOperator).(interface{ MaskInLog() any }); ok {
		y.LastOperator = v.MaskInLog().(*ChatMessageOperator)
	}
	if v, ok := any(y.DocSnapshot).(interface{ MaskInLog() any }); ok {
		y.DocSnapshot = v.MaskInLog().(*MessageDocSnapshot)
	}

	return y
}

func (x *ChatMessage) MaskInRpc() any {
	if x == nil {
		return (*ChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Ugcs[k] = vv.MaskInRpc().(*MessageUgc)
		}
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInRpc() any }); ok {
		y.CollectionSnapshot = v.MaskInRpc().(*MessageCollectionSnapshot)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*ChatMessageLog)
		}
	}
	if v, ok := any(y.Task).(interface{ MaskInRpc() any }); ok {
		y.Task = v.MaskInRpc().(*ChatMessageTask)
	}
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*ChatMessageFile)
		}
	}
	if v, ok := any(y.LastOperator).(interface{ MaskInRpc() any }); ok {
		y.LastOperator = v.MaskInRpc().(*ChatMessageOperator)
	}
	if v, ok := any(y.DocSnapshot).(interface{ MaskInRpc() any }); ok {
		y.DocSnapshot = v.MaskInRpc().(*MessageDocSnapshot)
	}

	return y
}

func (x *ChatMessage) MaskInBff() any {
	if x == nil {
		return (*ChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Ugcs[k] = vv.MaskInBff().(*MessageUgc)
		}
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInBff() any }); ok {
		y.CollectionSnapshot = v.MaskInBff().(*MessageCollectionSnapshot)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*ChatMessageLog)
		}
	}
	if v, ok := any(y.Task).(interface{ MaskInBff() any }); ok {
		y.Task = v.MaskInBff().(*ChatMessageTask)
	}
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*ChatMessageFile)
		}
	}
	if v, ok := any(y.LastOperator).(interface{ MaskInBff() any }); ok {
		y.LastOperator = v.MaskInBff().(*ChatMessageOperator)
	}
	if v, ok := any(y.DocSnapshot).(interface{ MaskInBff() any }); ok {
		y.DocSnapshot = v.MaskInBff().(*MessageDocSnapshot)
	}

	return y
}

func (x *QuestionsWithAnswer) MaskInLog() any {
	if x == nil {
		return (*QuestionsWithAnswer)(nil)
	}

	y := proto.Clone(x).(*QuestionsWithAnswer)
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Questions[k] = vv.MaskInLog().(*ChatMessage)
		}
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Answers[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *QuestionsWithAnswer) MaskInRpc() any {
	if x == nil {
		return (*QuestionsWithAnswer)(nil)
	}

	y := x
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Questions[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Answers[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *QuestionsWithAnswer) MaskInBff() any {
	if x == nil {
		return (*QuestionsWithAnswer)(nil)
	}

	y := x
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Questions[k] = vv.MaskInBff().(*ChatMessage)
		}
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Answers[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *ChatSendRecordInfo) MaskInLog() any {
	if x == nil {
		return (*ChatSendRecordInfo)(nil)
	}

	y := proto.Clone(x).(*ChatSendRecordInfo)
	if v, ok := any(y.SendDate).(interface{ MaskInLog() any }); ok {
		y.SendDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatSendRecordInfo) MaskInRpc() any {
	if x == nil {
		return (*ChatSendRecordInfo)(nil)
	}

	y := x
	if v, ok := any(y.SendDate).(interface{ MaskInRpc() any }); ok {
		y.SendDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatSendRecordInfo) MaskInBff() any {
	if x == nil {
		return (*ChatSendRecordInfo)(nil)
	}

	y := x
	if v, ok := any(y.SendDate).(interface{ MaskInBff() any }); ok {
		y.SendDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *EventChatMessage) MaskInLog() any {
	if x == nil {
		return (*EventChatMessage)(nil)
	}

	y := proto.Clone(x).(*EventChatMessage)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Ugcs[k] = vv.MaskInLog().(*MessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInLog() any }); ok {
		y.ProcessTime = v.MaskInLog().(*base.TimeRange)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Answers[k] = vv.MaskInLog().(*EventChatMessage)
		}
	}
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*ChatMessageFile)
		}
	}

	return y
}

func (x *EventChatMessage) MaskInRpc() any {
	if x == nil {
		return (*EventChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Ugcs[k] = vv.MaskInRpc().(*MessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInRpc() any }); ok {
		y.ProcessTime = v.MaskInRpc().(*base.TimeRange)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Answers[k] = vv.MaskInRpc().(*EventChatMessage)
		}
	}
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*ChatMessageFile)
		}
	}

	return y
}

func (x *EventChatMessage) MaskInBff() any {
	if x == nil {
		return (*EventChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Ugcs[k] = vv.MaskInBff().(*MessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInBff() any }); ok {
		y.ProcessTime = v.MaskInBff().(*base.TimeRange)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Answers[k] = vv.MaskInBff().(*EventChatMessage)
		}
	}
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*ChatMessageFile)
		}
	}

	return y
}

func (x *EventChatHashMessage) MaskInLog() any {
	if x == nil {
		return (*EventChatHashMessage)(nil)
	}

	y := proto.Clone(x).(*EventChatHashMessage)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*EventChatHashMessage_EventMessageDoc)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Ugcs[k] = vv.MaskInLog().(*EventChatHashMessage_EventMessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInLog() any }); ok {
		y.ProcessTime = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *EventChatHashMessage) MaskInRpc() any {
	if x == nil {
		return (*EventChatHashMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*EventChatHashMessage_EventMessageDoc)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Ugcs[k] = vv.MaskInRpc().(*EventChatHashMessage_EventMessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInRpc() any }); ok {
		y.ProcessTime = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *EventChatHashMessage) MaskInBff() any {
	if x == nil {
		return (*EventChatHashMessage)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*EventChatHashMessage_EventMessageDoc)
		}
	}
	for k, v := range y.Ugcs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Ugcs[k] = vv.MaskInBff().(*EventChatHashMessage_EventMessageUgc)
		}
	}
	if v, ok := any(y.ProcessTime).(interface{ MaskInBff() any }); ok {
		y.ProcessTime = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *EventChatHashMessage_EventMessageUgc) MaskInLog() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageUgc)(nil)
	}

	y := proto.Clone(x).(*EventChatHashMessage_EventMessageUgc)
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Filter[k] = vv.MaskInLog().(*EventChatHashMessage_EventMessageFilter)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Cards[k] = vv.MaskInLog().(*EventChatHashMessage_EventMessageUgcCard)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageUgc) MaskInRpc() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageUgc)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Filter[k] = vv.MaskInRpc().(*EventChatHashMessage_EventMessageFilter)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Cards[k] = vv.MaskInRpc().(*EventChatHashMessage_EventMessageUgcCard)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageUgc) MaskInBff() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageUgc)(nil)
	}

	y := x
	for k, v := range y.Filter {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Filter[k] = vv.MaskInBff().(*EventChatHashMessage_EventMessageFilter)
		}
	}
	for k, v := range y.Cards {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Cards[k] = vv.MaskInBff().(*EventChatHashMessage_EventMessageUgcCard)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageFilter) MaskInLog() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageFilter)(nil)
	}

	y := proto.Clone(x).(*EventChatHashMessage_EventMessageFilter)
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tags[k] = vv.MaskInLog().(*EventChatHashMessage_EventMessageTag)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageFilter) MaskInRpc() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageFilter)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tags[k] = vv.MaskInRpc().(*EventChatHashMessage_EventMessageTag)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageFilter) MaskInBff() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageFilter)(nil)
	}

	y := x
	for k, v := range y.Tags {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tags[k] = vv.MaskInBff().(*EventChatHashMessage_EventMessageTag)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageDoc) MaskInLog() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageDoc)(nil)
	}

	y := proto.Clone(x).(*EventChatHashMessage_EventMessageDoc)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*EventChatHashMessage_EventContributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageDoc) MaskInRpc() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageDoc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*EventChatHashMessage_EventContributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}

	return y
}

func (x *EventChatHashMessage_EventMessageDoc) MaskInBff() any {
	if x == nil {
		return (*EventChatHashMessage_EventMessageDoc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*EventChatHashMessage_EventContributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}

	return y
}

func (x *ChatMessageDoc) MaskInLog() any {
	if x == nil {
		return (*ChatMessageDoc)(nil)
	}

	y := proto.Clone(x).(*ChatMessageDoc)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ChatMessageDoc) MaskInRpc() any {
	if x == nil {
		return (*ChatMessageDoc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ChatMessageDoc) MaskInBff() any {
	if x == nil {
		return (*ChatMessageDoc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ChatMessageLog) MaskInLog() any {
	if x == nil {
		return (*ChatMessageLog)(nil)
	}

	y := proto.Clone(x).(*ChatMessageLog)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.FetchRespTime).(interface{ MaskInLog() any }); ok {
		y.FetchRespTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatMessageLog) MaskInRpc() any {
	if x == nil {
		return (*ChatMessageLog)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.FetchRespTime).(interface{ MaskInRpc() any }); ok {
		y.FetchRespTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatMessageLog) MaskInBff() any {
	if x == nil {
		return (*ChatMessageLog)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.FetchRespTime).(interface{ MaskInBff() any }); ok {
		y.FetchRespTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatAgentTask) MaskInLog() any {
	if x == nil {
		return (*ChatAgentTask)(nil)
	}

	y := proto.Clone(x).(*ChatAgentTask)
	if v, ok := any(y.SuggestionConfig).(interface{ MaskInLog() any }); ok {
		y.SuggestionConfig = v.MaskInLog().(*AssistantAskSuggestionConfig)
	}

	return y
}

func (x *ChatAgentTask) MaskInRpc() any {
	if x == nil {
		return (*ChatAgentTask)(nil)
	}

	y := x
	if v, ok := any(y.SuggestionConfig).(interface{ MaskInRpc() any }); ok {
		y.SuggestionConfig = v.MaskInRpc().(*AssistantAskSuggestionConfig)
	}

	return y
}

func (x *ChatAgentTask) MaskInBff() any {
	if x == nil {
		return (*ChatAgentTask)(nil)
	}

	y := x
	if v, ok := any(y.SuggestionConfig).(interface{ MaskInBff() any }); ok {
		y.SuggestionConfig = v.MaskInBff().(*AssistantAskSuggestionConfig)
	}

	return y
}

func (x *ChatSuggestLog) MaskInLog() any {
	if x == nil {
		return (*ChatSuggestLog)(nil)
	}

	y := proto.Clone(x).(*ChatSuggestLog)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatSuggestLog) MaskInRpc() any {
	if x == nil {
		return (*ChatSuggestLog)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatSuggestLog) MaskInBff() any {
	if x == nil {
		return (*ChatSuggestLog)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *Chat) MaskInLog() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := proto.Clone(x).(*Chat)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}

	return y
}

func (x *Chat) MaskInRpc() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}

	return y
}

func (x *Chat) MaskInBff() any {
	if x == nil {
		return (*Chat)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}

	return y
}

func (x *ChatDetail) MaskInLog() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := proto.Clone(x).(*ChatDetail)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatDetail) MaskInRpc() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatDetail) MaskInBff() any {
	if x == nil {
		return (*ChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TextFile) MaskInLog() any {
	if x == nil {
		return (*TextFile)(nil)
	}

	y := proto.Clone(x).(*TextFile)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*Operator)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}
	for k, v := range y.ShareReceivers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ShareReceivers[k] = vv.MaskInLog().(*DocShareReceiver)
		}
	}

	return y
}

func (x *TextFile) MaskInRpc() any {
	if x == nil {
		return (*TextFile)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*Operator)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}
	for k, v := range y.ShareReceivers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ShareReceivers[k] = vv.MaskInRpc().(*DocShareReceiver)
		}
	}

	return y
}

func (x *TextFile) MaskInBff() any {
	if x == nil {
		return (*TextFile)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*Assistant)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*Operator)
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*DocAssistantState)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}
	for k, v := range y.ShareReceivers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ShareReceivers[k] = vv.MaskInBff().(*DocShareReceiver)
		}
	}

	return y
}

func (x *FullTextFile) MaskInLog() any {
	if x == nil {
		return (*FullTextFile)(nil)
	}

	y := proto.Clone(x).(*FullTextFile)
	if v, ok := any(y.Doc).(interface{ MaskInLog() any }); ok {
		y.Doc = v.MaskInLog().(*TextFile)
	}
	for k, v := range y.Copies {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Copies[k] = vv.MaskInLog().(*TextFile)
		}
	}

	return y
}

func (x *FullTextFile) MaskInRpc() any {
	if x == nil {
		return (*FullTextFile)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInRpc() any }); ok {
		y.Doc = v.MaskInRpc().(*TextFile)
	}
	for k, v := range y.Copies {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Copies[k] = vv.MaskInRpc().(*TextFile)
		}
	}

	return y
}

func (x *FullTextFile) MaskInBff() any {
	if x == nil {
		return (*FullTextFile)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInBff() any }); ok {
		y.Doc = v.MaskInBff().(*TextFile)
	}
	for k, v := range y.Copies {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Copies[k] = vv.MaskInBff().(*TextFile)
		}
	}

	return y
}

func (x *QA) MaskInLog() any {
	if x == nil {
		return (*QA)(nil)
	}

	y := proto.Clone(x).(*QA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*Assistant)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*Operator)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}
	for k, v := range y.ShareReceivers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ShareReceivers[k] = vv.MaskInLog().(*DocShareReceiver)
		}
	}

	return y
}

func (x *QA) MaskInRpc() any {
	if x == nil {
		return (*QA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*Assistant)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*Operator)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}
	for k, v := range y.ShareReceivers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ShareReceivers[k] = vv.MaskInRpc().(*DocShareReceiver)
		}
	}

	return y
}

func (x *QA) MaskInBff() any {
	if x == nil {
		return (*QA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*Assistant)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*Operator)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}
	for k, v := range y.ShareReceivers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ShareReceivers[k] = vv.MaskInBff().(*DocShareReceiver)
		}
	}

	return y
}

func (x *FeedbackComment) MaskInLog() any {
	if x == nil {
		return (*FeedbackComment)(nil)
	}

	y := proto.Clone(x).(*FeedbackComment)
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*FeedbackComment_File)
		}
	}

	return y
}

func (x *FeedbackComment) MaskInRpc() any {
	if x == nil {
		return (*FeedbackComment)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*FeedbackComment_File)
		}
	}

	return y
}

func (x *FeedbackComment) MaskInBff() any {
	if x == nil {
		return (*FeedbackComment)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*FeedbackComment_File)
		}
	}

	return y
}

func (x *FullFeedback) MaskInLog() any {
	if x == nil {
		return (*FullFeedback)(nil)
	}

	y := proto.Clone(x).(*FullFeedback)
	if v, ok := any(y.Feedback).(interface{ MaskInLog() any }); ok {
		y.Feedback = v.MaskInLog().(*Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInLog() any }); ok {
		y.OriginalQuestion = v.MaskInLog().(*ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInLog() any }); ok {
		y.OriginalAnswer = v.MaskInLog().(*ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *FullFeedback) MaskInRpc() any {
	if x == nil {
		return (*FullFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInRpc() any }); ok {
		y.Feedback = v.MaskInRpc().(*Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInRpc() any }); ok {
		y.OriginalQuestion = v.MaskInRpc().(*ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInRpc() any }); ok {
		y.OriginalAnswer = v.MaskInRpc().(*ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *FullFeedback) MaskInBff() any {
	if x == nil {
		return (*FullFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Feedback).(interface{ MaskInBff() any }); ok {
		y.Feedback = v.MaskInBff().(*Feedback)
	}
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*FeedbackReference)
		}
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInBff() any }); ok {
		y.OriginalQuestion = v.MaskInBff().(*ChatMessage)
	}
	if v, ok := any(y.OriginalAnswer).(interface{ MaskInBff() any }); ok {
		y.OriginalAnswer = v.MaskInBff().(*ChatMessage)
	}
	for k, v := range y.ExpectedDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedDocs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}
	for k, v := range y.ExpectedMgmtDocs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExpectedMgmtDocs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *Feedback) MaskInLog() any {
	if x == nil {
		return (*Feedback)(nil)
	}

	y := proto.Clone(x).(*Feedback)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.OpComment).(interface{ MaskInLog() any }); ok {
		y.OpComment = v.MaskInLog().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInLog() any }); ok {
		y.MgmtFeedback = v.MaskInLog().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInLog() any }); ok {
		y.MgmtComment = v.MaskInLog().(*FeedbackComment)
	}
	if v, ok := any(y.UserFeedbackBy).(interface{ MaskInLog() any }); ok {
		y.UserFeedbackBy = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.OpFeedbackBy).(interface{ MaskInLog() any }); ok {
		y.OpFeedbackBy = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.MgmtFeedbackBy).(interface{ MaskInLog() any }); ok {
		y.MgmtFeedbackBy = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.UserFeedbackAt).(interface{ MaskInLog() any }); ok {
		y.UserFeedbackAt = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.OpFeedbackAt).(interface{ MaskInLog() any }); ok {
		y.OpFeedbackAt = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.MgmtFeedbackAt).(interface{ MaskInLog() any }); ok {
		y.MgmtFeedbackAt = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateIdentity).(interface{ MaskInLog() any }); ok {
		y.CreateIdentity = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.UpdateIdentity).(interface{ MaskInLog() any }); ok {
		y.UpdateIdentity = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *Feedback) MaskInRpc() any {
	if x == nil {
		return (*Feedback)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.OpComment).(interface{ MaskInRpc() any }); ok {
		y.OpComment = v.MaskInRpc().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInRpc() any }); ok {
		y.MgmtFeedback = v.MaskInRpc().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInRpc() any }); ok {
		y.MgmtComment = v.MaskInRpc().(*FeedbackComment)
	}
	if v, ok := any(y.UserFeedbackBy).(interface{ MaskInRpc() any }); ok {
		y.UserFeedbackBy = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.OpFeedbackBy).(interface{ MaskInRpc() any }); ok {
		y.OpFeedbackBy = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.MgmtFeedbackBy).(interface{ MaskInRpc() any }); ok {
		y.MgmtFeedbackBy = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.UserFeedbackAt).(interface{ MaskInRpc() any }); ok {
		y.UserFeedbackAt = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.OpFeedbackAt).(interface{ MaskInRpc() any }); ok {
		y.OpFeedbackAt = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.MgmtFeedbackAt).(interface{ MaskInRpc() any }); ok {
		y.MgmtFeedbackAt = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateIdentity).(interface{ MaskInRpc() any }); ok {
		y.CreateIdentity = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.UpdateIdentity).(interface{ MaskInRpc() any }); ok {
		y.UpdateIdentity = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *Feedback) MaskInBff() any {
	if x == nil {
		return (*Feedback)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.OpComment).(interface{ MaskInBff() any }); ok {
		y.OpComment = v.MaskInBff().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInBff() any }); ok {
		y.MgmtFeedback = v.MaskInBff().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInBff() any }); ok {
		y.MgmtComment = v.MaskInBff().(*FeedbackComment)
	}
	if v, ok := any(y.UserFeedbackBy).(interface{ MaskInBff() any }); ok {
		y.UserFeedbackBy = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.OpFeedbackBy).(interface{ MaskInBff() any }); ok {
		y.OpFeedbackBy = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.MgmtFeedbackBy).(interface{ MaskInBff() any }); ok {
		y.MgmtFeedbackBy = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.UserFeedbackAt).(interface{ MaskInBff() any }); ok {
		y.UserFeedbackAt = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.OpFeedbackAt).(interface{ MaskInBff() any }); ok {
		y.OpFeedbackAt = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.MgmtFeedbackAt).(interface{ MaskInBff() any }); ok {
		y.MgmtFeedbackAt = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateIdentity).(interface{ MaskInBff() any }); ok {
		y.CreateIdentity = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.UpdateIdentity).(interface{ MaskInBff() any }); ok {
		y.UpdateIdentity = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *FeedbackReference) MaskInLog() any {
	if x == nil {
		return (*FeedbackReference)(nil)
	}

	y := proto.Clone(x).(*FeedbackReference)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FeedbackReference) MaskInRpc() any {
	if x == nil {
		return (*FeedbackReference)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FeedbackReference) MaskInBff() any {
	if x == nil {
		return (*FeedbackReference)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *TypedReference) MaskInLog() any {
	if x == nil {
		return (*TypedReference)(nil)
	}

	y := proto.Clone(x).(*TypedReference)
	switch v := y.Reference.(type) {
	case *TypedReference_Url:
		if vv, ok := any(v.Url).(interface{ MaskInLog() any }); ok {
			v.Url = vv.MaskInLog().(*UrlReference)
		}
	case *TypedReference_Text:
		if vv, ok := any(v.Text).(interface{ MaskInLog() any }); ok {
			v.Text = vv.MaskInLog().(*TextReference)
		}
	case *TypedReference_File:
		if vv, ok := any(v.File).(interface{ MaskInLog() any }); ok {
			v.File = vv.MaskInLog().(*FileReference)
		}
	}

	return y
}

func (x *TypedReference) MaskInRpc() any {
	if x == nil {
		return (*TypedReference)(nil)
	}

	y := x
	switch v := y.Reference.(type) {
	case *TypedReference_Url:
		if vv, ok := any(v.Url).(interface{ MaskInRpc() any }); ok {
			v.Url = vv.MaskInRpc().(*UrlReference)
		}
	case *TypedReference_Text:
		if vv, ok := any(v.Text).(interface{ MaskInRpc() any }); ok {
			v.Text = vv.MaskInRpc().(*TextReference)
		}
	case *TypedReference_File:
		if vv, ok := any(v.File).(interface{ MaskInRpc() any }); ok {
			v.File = vv.MaskInRpc().(*FileReference)
		}
	}

	return y
}

func (x *TypedReference) MaskInBff() any {
	if x == nil {
		return (*TypedReference)(nil)
	}

	y := x
	switch v := y.Reference.(type) {
	case *TypedReference_Url:
		if vv, ok := any(v.Url).(interface{ MaskInBff() any }); ok {
			v.Url = vv.MaskInBff().(*UrlReference)
		}
	case *TypedReference_Text:
		if vv, ok := any(v.Text).(interface{ MaskInBff() any }); ok {
			v.Text = vv.MaskInBff().(*TextReference)
		}
	case *TypedReference_File:
		if vv, ok := any(v.File).(interface{ MaskInBff() any }); ok {
			v.File = vv.MaskInBff().(*FileReference)
		}
	}

	return y
}

func (x *FeedbackLog) MaskInLog() any {
	if x == nil {
		return (*FeedbackLog)(nil)
	}

	y := proto.Clone(x).(*FeedbackLog)
	if v, ok := any(y.CreateIdentity).(interface{ MaskInLog() any }); ok {
		y.CreateIdentity = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FeedbackLog) MaskInRpc() any {
	if x == nil {
		return (*FeedbackLog)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInRpc() any }); ok {
		y.CreateIdentity = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FeedbackLog) MaskInBff() any {
	if x == nil {
		return (*FeedbackLog)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInBff() any }); ok {
		y.CreateIdentity = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FullFeedbackLog) MaskInLog() any {
	if x == nil {
		return (*FullFeedbackLog)(nil)
	}

	y := proto.Clone(x).(*FullFeedbackLog)
	if v, ok := any(y.Log).(interface{ MaskInLog() any }); ok {
		y.Log = v.MaskInLog().(*FeedbackLog)
	}
	if v, ok := any(y.Feedback).(interface{ MaskInLog() any }); ok {
		y.Feedback = v.MaskInLog().(*Feedback)
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInLog() any }); ok {
		y.OriginalQuestion = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *FullFeedbackLog) MaskInRpc() any {
	if x == nil {
		return (*FullFeedbackLog)(nil)
	}

	y := x
	if v, ok := any(y.Log).(interface{ MaskInRpc() any }); ok {
		y.Log = v.MaskInRpc().(*FeedbackLog)
	}
	if v, ok := any(y.Feedback).(interface{ MaskInRpc() any }); ok {
		y.Feedback = v.MaskInRpc().(*Feedback)
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInRpc() any }); ok {
		y.OriginalQuestion = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *FullFeedbackLog) MaskInBff() any {
	if x == nil {
		return (*FullFeedbackLog)(nil)
	}

	y := x
	if v, ok := any(y.Log).(interface{ MaskInBff() any }); ok {
		y.Log = v.MaskInBff().(*FeedbackLog)
	}
	if v, ok := any(y.Feedback).(interface{ MaskInBff() any }); ok {
		y.Feedback = v.MaskInBff().(*Feedback)
	}
	if v, ok := any(y.OriginalQuestion).(interface{ MaskInBff() any }); ok {
		y.OriginalQuestion = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *LabelFilter) MaskInLog() any {
	if x == nil {
		return (*LabelFilter)(nil)
	}

	y := proto.Clone(x).(*LabelFilter)
	if v, ok := any(y.Eq).(interface{ MaskInLog() any }); ok {
		y.Eq = v.MaskInLog().(*LabelValue)
	}
	if v, ok := any(y.Gte).(interface{ MaskInLog() any }); ok {
		y.Gte = v.MaskInLog().(*LabelValue)
	}
	if v, ok := any(y.Lte).(interface{ MaskInLog() any }); ok {
		y.Lte = v.MaskInLog().(*LabelValue)
	}
	for k, v := range y.In {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.In[k] = vv.MaskInLog().(*LabelValue)
		}
	}
	if v, ok := any(y.Like).(interface{ MaskInLog() any }); ok {
		y.Like = v.MaskInLog().(*LabelValue)
	}

	return y
}

func (x *LabelFilter) MaskInRpc() any {
	if x == nil {
		return (*LabelFilter)(nil)
	}

	y := x
	if v, ok := any(y.Eq).(interface{ MaskInRpc() any }); ok {
		y.Eq = v.MaskInRpc().(*LabelValue)
	}
	if v, ok := any(y.Gte).(interface{ MaskInRpc() any }); ok {
		y.Gte = v.MaskInRpc().(*LabelValue)
	}
	if v, ok := any(y.Lte).(interface{ MaskInRpc() any }); ok {
		y.Lte = v.MaskInRpc().(*LabelValue)
	}
	for k, v := range y.In {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.In[k] = vv.MaskInRpc().(*LabelValue)
		}
	}
	if v, ok := any(y.Like).(interface{ MaskInRpc() any }); ok {
		y.Like = v.MaskInRpc().(*LabelValue)
	}

	return y
}

func (x *LabelFilter) MaskInBff() any {
	if x == nil {
		return (*LabelFilter)(nil)
	}

	y := x
	if v, ok := any(y.Eq).(interface{ MaskInBff() any }); ok {
		y.Eq = v.MaskInBff().(*LabelValue)
	}
	if v, ok := any(y.Gte).(interface{ MaskInBff() any }); ok {
		y.Gte = v.MaskInBff().(*LabelValue)
	}
	if v, ok := any(y.Lte).(interface{ MaskInBff() any }); ok {
		y.Lte = v.MaskInBff().(*LabelValue)
	}
	for k, v := range y.In {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.In[k] = vv.MaskInBff().(*LabelValue)
		}
	}
	if v, ok := any(y.Like).(interface{ MaskInBff() any }); ok {
		y.Like = v.MaskInBff().(*LabelValue)
	}

	return y
}

func (x *CustomLabel) MaskInLog() any {
	if x == nil {
		return (*CustomLabel)(nil)
	}

	y := proto.Clone(x).(*CustomLabel)
	if v, ok := any(y.Value).(interface{ MaskInLog() any }); ok {
		y.Value = v.MaskInLog().(*LabelValue)
	}

	return y
}

func (x *CustomLabel) MaskInRpc() any {
	if x == nil {
		return (*CustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Value).(interface{ MaskInRpc() any }); ok {
		y.Value = v.MaskInRpc().(*LabelValue)
	}

	return y
}

func (x *CustomLabel) MaskInBff() any {
	if x == nil {
		return (*CustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Value).(interface{ MaskInBff() any }); ok {
		y.Value = v.MaskInBff().(*LabelValue)
	}

	return y
}

func (x *Assistant) MaskInLog() any {
	if x == nil {
		return (*Assistant)(nil)
	}

	y := proto.Clone(x).(*Assistant)
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Collections[k] = vv.MaskInLog().(*Collection)
		}
	}

	return y
}

func (x *Assistant) MaskInRpc() any {
	if x == nil {
		return (*Assistant)(nil)
	}

	y := x
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Collections[k] = vv.MaskInRpc().(*Collection)
		}
	}

	return y
}

func (x *Assistant) MaskInBff() any {
	if x == nil {
		return (*Assistant)(nil)
	}

	y := x
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Collections[k] = vv.MaskInBff().(*Collection)
		}
	}

	return y
}

func (x *AssistantDetail) MaskInLog() any {
	if x == nil {
		return (*AssistantDetail)(nil)
	}

	y := proto.Clone(x).(*AssistantDetail)
	if v, ok := any(y.WelcomeMsg).(interface{ MaskInLog() any }); ok {
		y.WelcomeMsg = v.MaskInLog().(*AssistantWelcomeMsg)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.NoPermissionMsg).(interface{ MaskInLog() any }); ok {
		y.NoPermissionMsg = v.MaskInLog().(*AssistantDefaultConfigMsg)
	}
	if v, ok := any(y.DefaultPushMsg).(interface{ MaskInLog() any }); ok {
		y.DefaultPushMsg = v.MaskInLog().(*AssistantConfigMsg)
	}
	if v, ok := any(y.RatingScaleMsg).(interface{ MaskInLog() any }); ok {
		y.RatingScaleMsg = v.MaskInLog().(*AssistantRatingScaleMsg)
	}
	if v, ok := any(y.LiveAgentMsg).(interface{ MaskInLog() any }); ok {
		y.LiveAgentMsg = v.MaskInLog().(*AssistantLiverAgentConfigMsg)
	}
	if v, ok := any(y.MultimodalPrompt).(interface{ MaskInLog() any }); ok {
		y.MultimodalPrompt = v.MaskInLog().(*AssistantMultimodalPrompt)
	}
	if v, ok := any(y.QuestionTypeConfig).(interface{ MaskInLog() any }); ok {
		y.QuestionTypeConfig = v.MaskInLog().(*AssistantQuestionTypeConfig)
	}
	if v, ok := any(y.SuggestionConfig).(interface{ MaskInLog() any }); ok {
		y.SuggestionConfig = v.MaskInLog().(*AssistantAskSuggestionConfig)
	}

	return y
}

func (x *AssistantDetail) MaskInRpc() any {
	if x == nil {
		return (*AssistantDetail)(nil)
	}

	y := x
	if v, ok := any(y.WelcomeMsg).(interface{ MaskInRpc() any }); ok {
		y.WelcomeMsg = v.MaskInRpc().(*AssistantWelcomeMsg)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.NoPermissionMsg).(interface{ MaskInRpc() any }); ok {
		y.NoPermissionMsg = v.MaskInRpc().(*AssistantDefaultConfigMsg)
	}
	if v, ok := any(y.DefaultPushMsg).(interface{ MaskInRpc() any }); ok {
		y.DefaultPushMsg = v.MaskInRpc().(*AssistantConfigMsg)
	}
	if v, ok := any(y.RatingScaleMsg).(interface{ MaskInRpc() any }); ok {
		y.RatingScaleMsg = v.MaskInRpc().(*AssistantRatingScaleMsg)
	}
	if v, ok := any(y.LiveAgentMsg).(interface{ MaskInRpc() any }); ok {
		y.LiveAgentMsg = v.MaskInRpc().(*AssistantLiverAgentConfigMsg)
	}
	if v, ok := any(y.MultimodalPrompt).(interface{ MaskInRpc() any }); ok {
		y.MultimodalPrompt = v.MaskInRpc().(*AssistantMultimodalPrompt)
	}
	if v, ok := any(y.QuestionTypeConfig).(interface{ MaskInRpc() any }); ok {
		y.QuestionTypeConfig = v.MaskInRpc().(*AssistantQuestionTypeConfig)
	}
	if v, ok := any(y.SuggestionConfig).(interface{ MaskInRpc() any }); ok {
		y.SuggestionConfig = v.MaskInRpc().(*AssistantAskSuggestionConfig)
	}

	return y
}

func (x *AssistantDetail) MaskInBff() any {
	if x == nil {
		return (*AssistantDetail)(nil)
	}

	y := x
	if v, ok := any(y.WelcomeMsg).(interface{ MaskInBff() any }); ok {
		y.WelcomeMsg = v.MaskInBff().(*AssistantWelcomeMsg)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.NoPermissionMsg).(interface{ MaskInBff() any }); ok {
		y.NoPermissionMsg = v.MaskInBff().(*AssistantDefaultConfigMsg)
	}
	if v, ok := any(y.DefaultPushMsg).(interface{ MaskInBff() any }); ok {
		y.DefaultPushMsg = v.MaskInBff().(*AssistantConfigMsg)
	}
	if v, ok := any(y.RatingScaleMsg).(interface{ MaskInBff() any }); ok {
		y.RatingScaleMsg = v.MaskInBff().(*AssistantRatingScaleMsg)
	}
	if v, ok := any(y.LiveAgentMsg).(interface{ MaskInBff() any }); ok {
		y.LiveAgentMsg = v.MaskInBff().(*AssistantLiverAgentConfigMsg)
	}
	if v, ok := any(y.MultimodalPrompt).(interface{ MaskInBff() any }); ok {
		y.MultimodalPrompt = v.MaskInBff().(*AssistantMultimodalPrompt)
	}
	if v, ok := any(y.QuestionTypeConfig).(interface{ MaskInBff() any }); ok {
		y.QuestionTypeConfig = v.MaskInBff().(*AssistantQuestionTypeConfig)
	}
	if v, ok := any(y.SuggestionConfig).(interface{ MaskInBff() any }); ok {
		y.SuggestionConfig = v.MaskInBff().(*AssistantAskSuggestionConfig)
	}

	return y
}

func (x *AssistantWelcomeMsg) MaskInLog() any {
	if x == nil {
		return (*AssistantWelcomeMsg)(nil)
	}

	y := proto.Clone(x).(*AssistantWelcomeMsg)
	if v, ok := any(y.CodeConfig).(interface{ MaskInLog() any }); ok {
		y.CodeConfig = v.MaskInLog().(*AssistantInteractiveCodeConfig)
	}

	return y
}

func (x *AssistantWelcomeMsg) MaskInRpc() any {
	if x == nil {
		return (*AssistantWelcomeMsg)(nil)
	}

	y := x
	if v, ok := any(y.CodeConfig).(interface{ MaskInRpc() any }); ok {
		y.CodeConfig = v.MaskInRpc().(*AssistantInteractiveCodeConfig)
	}

	return y
}

func (x *AssistantWelcomeMsg) MaskInBff() any {
	if x == nil {
		return (*AssistantWelcomeMsg)(nil)
	}

	y := x
	if v, ok := any(y.CodeConfig).(interface{ MaskInBff() any }); ok {
		y.CodeConfig = v.MaskInBff().(*AssistantInteractiveCodeConfig)
	}

	return y
}

func (x *AssistantLiverAgentConfigMsg) MaskInLog() any {
	if x == nil {
		return (*AssistantLiverAgentConfigMsg)(nil)
	}

	y := proto.Clone(x).(*AssistantLiverAgentConfigMsg)
	if v, ok := any(y.RemindPushMsg).(interface{ MaskInLog() any }); ok {
		y.RemindPushMsg = v.MaskInLog().(*AssistantDefaultConfigMsg)
	}
	if v, ok := any(y.Reply).(interface{ MaskInLog() any }); ok {
		y.Reply = v.MaskInLog().(*AssistantKefuReply)
	}

	return y
}

func (x *AssistantLiverAgentConfigMsg) MaskInRpc() any {
	if x == nil {
		return (*AssistantLiverAgentConfigMsg)(nil)
	}

	y := x
	if v, ok := any(y.RemindPushMsg).(interface{ MaskInRpc() any }); ok {
		y.RemindPushMsg = v.MaskInRpc().(*AssistantDefaultConfigMsg)
	}
	if v, ok := any(y.Reply).(interface{ MaskInRpc() any }); ok {
		y.Reply = v.MaskInRpc().(*AssistantKefuReply)
	}

	return y
}

func (x *AssistantLiverAgentConfigMsg) MaskInBff() any {
	if x == nil {
		return (*AssistantLiverAgentConfigMsg)(nil)
	}

	y := x
	if v, ok := any(y.RemindPushMsg).(interface{ MaskInBff() any }); ok {
		y.RemindPushMsg = v.MaskInBff().(*AssistantDefaultConfigMsg)
	}
	if v, ok := any(y.Reply).(interface{ MaskInBff() any }); ok {
		y.Reply = v.MaskInBff().(*AssistantKefuReply)
	}

	return y
}

func (x *ChatLiveAgent) MaskInLog() any {
	if x == nil {
		return (*ChatLiveAgent)(nil)
	}

	y := proto.Clone(x).(*ChatLiveAgent)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatLiveAgent) MaskInRpc() any {
	if x == nil {
		return (*ChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatLiveAgent) MaskInBff() any {
	if x == nil {
		return (*ChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatLiveAgentInfo) MaskInLog() any {
	if x == nil {
		return (*ChatLiveAgentInfo)(nil)
	}

	y := proto.Clone(x).(*ChatLiveAgentInfo)
	for k, v := range y.LiveAgents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.LiveAgents[k] = vv.MaskInLog().(*ChatLiveAgentInfo_LiveAgentInfo)
		}
	}
	if v, ok := any(y.CurrentLiveAgent).(interface{ MaskInLog() any }); ok {
		y.CurrentLiveAgent = v.MaskInLog().(*ChatLiveAgentInfo_LiveAgentInfo)
	}

	return y
}

func (x *ChatLiveAgentInfo) MaskInRpc() any {
	if x == nil {
		return (*ChatLiveAgentInfo)(nil)
	}

	y := x
	for k, v := range y.LiveAgents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.LiveAgents[k] = vv.MaskInRpc().(*ChatLiveAgentInfo_LiveAgentInfo)
		}
	}
	if v, ok := any(y.CurrentLiveAgent).(interface{ MaskInRpc() any }); ok {
		y.CurrentLiveAgent = v.MaskInRpc().(*ChatLiveAgentInfo_LiveAgentInfo)
	}

	return y
}

func (x *ChatLiveAgentInfo) MaskInBff() any {
	if x == nil {
		return (*ChatLiveAgentInfo)(nil)
	}

	y := x
	for k, v := range y.LiveAgents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.LiveAgents[k] = vv.MaskInBff().(*ChatLiveAgentInfo_LiveAgentInfo)
		}
	}
	if v, ok := any(y.CurrentLiveAgent).(interface{ MaskInBff() any }); ok {
		y.CurrentLiveAgent = v.MaskInBff().(*ChatLiveAgentInfo_LiveAgentInfo)
	}

	return y
}

func (x *SearchCollectionItem) MaskInLog() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := proto.Clone(x).(*SearchCollectionItem)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *SearchCollectionItem) MaskInRpc() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *SearchCollectionItem) MaskInBff() any {
	if x == nil {
		return (*SearchCollectionItem)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *AiChatSendRecord) MaskInLog() any {
	if x == nil {
		return (*AiChatSendRecord)(nil)
	}

	y := proto.Clone(x).(*AiChatSendRecord)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.SendDate).(interface{ MaskInLog() any }); ok {
		y.SendDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *AiChatSendRecord) MaskInRpc() any {
	if x == nil {
		return (*AiChatSendRecord)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.SendDate).(interface{ MaskInRpc() any }); ok {
		y.SendDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *AiChatSendRecord) MaskInBff() any {
	if x == nil {
		return (*AiChatSendRecord)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.SendDate).(interface{ MaskInBff() any }); ok {
		y.SendDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ExportTask) MaskInLog() any {
	if x == nil {
		return (*ExportTask)(nil)
	}

	y := proto.Clone(x).(*ExportTask)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInLog() any }); ok {
		y.LastUpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ExportTask) MaskInRpc() any {
	if x == nil {
		return (*ExportTask)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInRpc() any }); ok {
		y.LastUpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ExportTask) MaskInBff() any {
	if x == nil {
		return (*ExportTask)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastUpdateDate).(interface{ MaskInBff() any }); ok {
		y.LastUpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *FullAssistant) MaskInLog() any {
	if x == nil {
		return (*FullAssistant)(nil)
	}

	y := proto.Clone(x).(*FullAssistant)
	if v, ok := any(y.Assistant).(interface{ MaskInLog() any }); ok {
		y.Assistant = v.MaskInLog().(*AssistantV2)
	}

	return y
}

func (x *FullAssistant) MaskInRpc() any {
	if x == nil {
		return (*FullAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Assistant).(interface{ MaskInRpc() any }); ok {
		y.Assistant = v.MaskInRpc().(*AssistantV2)
	}

	return y
}

func (x *FullAssistant) MaskInBff() any {
	if x == nil {
		return (*FullAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Assistant).(interface{ MaskInBff() any }); ok {
		y.Assistant = v.MaskInBff().(*AssistantV2)
	}

	return y
}

func (x *AssistantV2) MaskInLog() any {
	if x == nil {
		return (*AssistantV2)(nil)
	}

	y := proto.Clone(x).(*AssistantV2)
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInLog() any }); ok {
		y.UpdateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*AssistantConfig)
	}

	return y
}

func (x *AssistantV2) MaskInRpc() any {
	if x == nil {
		return (*AssistantV2)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInRpc() any }); ok {
		y.UpdateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*AssistantConfig)
	}

	return y
}

func (x *AssistantV2) MaskInBff() any {
	if x == nil {
		return (*AssistantV2)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.UpdateDate).(interface{ MaskInBff() any }); ok {
		y.UpdateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*AssistantConfig)
	}

	return y
}

func (x *AssistantConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantConfig)
	if v, ok := any(y.VisibleChainConfig).(interface{ MaskInLog() any }); ok {
		y.VisibleChainConfig = v.MaskInLog().(*AssistantVisibleChainConfig)
	}
	if v, ok := any(y.FieldManageConfig).(interface{ MaskInLog() any }); ok {
		y.FieldManageConfig = v.MaskInLog().(*AssistantFieldManageConfig)
	}
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Admins[k] = vv.MaskInLog().(*base.Identity)
		}
	}
	if v, ok := any(y.ChatOrSqlConfig).(interface{ MaskInLog() any }); ok {
		y.ChatOrSqlConfig = v.MaskInLog().(*AssistantChatOrSqlConfig)
	}
	if v, ok := any(y.AskSuggestionConfig).(interface{ MaskInLog() any }); ok {
		y.AskSuggestionConfig = v.MaskInLog().(*AssistantAskSuggestionConfig)
	}
	if v, ok := any(y.WeixinChannelConfig).(interface{ MaskInLog() any }); ok {
		y.WeixinChannelConfig = v.MaskInLog().(*AssistantWeixinChannelConfig)
	}
	if v, ok := any(y.TanliveWebChannelConfig).(interface{ MaskInLog() any }); ok {
		y.TanliveWebChannelConfig = v.MaskInLog().(*AssistantTanliveWebChannelConfig)
	}
	if v, ok := any(y.TanliveAppChannelConfig).(interface{ MaskInLog() any }); ok {
		y.TanliveAppChannelConfig = v.MaskInLog().(*AssistantTanliveAppChannelConfig)
	}
	if v, ok := any(y.WhatsappChannelConfig).(interface{ MaskInLog() any }); ok {
		y.WhatsappChannelConfig = v.MaskInLog().(*AssistantWhatsappChannelConfig)
	}
	if v, ok := any(y.MiniprogramChannelConfig).(interface{ MaskInLog() any }); ok {
		y.MiniprogramChannelConfig = v.MaskInLog().(*AssistantMiniprogramChannelConfig)
	}
	if v, ok := any(y.ChunkConfig).(interface{ MaskInLog() any }); ok {
		y.ChunkConfig = v.MaskInLog().(*AssistantChunkConfig)
	}
	if v, ok := any(y.QuestionTypeConfig).(interface{ MaskInLog() any }); ok {
		y.QuestionTypeConfig = v.MaskInLog().(*AssistantQuestionTypeConfig)
	}
	if v, ok := any(y.AllowlistConfig).(interface{ MaskInLog() any }); ok {
		y.AllowlistConfig = v.MaskInLog().(*AssistantAllowlistConfig)
	}
	if v, ok := any(y.UserLabelConfig).(interface{ MaskInLog() any }); ok {
		y.UserLabelConfig = v.MaskInLog().(*AssistantUserLabelConfig)
	}

	return y
}

func (x *AssistantConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantConfig)(nil)
	}

	y := x
	if v, ok := any(y.VisibleChainConfig).(interface{ MaskInRpc() any }); ok {
		y.VisibleChainConfig = v.MaskInRpc().(*AssistantVisibleChainConfig)
	}
	if v, ok := any(y.FieldManageConfig).(interface{ MaskInRpc() any }); ok {
		y.FieldManageConfig = v.MaskInRpc().(*AssistantFieldManageConfig)
	}
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Admins[k] = vv.MaskInRpc().(*base.Identity)
		}
	}
	if v, ok := any(y.ChatOrSqlConfig).(interface{ MaskInRpc() any }); ok {
		y.ChatOrSqlConfig = v.MaskInRpc().(*AssistantChatOrSqlConfig)
	}
	if v, ok := any(y.AskSuggestionConfig).(interface{ MaskInRpc() any }); ok {
		y.AskSuggestionConfig = v.MaskInRpc().(*AssistantAskSuggestionConfig)
	}
	if v, ok := any(y.WeixinChannelConfig).(interface{ MaskInRpc() any }); ok {
		y.WeixinChannelConfig = v.MaskInRpc().(*AssistantWeixinChannelConfig)
	}
	if v, ok := any(y.TanliveWebChannelConfig).(interface{ MaskInRpc() any }); ok {
		y.TanliveWebChannelConfig = v.MaskInRpc().(*AssistantTanliveWebChannelConfig)
	}
	if v, ok := any(y.TanliveAppChannelConfig).(interface{ MaskInRpc() any }); ok {
		y.TanliveAppChannelConfig = v.MaskInRpc().(*AssistantTanliveAppChannelConfig)
	}
	if v, ok := any(y.WhatsappChannelConfig).(interface{ MaskInRpc() any }); ok {
		y.WhatsappChannelConfig = v.MaskInRpc().(*AssistantWhatsappChannelConfig)
	}
	if v, ok := any(y.MiniprogramChannelConfig).(interface{ MaskInRpc() any }); ok {
		y.MiniprogramChannelConfig = v.MaskInRpc().(*AssistantMiniprogramChannelConfig)
	}
	if v, ok := any(y.ChunkConfig).(interface{ MaskInRpc() any }); ok {
		y.ChunkConfig = v.MaskInRpc().(*AssistantChunkConfig)
	}
	if v, ok := any(y.QuestionTypeConfig).(interface{ MaskInRpc() any }); ok {
		y.QuestionTypeConfig = v.MaskInRpc().(*AssistantQuestionTypeConfig)
	}
	if v, ok := any(y.AllowlistConfig).(interface{ MaskInRpc() any }); ok {
		y.AllowlistConfig = v.MaskInRpc().(*AssistantAllowlistConfig)
	}
	if v, ok := any(y.UserLabelConfig).(interface{ MaskInRpc() any }); ok {
		y.UserLabelConfig = v.MaskInRpc().(*AssistantUserLabelConfig)
	}

	return y
}

func (x *AssistantConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantConfig)(nil)
	}

	y := x
	if v, ok := any(y.VisibleChainConfig).(interface{ MaskInBff() any }); ok {
		y.VisibleChainConfig = v.MaskInBff().(*AssistantVisibleChainConfig)
	}
	if v, ok := any(y.FieldManageConfig).(interface{ MaskInBff() any }); ok {
		y.FieldManageConfig = v.MaskInBff().(*AssistantFieldManageConfig)
	}
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Admins[k] = vv.MaskInBff().(*base.Identity)
		}
	}
	if v, ok := any(y.ChatOrSqlConfig).(interface{ MaskInBff() any }); ok {
		y.ChatOrSqlConfig = v.MaskInBff().(*AssistantChatOrSqlConfig)
	}
	if v, ok := any(y.AskSuggestionConfig).(interface{ MaskInBff() any }); ok {
		y.AskSuggestionConfig = v.MaskInBff().(*AssistantAskSuggestionConfig)
	}
	if v, ok := any(y.WeixinChannelConfig).(interface{ MaskInBff() any }); ok {
		y.WeixinChannelConfig = v.MaskInBff().(*AssistantWeixinChannelConfig)
	}
	if v, ok := any(y.TanliveWebChannelConfig).(interface{ MaskInBff() any }); ok {
		y.TanliveWebChannelConfig = v.MaskInBff().(*AssistantTanliveWebChannelConfig)
	}
	if v, ok := any(y.TanliveAppChannelConfig).(interface{ MaskInBff() any }); ok {
		y.TanliveAppChannelConfig = v.MaskInBff().(*AssistantTanliveAppChannelConfig)
	}
	if v, ok := any(y.WhatsappChannelConfig).(interface{ MaskInBff() any }); ok {
		y.WhatsappChannelConfig = v.MaskInBff().(*AssistantWhatsappChannelConfig)
	}
	if v, ok := any(y.MiniprogramChannelConfig).(interface{ MaskInBff() any }); ok {
		y.MiniprogramChannelConfig = v.MaskInBff().(*AssistantMiniprogramChannelConfig)
	}
	if v, ok := any(y.ChunkConfig).(interface{ MaskInBff() any }); ok {
		y.ChunkConfig = v.MaskInBff().(*AssistantChunkConfig)
	}
	if v, ok := any(y.QuestionTypeConfig).(interface{ MaskInBff() any }); ok {
		y.QuestionTypeConfig = v.MaskInBff().(*AssistantQuestionTypeConfig)
	}
	if v, ok := any(y.AllowlistConfig).(interface{ MaskInBff() any }); ok {
		y.AllowlistConfig = v.MaskInBff().(*AssistantAllowlistConfig)
	}
	if v, ok := any(y.UserLabelConfig).(interface{ MaskInBff() any }); ok {
		y.UserLabelConfig = v.MaskInBff().(*AssistantUserLabelConfig)
	}

	return y
}

func (x *AssistantWeixinChannelConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantWeixinChannelConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantWeixinChannelConfig)
	if v, ok := any(y.WeixinDevelopConfig).(interface{ MaskInLog() any }); ok {
		y.WeixinDevelopConfig = v.MaskInLog().(*AssistantWeixinDevelopConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInLog() any }); ok {
		y.WelcomeMessageConfig = v.MaskInLog().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInLog() any }); ok {
		y.PresetQuestionConfig = v.MaskInLog().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInLog() any }); ok {
		y.KefuConfig = v.MaskInLog().(*AssistantKefuConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInLog() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInLog().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInLog() any }); ok {
		y.InteractiveCodeConfig = v.MaskInLog().(*AssistantInteractiveCodeConfig)
	}

	return y
}

func (x *AssistantWeixinChannelConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantWeixinChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WeixinDevelopConfig).(interface{ MaskInRpc() any }); ok {
		y.WeixinDevelopConfig = v.MaskInRpc().(*AssistantWeixinDevelopConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInRpc() any }); ok {
		y.WelcomeMessageConfig = v.MaskInRpc().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInRpc() any }); ok {
		y.PresetQuestionConfig = v.MaskInRpc().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInRpc() any }); ok {
		y.KefuConfig = v.MaskInRpc().(*AssistantKefuConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInRpc() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInRpc().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInRpc() any }); ok {
		y.InteractiveCodeConfig = v.MaskInRpc().(*AssistantInteractiveCodeConfig)
	}

	return y
}

func (x *AssistantWeixinChannelConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantWeixinChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WeixinDevelopConfig).(interface{ MaskInBff() any }); ok {
		y.WeixinDevelopConfig = v.MaskInBff().(*AssistantWeixinDevelopConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInBff() any }); ok {
		y.WelcomeMessageConfig = v.MaskInBff().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInBff() any }); ok {
		y.PresetQuestionConfig = v.MaskInBff().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInBff() any }); ok {
		y.KefuConfig = v.MaskInBff().(*AssistantKefuConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInBff() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInBff().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInBff() any }); ok {
		y.InteractiveCodeConfig = v.MaskInBff().(*AssistantInteractiveCodeConfig)
	}

	return y
}

func (x *AssistantTanliveWebChannelConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantTanliveWebChannelConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantTanliveWebChannelConfig)
	if v, ok := any(y.WebsiteConfig).(interface{ MaskInLog() any }); ok {
		y.WebsiteConfig = v.MaskInLog().(*AssistantWebsiteConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInLog() any }); ok {
		y.WelcomeMessageConfig = v.MaskInLog().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInLog() any }); ok {
		y.PresetQuestionConfig = v.MaskInLog().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInLog() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInLog().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInLog() any }); ok {
		y.InteractiveCodeConfig = v.MaskInLog().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInLog() any }); ok {
		y.GraphParseConfig = v.MaskInLog().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInLog() any }); ok {
		y.KefuConfig = v.MaskInLog().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantTanliveWebChannelConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantTanliveWebChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WebsiteConfig).(interface{ MaskInRpc() any }); ok {
		y.WebsiteConfig = v.MaskInRpc().(*AssistantWebsiteConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInRpc() any }); ok {
		y.WelcomeMessageConfig = v.MaskInRpc().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInRpc() any }); ok {
		y.PresetQuestionConfig = v.MaskInRpc().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInRpc() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInRpc().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInRpc() any }); ok {
		y.InteractiveCodeConfig = v.MaskInRpc().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInRpc() any }); ok {
		y.GraphParseConfig = v.MaskInRpc().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInRpc() any }); ok {
		y.KefuConfig = v.MaskInRpc().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantTanliveWebChannelConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantTanliveWebChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WebsiteConfig).(interface{ MaskInBff() any }); ok {
		y.WebsiteConfig = v.MaskInBff().(*AssistantWebsiteConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInBff() any }); ok {
		y.WelcomeMessageConfig = v.MaskInBff().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInBff() any }); ok {
		y.PresetQuestionConfig = v.MaskInBff().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInBff() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInBff().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInBff() any }); ok {
		y.InteractiveCodeConfig = v.MaskInBff().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInBff() any }); ok {
		y.GraphParseConfig = v.MaskInBff().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInBff() any }); ok {
		y.KefuConfig = v.MaskInBff().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantTanliveAppChannelConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantTanliveAppChannelConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantTanliveAppChannelConfig)
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInLog() any }); ok {
		y.WelcomeMessageConfig = v.MaskInLog().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInLog() any }); ok {
		y.PresetQuestionConfig = v.MaskInLog().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInLog() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInLog().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInLog() any }); ok {
		y.InteractiveCodeConfig = v.MaskInLog().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInLog() any }); ok {
		y.GraphParseConfig = v.MaskInLog().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInLog() any }); ok {
		y.KefuConfig = v.MaskInLog().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantTanliveAppChannelConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantTanliveAppChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInRpc() any }); ok {
		y.WelcomeMessageConfig = v.MaskInRpc().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInRpc() any }); ok {
		y.PresetQuestionConfig = v.MaskInRpc().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInRpc() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInRpc().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInRpc() any }); ok {
		y.InteractiveCodeConfig = v.MaskInRpc().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInRpc() any }); ok {
		y.GraphParseConfig = v.MaskInRpc().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInRpc() any }); ok {
		y.KefuConfig = v.MaskInRpc().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantTanliveAppChannelConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantTanliveAppChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInBff() any }); ok {
		y.WelcomeMessageConfig = v.MaskInBff().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInBff() any }); ok {
		y.PresetQuestionConfig = v.MaskInBff().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInBff() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInBff().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInBff() any }); ok {
		y.InteractiveCodeConfig = v.MaskInBff().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInBff() any }); ok {
		y.GraphParseConfig = v.MaskInBff().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInBff() any }); ok {
		y.KefuConfig = v.MaskInBff().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantWhatsappChannelConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantWhatsappChannelConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantWhatsappChannelConfig)
	if v, ok := any(y.WhatsappDevelopConfig).(interface{ MaskInLog() any }); ok {
		y.WhatsappDevelopConfig = v.MaskInLog().(*AssistantWhatsappDevelopConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInLog() any }); ok {
		y.WelcomeMessageConfig = v.MaskInLog().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInLog() any }); ok {
		y.PresetQuestionConfig = v.MaskInLog().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInLog() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInLog().(*AssistantRatingScaleReplyConfig)
	}

	return y
}

func (x *AssistantWhatsappChannelConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantWhatsappChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WhatsappDevelopConfig).(interface{ MaskInRpc() any }); ok {
		y.WhatsappDevelopConfig = v.MaskInRpc().(*AssistantWhatsappDevelopConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInRpc() any }); ok {
		y.WelcomeMessageConfig = v.MaskInRpc().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInRpc() any }); ok {
		y.PresetQuestionConfig = v.MaskInRpc().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInRpc() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInRpc().(*AssistantRatingScaleReplyConfig)
	}

	return y
}

func (x *AssistantWhatsappChannelConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantWhatsappChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.WhatsappDevelopConfig).(interface{ MaskInBff() any }); ok {
		y.WhatsappDevelopConfig = v.MaskInBff().(*AssistantWhatsappDevelopConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInBff() any }); ok {
		y.WelcomeMessageConfig = v.MaskInBff().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInBff() any }); ok {
		y.PresetQuestionConfig = v.MaskInBff().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInBff() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInBff().(*AssistantRatingScaleReplyConfig)
	}

	return y
}

func (x *AssistantMiniprogramChannelConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantMiniprogramChannelConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantMiniprogramChannelConfig)
	if v, ok := any(y.MiniprogramConfig).(interface{ MaskInLog() any }); ok {
		y.MiniprogramConfig = v.MaskInLog().(*AssistantMiniprogramConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInLog() any }); ok {
		y.WelcomeMessageConfig = v.MaskInLog().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInLog() any }); ok {
		y.PresetQuestionConfig = v.MaskInLog().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInLog() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInLog().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInLog() any }); ok {
		y.InteractiveCodeConfig = v.MaskInLog().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInLog() any }); ok {
		y.GraphParseConfig = v.MaskInLog().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInLog() any }); ok {
		y.KefuConfig = v.MaskInLog().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantMiniprogramChannelConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantMiniprogramChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.MiniprogramConfig).(interface{ MaskInRpc() any }); ok {
		y.MiniprogramConfig = v.MaskInRpc().(*AssistantMiniprogramConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInRpc() any }); ok {
		y.WelcomeMessageConfig = v.MaskInRpc().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInRpc() any }); ok {
		y.PresetQuestionConfig = v.MaskInRpc().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInRpc() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInRpc().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInRpc() any }); ok {
		y.InteractiveCodeConfig = v.MaskInRpc().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInRpc() any }); ok {
		y.GraphParseConfig = v.MaskInRpc().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInRpc() any }); ok {
		y.KefuConfig = v.MaskInRpc().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantMiniprogramChannelConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantMiniprogramChannelConfig)(nil)
	}

	y := x
	if v, ok := any(y.MiniprogramConfig).(interface{ MaskInBff() any }); ok {
		y.MiniprogramConfig = v.MaskInBff().(*AssistantMiniprogramConfig)
	}
	if v, ok := any(y.WelcomeMessageConfig).(interface{ MaskInBff() any }); ok {
		y.WelcomeMessageConfig = v.MaskInBff().(*AssistantWelcomeMessageConfig)
	}
	if v, ok := any(y.PresetQuestionConfig).(interface{ MaskInBff() any }); ok {
		y.PresetQuestionConfig = v.MaskInBff().(*AssistantPresetQuestionConfig)
	}
	if v, ok := any(y.RatingScaleReplyConfig).(interface{ MaskInBff() any }); ok {
		y.RatingScaleReplyConfig = v.MaskInBff().(*AssistantRatingScaleReplyConfig)
	}
	if v, ok := any(y.InteractiveCodeConfig).(interface{ MaskInBff() any }); ok {
		y.InteractiveCodeConfig = v.MaskInBff().(*AssistantInteractiveCodeConfig)
	}
	if v, ok := any(y.GraphParseConfig).(interface{ MaskInBff() any }); ok {
		y.GraphParseConfig = v.MaskInBff().(*AssistantGraphParseConfig)
	}
	if v, ok := any(y.KefuConfig).(interface{ MaskInBff() any }); ok {
		y.KefuConfig = v.MaskInBff().(*AssistantKefuConfig)
	}

	return y
}

func (x *AssistantWelcomeMessageConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantWelcomeMessageConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantWelcomeMessageConfig)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*AssistantWelcomeMessage)
		}
	}

	return y
}

func (x *AssistantWelcomeMessageConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantWelcomeMessageConfig)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*AssistantWelcomeMessage)
		}
	}

	return y
}

func (x *AssistantWelcomeMessageConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantWelcomeMessageConfig)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*AssistantWelcomeMessage)
		}
	}

	return y
}

func (x *AssistantPresetQuestionConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantPresetQuestionConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantPresetQuestionConfig)
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Questions[k] = vv.MaskInLog().(*AssistantPresetQuestion)
		}
	}

	return y
}

func (x *AssistantPresetQuestionConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantPresetQuestionConfig)(nil)
	}

	y := x
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Questions[k] = vv.MaskInRpc().(*AssistantPresetQuestion)
		}
	}

	return y
}

func (x *AssistantPresetQuestionConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantPresetQuestionConfig)(nil)
	}

	y := x
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Questions[k] = vv.MaskInBff().(*AssistantPresetQuestion)
		}
	}

	return y
}

func (x *AssistantRatingScaleReplyConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantRatingScaleReplyConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantRatingScaleReplyConfig)
	for k, v := range y.Replies {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Replies[k] = vv.MaskInLog().(*AssistantRatingScaleReply)
		}
	}

	return y
}

func (x *AssistantRatingScaleReplyConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantRatingScaleReplyConfig)(nil)
	}

	y := x
	for k, v := range y.Replies {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Replies[k] = vv.MaskInRpc().(*AssistantRatingScaleReply)
		}
	}

	return y
}

func (x *AssistantRatingScaleReplyConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantRatingScaleReplyConfig)(nil)
	}

	y := x
	for k, v := range y.Replies {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Replies[k] = vv.MaskInBff().(*AssistantRatingScaleReply)
		}
	}

	return y
}

func (x *AssistantInteractiveCodeConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantInteractiveCodeConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantInteractiveCodeConfig)
	for k, v := range y.Codes {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Codes[k] = vv.MaskInLog().(*AssistantInteractiveCode)
		}
	}

	return y
}

func (x *AssistantInteractiveCodeConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantInteractiveCodeConfig)(nil)
	}

	y := x
	for k, v := range y.Codes {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Codes[k] = vv.MaskInRpc().(*AssistantInteractiveCode)
		}
	}

	return y
}

func (x *AssistantInteractiveCodeConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantInteractiveCodeConfig)(nil)
	}

	y := x
	for k, v := range y.Codes {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Codes[k] = vv.MaskInBff().(*AssistantInteractiveCode)
		}
	}

	return y
}

func (x *AssistantKefuConfig) MaskInLog() any {
	if x == nil {
		return (*AssistantKefuConfig)(nil)
	}

	y := proto.Clone(x).(*AssistantKefuConfig)
	for k, v := range y.Staffs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Staffs[k] = vv.MaskInLog().(*AssistantKefuStaff)
		}
	}
	if v, ok := any(y.Reply).(interface{ MaskInLog() any }); ok {
		y.Reply = v.MaskInLog().(*AssistantKefuReply)
	}

	return y
}

func (x *AssistantKefuConfig) MaskInRpc() any {
	if x == nil {
		return (*AssistantKefuConfig)(nil)
	}

	y := x
	for k, v := range y.Staffs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Staffs[k] = vv.MaskInRpc().(*AssistantKefuStaff)
		}
	}
	if v, ok := any(y.Reply).(interface{ MaskInRpc() any }); ok {
		y.Reply = v.MaskInRpc().(*AssistantKefuReply)
	}

	return y
}

func (x *AssistantKefuConfig) MaskInBff() any {
	if x == nil {
		return (*AssistantKefuConfig)(nil)
	}

	y := x
	for k, v := range y.Staffs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Staffs[k] = vv.MaskInBff().(*AssistantKefuStaff)
		}
	}
	if v, ok := any(y.Reply).(interface{ MaskInBff() any }); ok {
		y.Reply = v.MaskInBff().(*AssistantKefuReply)
	}

	return y
}

func (x *AssistantKefuReply) MaskInLog() any {
	if x == nil {
		return (*AssistantKefuReply)(nil)
	}

	y := proto.Clone(x).(*AssistantKefuReply)
	if v, ok := any(y.MainlandZh).(interface{ MaskInLog() any }); ok {
		y.MainlandZh = v.MaskInLog().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.MainlandEn).(interface{ MaskInLog() any }); ok {
		y.MainlandEn = v.MaskInLog().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.NonMainlandZh).(interface{ MaskInLog() any }); ok {
		y.NonMainlandZh = v.MaskInLog().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.NonMainlandEn).(interface{ MaskInLog() any }); ok {
		y.NonMainlandEn = v.MaskInLog().(*AssistantKefuReply_Reply)
	}
	for k, v := range y.Custom {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Custom[k] = vv.MaskInLog().(*AssistantKefuReply_Custom)
		}
	}

	return y
}

func (x *AssistantKefuReply) MaskInRpc() any {
	if x == nil {
		return (*AssistantKefuReply)(nil)
	}

	y := x
	if v, ok := any(y.MainlandZh).(interface{ MaskInRpc() any }); ok {
		y.MainlandZh = v.MaskInRpc().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.MainlandEn).(interface{ MaskInRpc() any }); ok {
		y.MainlandEn = v.MaskInRpc().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.NonMainlandZh).(interface{ MaskInRpc() any }); ok {
		y.NonMainlandZh = v.MaskInRpc().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.NonMainlandEn).(interface{ MaskInRpc() any }); ok {
		y.NonMainlandEn = v.MaskInRpc().(*AssistantKefuReply_Reply)
	}
	for k, v := range y.Custom {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Custom[k] = vv.MaskInRpc().(*AssistantKefuReply_Custom)
		}
	}

	return y
}

func (x *AssistantKefuReply) MaskInBff() any {
	if x == nil {
		return (*AssistantKefuReply)(nil)
	}

	y := x
	if v, ok := any(y.MainlandZh).(interface{ MaskInBff() any }); ok {
		y.MainlandZh = v.MaskInBff().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.MainlandEn).(interface{ MaskInBff() any }); ok {
		y.MainlandEn = v.MaskInBff().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.NonMainlandZh).(interface{ MaskInBff() any }); ok {
		y.NonMainlandZh = v.MaskInBff().(*AssistantKefuReply_Reply)
	}
	if v, ok := any(y.NonMainlandEn).(interface{ MaskInBff() any }); ok {
		y.NonMainlandEn = v.MaskInBff().(*AssistantKefuReply_Reply)
	}
	for k, v := range y.Custom {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Custom[k] = vv.MaskInBff().(*AssistantKefuReply_Custom)
		}
	}

	return y
}

func (x *EmbeddingModelOption) MaskInLog() any {
	if x == nil {
		return (*EmbeddingModelOption)(nil)
	}

	y := proto.Clone(x).(*EmbeddingModelOption)
	if v, ok := any(y.ZhDefault).(interface{ MaskInLog() any }); ok {
		y.ZhDefault = v.MaskInLog().(*AssistantChunkConfig)
	}
	if v, ok := any(y.EnDefault).(interface{ MaskInLog() any }); ok {
		y.EnDefault = v.MaskInLog().(*AssistantChunkConfig)
	}

	return y
}

func (x *EmbeddingModelOption) MaskInRpc() any {
	if x == nil {
		return (*EmbeddingModelOption)(nil)
	}

	y := x
	if v, ok := any(y.ZhDefault).(interface{ MaskInRpc() any }); ok {
		y.ZhDefault = v.MaskInRpc().(*AssistantChunkConfig)
	}
	if v, ok := any(y.EnDefault).(interface{ MaskInRpc() any }); ok {
		y.EnDefault = v.MaskInRpc().(*AssistantChunkConfig)
	}

	return y
}

func (x *EmbeddingModelOption) MaskInBff() any {
	if x == nil {
		return (*EmbeddingModelOption)(nil)
	}

	y := x
	if v, ok := any(y.ZhDefault).(interface{ MaskInBff() any }); ok {
		y.ZhDefault = v.MaskInBff().(*AssistantChunkConfig)
	}
	if v, ok := any(y.EnDefault).(interface{ MaskInBff() any }); ok {
		y.EnDefault = v.MaskInBff().(*AssistantChunkConfig)
	}

	return y
}

func (x *AssistantChanges) MaskInLog() any {
	if x == nil {
		return (*AssistantChanges)(nil)
	}

	y := proto.Clone(x).(*AssistantChanges)
	if v, ok := any(y.Old).(interface{ MaskInLog() any }); ok {
		y.Old = v.MaskInLog().(*AssistantConfig)
	}
	if v, ok := any(y.New).(interface{ MaskInLog() any }); ok {
		y.New = v.MaskInLog().(*AssistantConfig)
	}

	return y
}

func (x *AssistantChanges) MaskInRpc() any {
	if x == nil {
		return (*AssistantChanges)(nil)
	}

	y := x
	if v, ok := any(y.Old).(interface{ MaskInRpc() any }); ok {
		y.Old = v.MaskInRpc().(*AssistantConfig)
	}
	if v, ok := any(y.New).(interface{ MaskInRpc() any }); ok {
		y.New = v.MaskInRpc().(*AssistantConfig)
	}

	return y
}

func (x *AssistantChanges) MaskInBff() any {
	if x == nil {
		return (*AssistantChanges)(nil)
	}

	y := x
	if v, ok := any(y.Old).(interface{ MaskInBff() any }); ok {
		y.Old = v.MaskInBff().(*AssistantConfig)
	}
	if v, ok := any(y.New).(interface{ MaskInBff() any }); ok {
		y.New = v.MaskInBff().(*AssistantConfig)
	}

	return y
}

func (x *AssistantLog) MaskInLog() any {
	if x == nil {
		return (*AssistantLog)(nil)
	}

	y := proto.Clone(x).(*AssistantLog)
	if v, ok := any(y.Changes).(interface{ MaskInLog() any }); ok {
		y.Changes = v.MaskInLog().(*AssistantChanges)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *AssistantLog) MaskInRpc() any {
	if x == nil {
		return (*AssistantLog)(nil)
	}

	y := x
	if v, ok := any(y.Changes).(interface{ MaskInRpc() any }); ok {
		y.Changes = v.MaskInRpc().(*AssistantChanges)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *AssistantLog) MaskInBff() any {
	if x == nil {
		return (*AssistantLog)(nil)
	}

	y := x
	if v, ok := any(y.Changes).(interface{ MaskInBff() any }); ok {
		y.Changes = v.MaskInBff().(*AssistantChanges)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *AssistantChunks) MaskInLog() any {
	if x == nil {
		return (*AssistantChunks)(nil)
	}

	y := proto.Clone(x).(*AssistantChunks)
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chunks[k] = vv.MaskInLog().(*ChunkItem)
		}
	}

	return y
}

func (x *AssistantChunks) MaskInRpc() any {
	if x == nil {
		return (*AssistantChunks)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chunks[k] = vv.MaskInRpc().(*ChunkItem)
		}
	}

	return y
}

func (x *AssistantChunks) MaskInBff() any {
	if x == nil {
		return (*AssistantChunks)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chunks[k] = vv.MaskInBff().(*ChunkItem)
		}
	}

	return y
}

func (x *ManualChunkPara) MaskInLog() any {
	if x == nil {
		return (*ManualChunkPara)(nil)
	}

	y := proto.Clone(x).(*ManualChunkPara)
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AssistantChunks[k] = vv.MaskInLog().(*AssistantChunks)
		}
	}

	return y
}

func (x *ManualChunkPara) MaskInRpc() any {
	if x == nil {
		return (*ManualChunkPara)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AssistantChunks[k] = vv.MaskInRpc().(*AssistantChunks)
		}
	}

	return y
}

func (x *ManualChunkPara) MaskInBff() any {
	if x == nil {
		return (*ManualChunkPara)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AssistantChunks[k] = vv.MaskInBff().(*AssistantChunks)
		}
	}

	return y
}

func (x *AutoChunkPara) MaskInLog() any {
	if x == nil {
		return (*AutoChunkPara)(nil)
	}

	y := proto.Clone(x).(*AutoChunkPara)
	if v, ok := any(y.ChunkConfig).(interface{ MaskInLog() any }); ok {
		y.ChunkConfig = v.MaskInLog().(*AssistantChunkConfig)
	}

	return y
}

func (x *AutoChunkPara) MaskInRpc() any {
	if x == nil {
		return (*AutoChunkPara)(nil)
	}

	y := x
	if v, ok := any(y.ChunkConfig).(interface{ MaskInRpc() any }); ok {
		y.ChunkConfig = v.MaskInRpc().(*AssistantChunkConfig)
	}

	return y
}

func (x *AutoChunkPara) MaskInBff() any {
	if x == nil {
		return (*AutoChunkPara)(nil)
	}

	y := x
	if v, ok := any(y.ChunkConfig).(interface{ MaskInBff() any }); ok {
		y.ChunkConfig = v.MaskInBff().(*AssistantChunkConfig)
	}

	return y
}

func (x *AiAssistantNoticeConf) MaskInLog() any {
	if x == nil {
		return (*AiAssistantNoticeConf)(nil)
	}

	y := proto.Clone(x).(*AiAssistantNoticeConf)
	if v, ok := any(y.Notice).(interface{ MaskInLog() any }); ok {
		y.Notice = v.MaskInLog().(*AiAssistantNoticeConf_Notice)
	}
	if v, ok := any(y.RangeTime).(interface{ MaskInLog() any }); ok {
		y.RangeTime = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *AiAssistantNoticeConf) MaskInRpc() any {
	if x == nil {
		return (*AiAssistantNoticeConf)(nil)
	}

	y := x
	if v, ok := any(y.Notice).(interface{ MaskInRpc() any }); ok {
		y.Notice = v.MaskInRpc().(*AiAssistantNoticeConf_Notice)
	}
	if v, ok := any(y.RangeTime).(interface{ MaskInRpc() any }); ok {
		y.RangeTime = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *AiAssistantNoticeConf) MaskInBff() any {
	if x == nil {
		return (*AiAssistantNoticeConf)(nil)
	}

	y := x
	if v, ok := any(y.Notice).(interface{ MaskInBff() any }); ok {
		y.Notice = v.MaskInBff().(*AiAssistantNoticeConf_Notice)
	}
	if v, ok := any(y.RangeTime).(interface{ MaskInBff() any }); ok {
		y.RangeTime = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *MessageCollectionSnapshot) MaskInLog() any {
	if x == nil {
		return (*MessageCollectionSnapshot)(nil)
	}

	y := proto.Clone(x).(*MessageCollectionSnapshot)
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *MessageCollectionSnapshot) MaskInRpc() any {
	if x == nil {
		return (*MessageCollectionSnapshot)(nil)
	}

	y := x
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *MessageCollectionSnapshot) MaskInBff() any {
	if x == nil {
		return (*MessageCollectionSnapshot)(nil)
	}

	y := x
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *MessageDocSnapshot) MaskInLog() any {
	if x == nil {
		return (*MessageDocSnapshot)(nil)
	}

	y := proto.Clone(x).(*MessageDocSnapshot)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *MessageDocSnapshot) MaskInRpc() any {
	if x == nil {
		return (*MessageDocSnapshot)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *MessageDocSnapshot) MaskInBff() any {
	if x == nil {
		return (*MessageDocSnapshot)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ChatShare) MaskInLog() any {
	if x == nil {
		return (*ChatShare)(nil)
	}

	y := proto.Clone(x).(*ChatShare)
	if v, ok := any(y.ShareDate).(interface{ MaskInLog() any }); ok {
		y.ShareDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInLog() any }); ok {
		y.ExpireDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInLog() any }); ok {
		y.LastAccessTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShare) MaskInRpc() any {
	if x == nil {
		return (*ChatShare)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInRpc() any }); ok {
		y.ShareDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInRpc() any }); ok {
		y.ExpireDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInRpc() any }); ok {
		y.LastAccessTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShare) MaskInBff() any {
	if x == nil {
		return (*ChatShare)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInBff() any }); ok {
		y.ShareDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInBff() any }); ok {
		y.ExpireDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInBff() any }); ok {
		y.LastAccessTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShareAccess) MaskInLog() any {
	if x == nil {
		return (*ChatShareAccess)(nil)
	}

	y := proto.Clone(x).(*ChatShareAccess)
	if v, ok := any(y.AccessDate).(interface{ MaskInLog() any }); ok {
		y.AccessDate = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShareAccess) MaskInRpc() any {
	if x == nil {
		return (*ChatShareAccess)(nil)
	}

	y := x
	if v, ok := any(y.AccessDate).(interface{ MaskInRpc() any }); ok {
		y.AccessDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatShareAccess) MaskInBff() any {
	if x == nil {
		return (*ChatShareAccess)(nil)
	}

	y := x
	if v, ok := any(y.AccessDate).(interface{ MaskInBff() any }); ok {
		y.AccessDate = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ChatMessageContentFilterItem) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatMessageContent) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Filter {
		if sanitizer, ok := any(x.Filter[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Ugcs {
		if sanitizer, ok := any(x.Ugcs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *MessageUgc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Filter {
		if sanitizer, ok := any(x.Filter[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Cards {
		if sanitizer, ok := any(x.Cards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatPushMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.HashMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Ugcs {
		if sanitizer, ok := any(x.Ugcs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CollectionSnapshot).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Task).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.LastOperator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.DocSnapshot).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *QuestionsWithAnswer) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Questions {
		if sanitizer, ok := any(x.Questions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Answers {
		if sanitizer, ok := any(x.Answers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatSendRecordInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.SendDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *EventChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Ugcs {
		if sanitizer, ok := any(x.Ugcs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.ProcessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Answers {
		if sanitizer, ok := any(x.Answers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *EventChatHashMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Ugcs {
		if sanitizer, ok := any(x.Ugcs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.ProcessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *EventChatHashMessage_EventMessageUgc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Filter {
		if sanitizer, ok := any(x.Filter[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Cards {
		if sanitizer, ok := any(x.Cards[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *EventChatHashMessage_EventMessageFilter) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tags {
		if sanitizer, ok := any(x.Tags[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *EventChatHashMessage_EventMessageDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatMessageDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatMessageLog) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.FetchRespTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatAgentTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.SuggestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatSuggestLog) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *Chat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ShareReceivers {
		if sanitizer, ok := any(x.ShareReceivers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *FullTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Doc).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Copies {
		if sanitizer, ok := any(x.Copies[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *QA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ShareReceivers {
		if sanitizer, ok := any(x.ShareReceivers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *FeedbackComment) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *FullFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Feedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OriginalQuestion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalAnswer).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.ExpectedDocs {
		if sanitizer, ok := any(x.ExpectedDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ExpectedMgmtDocs {
		if sanitizer, ok := any(x.ExpectedMgmtDocs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *Feedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OpComment).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MgmtFeedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MgmtComment).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UserFeedbackBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OpFeedbackBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MgmtFeedbackBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UserFeedbackAt).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OpFeedbackAt).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MgmtFeedbackAt).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateIdentity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateIdentity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *FeedbackReference) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *TypedReference) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.Reference.(type) {
	case *TypedReference_Url:
		if sanitizer, ok := any(oneof.Url).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Reference = oneof
	case *TypedReference_Text:
		if sanitizer, ok := any(oneof.Text).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Reference = oneof
	case *TypedReference_File:
		if sanitizer, ok := any(oneof.File).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Reference = oneof
	}
}

func (x *FeedbackLog) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateIdentity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *FullFeedbackLog) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Log).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Feedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OriginalQuestion).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *LabelFilter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Eq).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Gte).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Lte).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.In {
		if sanitizer, ok := any(x.In[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Like).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *CustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Value).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *Assistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Collections {
		if sanitizer, ok := any(x.Collections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *AssistantDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WelcomeMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.NoPermissionMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.DefaultPushMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RatingScaleMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LiveAgentMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MultimodalPrompt).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.QuestionTypeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.SuggestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantWelcomeMsg) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CodeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantLiverAgentConfigMsg) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.RemindPushMsg).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Reply).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatLiveAgent) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatLiveAgentInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.LiveAgents {
		if sanitizer, ok := any(x.LiveAgents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CurrentLiveAgent).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *SearchCollectionItem) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AiChatSendRecord) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.SendDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ExportTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastUpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *FullAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Assistant).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantV2) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.VisibleChainConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.FieldManageConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Admins {
		if sanitizer, ok := any(x.Admins[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.ChatOrSqlConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.AskSuggestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.WeixinChannelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.TanliveWebChannelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.TanliveAppChannelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.WhatsappChannelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MiniprogramChannelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ChunkConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.QuestionTypeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.AllowlistConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UserLabelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantWeixinChannelConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WeixinDevelopConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.WelcomeMessageConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.PresetQuestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.KefuConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RatingScaleReplyConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.InteractiveCodeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantTanliveWebChannelConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WebsiteConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.WelcomeMessageConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.PresetQuestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RatingScaleReplyConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.InteractiveCodeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.GraphParseConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.KefuConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantTanliveAppChannelConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WelcomeMessageConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.PresetQuestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RatingScaleReplyConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.InteractiveCodeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.GraphParseConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.KefuConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantWhatsappChannelConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WhatsappDevelopConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.WelcomeMessageConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.PresetQuestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RatingScaleReplyConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantMiniprogramChannelConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.MiniprogramConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.WelcomeMessageConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.PresetQuestionConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RatingScaleReplyConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.InteractiveCodeConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.GraphParseConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.KefuConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantWelcomeMessageConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *AssistantPresetQuestionConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Questions {
		if sanitizer, ok := any(x.Questions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *AssistantRatingScaleReplyConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Replies {
		if sanitizer, ok := any(x.Replies[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *AssistantInteractiveCodeConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Codes {
		if sanitizer, ok := any(x.Codes[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *AssistantKefuConfig) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Staffs {
		if sanitizer, ok := any(x.Staffs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Reply).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantKefuReply) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.MainlandZh).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MainlandEn).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.NonMainlandZh).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.NonMainlandEn).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Custom {
		if sanitizer, ok := any(x.Custom[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *EmbeddingModelOption) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ZhDefault).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EnDefault).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantChanges) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Old).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.New).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantLog) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Changes).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AssistantChunks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chunks {
		if sanitizer, ok := any(x.Chunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ManualChunkPara) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.AssistantChunks {
		if sanitizer, ok := any(x.AssistantChunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *AutoChunkPara) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ChunkConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *AiAssistantNoticeConf) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Notice).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.RangeTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *MessageCollectionSnapshot) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *MessageDocSnapshot) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ChatShare) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ShareDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ExpireDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastAccessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ChatShareAccess) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AccessDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}
