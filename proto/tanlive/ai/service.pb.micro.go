// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: tanlive/ai/service.proto

package ai

import (
	_ "e.coding.net/tencent-ssv/tanlive/gokits/proto/tanlive"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	_ "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/fieldmaskpb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for AiService service

func NewAiServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for AiService service

type AiService interface {
	// 更新消息doc快照
	UpdateMessageDocs(ctx context.Context, in *ReqUpdateMessageDocs, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取消息doc快照
	DescribeMessageDocSnapshot(ctx context.Context, in *ReqDescribeMessageDocSnapshot, opts ...client.CallOption) (*RspDescribeMessageDocSnapshot, error)
	// 停止消息发送
	StopAnswerReply(ctx context.Context, in *ReqStopAnswerReply, opts ...client.CallOption) (*RspStopAnswerReply, error)
	// 停止消息发送
	StopQuestionReply(ctx context.Context, in *ReqStopQuestionReply, opts ...client.CallOption) (*RspStopQuestionReply, error)
	// 查询前置消息
	DescribePreMessageTask(ctx context.Context, in *ReqDescribePreMessageTask, opts ...client.CallOption) (*RspReqDescribePreMessageTask, error)
	// 查询文件解析状态
	DescribeChatMessageFileState(ctx context.Context, in *ReqDescribeChatMessageFileState, opts ...client.CallOption) (*RspDescribeChatMessageFileState, error)
	// 查询当前chat需要执行的tasks
	DescribeChatAgentTaskByPreTask(ctx context.Context, in *ReqDescribeChatAgentTaskByPreTask, opts ...client.CallOption) (*RspDescribeChatAgentTaskByPreTask, error)
	// 更新message collections
	UpdateChatMessageCollections(ctx context.Context, in *ReqUpdateChatMessageCollections, opts ...client.CallOption) (*emptypb.Empty, error)
	// 修复search collection items
	FixSearchCollectionItems(ctx context.Context, in *ReqFixSearchCollectionItems, opts ...client.CallOption) (*RspFixSearchCollectionItems, error)
	// 查询agent任务
	DescribeChatAgentTask(ctx context.Context, in *ReqDescribeChatAgentTask, opts ...client.CallOption) (*RspDescribeChatAgentTask, error)
	// 检查会话权限
	CheckChatPermission(ctx context.Context, in *ReqCheckChatPermission, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取会话聊天记录
	DescribeChatQuestionAnswersByPage(ctx context.Context, in *ReqDescribeChatQuestionAnswersByPage, opts ...client.CallOption) (*RspDescribeChatQuestionAnswersByPage, error)
	// 创建更新
	CreateChatOperation(ctx context.Context, in *ReqCreateChatOperation, opts ...client.CallOption) (*emptypb.Empty, error)
	// 将对话中的文件转换成文本
	ParseChatDoc(ctx context.Context, in *ReqParseChatDoc, opts ...client.CallOption) (*RspParseChatDoc, error)
	// 更新message 文本
	UpdateMessageText(ctx context.Context, in *ReqUpdateMessageText, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建QA匹配模式命中回答
	CreateQaMatchMessage(ctx context.Context, in *ReqCreateQaMatchMessage, opts ...client.CallOption) (*RspCreateQaMatchMessage, error)
	// 查询qa模式匹配
	DescribeMessageMatchQa(ctx context.Context, in *ReqDescribeMessageMatchQa, opts ...client.CallOption) (*RspDescribeMessageMatchQa, error)
	// 重新发送
	ResendMessageSync(ctx context.Context, in *ReqResendMessageSync, opts ...client.CallOption) (*RspResendMessageSync, error)
	// 获取导出消息体
	DescribeExportChatMessages(ctx context.Context, in *ReqDescribeExportChatMessages, opts ...client.CallOption) (*RspDescribeExportChatMessages, error)
	// 创建消息建议问题
	CreateMessageSuggestQuestion(ctx context.Context, in *ReqCreateMessageSuggestQuestion, opts ...client.CallOption) (*RspCreateMessageSuggestQuestion, error)
	// 查询导出任务列表
	DescribeExportTasks(ctx context.Context, in *ReqDescribeExportTasks, opts ...client.CallOption) (*RspDescribeExportTasks, error)
	// 创建导出任务
	CreateExportTask(ctx context.Context, in *ReqCreateExportTask, opts ...client.CallOption) (*RspCreateExportTask, error)
	// 更新导出任务
	UpdateExportTask(ctx context.Context, in *ReqUpdateExportTask, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取QA列表
	ListQA(ctx context.Context, in *ReqListQA, opts ...client.CallOption) (*RspListQA, error)
	// 批量创建QA
	CreateQAInBulk(ctx context.Context, in *ReqCreateQAInBulk, opts ...client.CallOption) (*RspCreateQAInBulk, error)
	// 创建QA
	CreateQA(ctx context.Context, in *ReqCreateQA, opts ...client.CallOption) (*RspCreateQA, error)
	// 更新QA
	UpdateQA(ctx context.Context, in *ReqUpdateQA, opts ...client.CallOption) (*emptypb.Empty, error)
	// 批量更新QA
	UpdateQAInBulk(ctx context.Context, in *ReqUpdateQAInBulk, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取文本/文件列表
	ListTextFile(ctx context.Context, in *ReqListTextFile, opts ...client.CallOption) (*RspListTextFile, error)
	// 创建文本/文科
	CreateTextFileInBulk(ctx context.Context, in *ReqCreateTextFileInBulk, opts ...client.CallOption) (*RspCreateTextFileInBulk, error)
	// 更新文本/文件
	UpdateTextFile(ctx context.Context, in *ReqUpdateTextFile, opts ...client.CallOption) (*emptypb.Empty, error)
	// 批量更新文本/文件
	UpdateTextFileInBulk(ctx context.Context, in *ReqUpdateTextFileInBulk, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除QA/文本/文件
	DeleteDocInBulk(ctx context.Context, in *ReqDeleteDocInBulk, opts ...client.CallOption) (*RspDeleteDocInBulk, error)
	// 向量搜索带命中
	SearchCollectionOneShot(ctx context.Context, in *ReqSearchCollectionOneShot, opts ...client.CallOption) (*RspSearchCollectionOneShot, error)
	// 重新解析doc文件
	ReparseTextFiles(ctx context.Context, in *ReqReparseTextFiles, opts ...client.CallOption) (*RspReparseTextFiles, error)
	// 向量搜索
	SearchCollection(ctx context.Context, in *ReqSearchCollection, opts ...client.CallOption) (*RspSearchCollection, error)
	// deprecated 获取collection列表
	ListCollection(ctx context.Context, in *ReqListCollection, opts ...client.CallOption) (*RspListCollection, error)
	// 获取贡献者列表
	ListContributor(ctx context.Context, in *ReqListContributor, opts ...client.CallOption) (*RspListContributor, error)
	// 获取最近更新人列表
	ListUpdateBy(ctx context.Context, in *ReqListUpdateBy, opts ...client.CallOption) (*RspListUpdateBy, error)
	// 获取最近创建人人列表
	ListCreateBy(ctx context.Context, in *ReqListUpdateBy, opts ...client.CallOption) (*RspListUpdateBy, error)
	// 获取已分享的助手列表，用于表头筛选
	ListSharedAssistant(ctx context.Context, in *ReqListSharedAssistant, opts ...client.CallOption) (*RspListSharedAssistant, error)
	// 校验QA
	ValidateQAInBulk(ctx context.Context, in *ReqValidateQAInBulk, opts ...client.CallOption) (*RspValidateQAInBulk, error)
	// 校验文本/文件
	ValidateTextFileInBulk(ctx context.Context, in *ReqValidateTextFileInBulk, opts ...client.CallOption) (*RspValidateTextFileInBulk, error)
	// 克隆QA/文本/文件
	CloneDocInBulk(ctx context.Context, in *ReqCloneDocInBulk, opts ...client.CallOption) (*RspCloneDocInBulk, error)
	// 启用/禁用doc
	OnOffDocInBulk(ctx context.Context, in *ReqOnOffDocInBulk, opts ...client.CallOption) (*RspOnOffDocInBulk, error)
	// 根据文档ref_id查询文档
	ListDocByRef(ctx context.Context, in *ReqListDocByRef, opts ...client.CallOption) (*RspListDocByRef, error)
	// 更新doc标签
	UpdateDocLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, opts ...client.CallOption) (*emptypb.Empty, error)
	// 转换标签
	ConvertCustomLabel(ctx context.Context, in *ReqConvertCustomLabel, opts ...client.CallOption) (*RspConvertCustomLabel, error)
	// 获取标签topn值
	GetCustomLabelValueTopN(ctx context.Context, in *ReqGetCustomLabelValueTopN, opts ...client.CallOption) (*RspGetCustomLabelValueTopN, error)
	// 批量更新doc的特定字段值
	UpdateDocAttrInBulk(ctx context.Context, in *ReqUpdateDocAttrInBulk, opts ...client.CallOption) (*RspUpdateDocAttrInBulk, error)
	// 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
	GetTextFileTip(ctx context.Context, in *ReqGetTextFileTip, opts ...client.CallOption) (*RspGetTextFileTip, error)
	// 创建doc查询
	CreateDocQuery(ctx context.Context, in *ReqCreateDocQuery, opts ...client.CallOption) (*RspCreateDocQuery, error)
	// 查询QA的知识提示（问题超长，内容重复）等信息
	GetQaTip(ctx context.Context, in *ReqGetQaTip, opts ...client.CallOption) (*RspGetQaTip, error)
	// 创建全部文档md5
	CreateAllDocMd5(ctx context.Context, in *ReqCreateAllDocMd5, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建助手发送方设置
	CreateDocShareConfigSender(ctx context.Context, in *ReqCreateDocShareConfigSender, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查看创建知识库接收者设置
	ListeDocShareConfigSender(ctx context.Context, in *ReqListeDocShareConfigSender, opts ...client.CallOption) (*RspListeDocShareConfigSender, error)
	// 查询可分享的助手列表
	ListAssistantCanShareDoc(ctx context.Context, in *ReqListAssistantCanShareDoc, opts ...client.CallOption) (*RspListAssistantCanShareDoc, error)
	// 查询可分享的团队列表
	ListTeamCanShareDoc(ctx context.Context, in *ReqListTeamCanShareDoc, opts ...client.CallOption) (*RspListTeamCanShareDoc, error)
	// 查询可分享的用户列表
	ListUserCanShareDoc(ctx context.Context, in *ReqListUserCanShareDoc, opts ...client.CallOption) (*RspListUserCanShareDoc, error)
	// 查询已分享的团队列表
	ListSharedTeam(ctx context.Context, in *ReqListSharedTeam, opts ...client.CallOption) (*RspListSharedTeam, error)
	// 查询已分享的用户列表
	ListSharedUser(ctx context.Context, in *ReqListSharedUser, opts ...client.CallOption) (*RspListSharedUser, error)
	// 查询我设置的助手
	ListMyAssistantIds(ctx context.Context, in *ReqListMyAssistantIds, opts ...client.CallOption) (*RspListMyAssistantIds, error)
	// 创建助手接收方设置
	CreateDocShareConfigReceiverAssistant(ctx context.Context, in *ReqCreateDocShareConfigReceiverAssistant, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建个人/团队接收方设置
	CreateDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqCreateDocShareConfigReceiverUserTeam, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询助手接收方设置
	ListDocShareConfigReceiverAssistant(ctx context.Context, in *ReqListDocShareConfigReceiverAssistant, opts ...client.CallOption) (*RspListDocShareConfigReceiverAssistant, error)
	// 查询个人/团队接收方设置
	ListDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqListDocShareConfigReceiverUserTeam, opts ...client.CallOption) (*RspListDocShareConfigReceiverUserTeam, error)
	// 创建知识库分享至助手、个人、团队
	CreateDocShare(ctx context.Context, in *ReqCreateDocShare, opts ...client.CallOption) (*RspCreateDocShare, error)
	// 创建知识库同步or取消至助手（已废弃，请使用CreateDocShare）
	CreateDocShareAssistant(ctx context.Context, in *ReqCreateDocShare, opts ...client.CallOption) (*RspCreateDocShare, error)
	// 创建企微服务商租户
	CreateOpenWechat(ctx context.Context, in *ReqCreateOpenWechat, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询企微服务商租户
	DescribeOpenWechat(ctx context.Context, in *ReqDescribeOpenWechat, opts ...client.CallOption) (*RspDescribeOpenWechat, error)
	// 腾讯文档授权code处理
	AuthTencentCode(ctx context.Context, in *ReqAuthTencentCode, opts ...client.CallOption) (*RspAuthTencentCode, error)
	// 查询腾讯文档缓存是否为空
	DescribeTokenIsEmpty(ctx context.Context, in *ReqDescribeTokenIsEmpty, opts ...client.CallOption) (*RspDescribeTokenIsEmpty, error)
	// 查询腾讯文档外部用户列表
	ListExternalSourceUser(ctx context.Context, in *ReqListExternalSourceUser, opts ...client.CallOption) (*RspListExternalSourceUser, error)
	// 批量导入绿技行
	CreateGTBText(ctx context.Context, in *ReqCreateGTBText, opts ...client.CallOption) (*RspCreateGTBText, error)
	// 查询腾讯文档任务状态
	DescribeTencentDocTask(ctx context.Context, in *ReqDescribeTencentDocTask, opts ...client.CallOption) (*RspDescribeTencentDocTask, error)
	// 删除腾讯文档授权
	DelTencentDocAuth(ctx context.Context, in *ReqDelTencentDocAuth, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询绿技行状态
	DescribeGTBText(ctx context.Context, in *ReqDescribeGTBText, opts ...client.CallOption) (*RspDescribeGTBText, error)
	// 创建腾讯文档AuthURL
	CreateTencentDocAuthUrl(ctx context.Context, in *ReqCreateTencentDocAuthUrl, opts ...client.CallOption) (*RspCreateTencentDocAuthUrl, error)
	// 查询腾讯文档列表
	DescribeDocList(ctx context.Context, in *ReqDescribeTencentDocList, opts ...client.CallOption) (*RspDescribeTencentDocList, error)
	// 腾讯文档导入
	ImportTencentDoc(ctx context.Context, in *ReqImportTencentDoc, opts ...client.CallOption) (*RspImportTencentDoc, error)
	// 重新导入腾讯文档
	ReimportTencentDoc(ctx context.Context, in *ReqReimportTencentDoc, opts ...client.CallOption) (*RspReimportTencentDoc, error)
	// 导入腾讯文档网页剪存文档
	ImportTencentDocWebClip(ctx context.Context, in *ReqImportTencentDocWebClip, opts ...client.CallOption) (*RspImportTencentDocWebClip, error)
	// 知识库文档标签修改
	ModifyDocTab(ctx context.Context, in *ReqModifyDocTab, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询知识库文档标签
	DescribeDocTab(ctx context.Context, in *ReqDescribeDocTab, opts ...client.CallOption) (*RspDescribeDocTab, error)
	// 查询我的知识库文档
	DescribeMyDoc(ctx context.Context, in *ReqDescribeMyDoc, opts ...client.CallOption) (*RspDescribeMyDoc, error)
	// 创建星云任务
	CreateNebulaTask(ctx context.Context, in *ReqCreateNebulaTask, opts ...client.CallOption) (*RspCreateNebulaTask, error)
	// 查询星云任务
	DescribeNebulaTask(ctx context.Context, in *ReqDescribeNebulaTask, opts ...client.CallOption) (*RspDescribeNebulaTask, error)
	// 查询星云任务列表
	DescribeNebulaTaskList(ctx context.Context, in *ReqDescribeNebulaTaskList, opts ...client.CallOption) (*RspDescribeNebulaTaskList, error)
	// 查看投影坐标
	DescribeNebulaProjection(ctx context.Context, in *ReqDescribeNebulaProjection, opts ...client.CallOption) (*RspDescribeNebulaProjection, error)
	// 查看投影元数据
	DescribeNebulaData(ctx context.Context, in *ReqDescribeNebulaData, opts ...client.CallOption) (*RspDescribeNebulaData, error)
	// 批量创建星云任务，自动化任务
	BatchCreateNebulaTasks(ctx context.Context, in *ReqBatchCreateNebulaTasks, opts ...client.CallOption) (*RspBatchCreateNebulaTasks, error)
	// 创建助手已有collection数据初始化处理
	CreateAssistantCollectionInit(ctx context.Context, in *ReqCreateAssistantCollectionInit, opts ...client.CallOption) (*RspCreateAssistantCollectionInit, error)
	// 创建会话消息
	CreateChatMessage(ctx context.Context, in *ReqCreateChatMessage, opts ...client.CallOption) (*RspCreateChatMessage, error)
	// 更新会话消息
	UpdateChatMessageThink(ctx context.Context, in *ReqUpdateChatMessageThink, opts ...client.CallOption) (*emptypb.Empty, error)
	// 更新会话消息
	UpdateChatMessage(ctx context.Context, in *ReqUpdateChatMessage, opts ...client.CallOption) (*emptypb.Empty, error)
	// 推送消息
	PublishChatMessage(ctx context.Context, in *ReqPublishChatMessage, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建AI Agent任务
	CreateChatTaskMessage(ctx context.Context, in *ReqCreateChatTaskMessage, opts ...client.CallOption) (*RspCreateChatTaskMessage, error)
	// 发送请求AI消息同步
	SendMessageWithoutSaveSync(ctx context.Context, in *ReqSendMessageWithoutSaveSync, opts ...client.CallOption) (*RspSendMessageWithoutSaveSync, error)
	// 获取消息
	DescribeMessage(ctx context.Context, in *ReqDescribeMessage, opts ...client.CallOption) (*RspDescribeMessage, error)
	// 获取答案
	DescribeMessageByQuestionId(ctx context.Context, in *ReqDescribeMessageByQuestionId, opts ...client.CallOption) (*RspDescribeMessageByQuestionId, error)
	// 创建会话
	CreateUserChat(ctx context.Context, in *ReqCreateUserChat, opts ...client.CallOption) (*RspCreateUserChat, error)
	// 更新chat标签
	UpdateUserChatLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取会话列表
	DescribeUserChats(ctx context.Context, in *ReqDescribeUserChats, opts ...client.CallOption) (*RspDescribeUserChats, error)
	// 删除会话
	DeleteUserChat(ctx context.Context, in *ReqDeleteUserChat, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除消息
	DeleteChatMessage(ctx context.Context, in *ReqDeleteChatMessage, opts ...client.CallOption) (*emptypb.Empty, error)
	// 创建系统文档副本
	CreateSystemDocCopy(ctx context.Context, in *ReqCreateSystemDocCopy, opts ...client.CallOption) (*RspCreateSystemDocCopy, error)
	// 启用系统文档
	EnableSystemDoc(ctx context.Context, in *ReqEnableSystemDoc, opts ...client.CallOption) (*emptypb.Empty, error)
	// 停用系统文档
	DisableSystemDoc(ctx context.Context, in *ReqDisableSystemDoc, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除系统文档
	DeleteSystemDoc(ctx context.Context, in *ReqDeleteSystemDoc, opts ...client.CallOption) (*emptypb.Empty, error)
	// 评价AI回答
	RateAiAnswer(ctx context.Context, in *ReqRateAiAnswer, opts ...client.CallOption) (*emptypb.Empty, error)
	// 更新/创建用户反馈
	UpsertUserFeedback(ctx context.Context, in *ReqUpsertUserFeedback, opts ...client.CallOption) (*RspUpsertFeedback, error)
	// 更新/创建运营反馈
	UpsertOpFeedback(ctx context.Context, in *ReqUpsertOpFeedback, opts ...client.CallOption) (*RspUpsertFeedback, error)
	// 更新碳LIVE反馈
	UpsertMgmtFeedback(ctx context.Context, in *ReqUpsertMgmtFeedback, opts ...client.CallOption) (*RspUpsertFeedback, error)
	// 查询用户反馈列表
	GetFeedbacks(ctx context.Context, in *ReqGetFeedbacks, opts ...client.CallOption) (*RspGetFeedbacks, error)
	// 已读用户反馈
	ReadFeedback(ctx context.Context, in *ReqReadFeedback, opts ...client.CallOption) (*emptypb.Empty, error)
	// 采用用户反馈
	AcceptFeedback(ctx context.Context, in *ReqAcceptFeedback, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询用户反馈操作日志
	GetFeedbackLogs(ctx context.Context, in *ReqGetFeedbackLogs, opts ...client.CallOption) (*RspGetFeedbackLogs, error)
	// 查询文档列表
	GetDocs(ctx context.Context, in *ReqGetDocs, opts ...client.CallOption) (*RspGetDocs, error)
	// 获取消息doc
	DescribeMessageDocs(ctx context.Context, in *ReqDescribeMessageDocs, opts ...client.CallOption) (*RspDescribeMessageDocs, error)
	// 获取Suggest log
	DescribeSuggestLogs(ctx context.Context, in *ReqDescribeSuggestLog, opts ...client.CallOption) (*RspDescribeSuggestLog, error)
	// 获取消息log
	DescribeMessageLogs(ctx context.Context, in *ReqDescribeMessageLog, opts ...client.CallOption) (*RspDescribeMessageLog, error)
	// 修复更新collection
	SyncFixChatMessageCollection(ctx context.Context, in *ReqSyncFixChatMessageCollection, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取助手的聊天创建者列表
	GetAssistantChatCreators(ctx context.Context, in *ReqGetAssistantChatCreators, opts ...client.CallOption) (*RspGetAssistantChatCreators, error)
	// 运营端获取AI对话管理列表
	ListChat(ctx context.Context, in *ReqListChat, opts ...client.CallOption) (*RspListChat, error)
	// 运营端获取AI对话详情
	GetChatDetail(ctx context.Context, in *ReqGetChatDetail, opts ...client.CallOption) (*RspGetChatDetail, error)
	// 获取AI对话自定义标签
	ListCustomLabel(ctx context.Context, in *ReqListCustomLabel, opts ...client.CallOption) (*RspListCustomLabel, error)
	// 插入/更新对话标签
	UpsertCustomLabels(ctx context.Context, in *ReqUpsertCustomLabels, opts ...client.CallOption) (*RspUpsertCustomLabels, error)
	// 删除对话标签
	DeleteCustomLabels(ctx context.Context, in *ReqDeleteCustomLabels, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询ai助手列表
	ListAssistant(ctx context.Context, in *ReqListAssistant, opts ...client.CallOption) (*RspListAssistant, error)
	// 获取ai助手详情
	GetAssistant(ctx context.Context, in *ReqGetAssistant, opts ...client.CallOption) (*RspGetAssistant, error)
	// 获取小助手信息map
	GetAssistantInfoMap(ctx context.Context, in *ReqGetAssistantInfoMap, opts ...client.CallOption) (*RspGetAssistantInfoMap, error)
	// 获取人工坐席列表
	ListChatLiveAgent(ctx context.Context, in *ReqListChatLiveAgent, opts ...client.CallOption) (*RspListChatLiveAgent, error)
	// 切换人工坐席
	SwitchChatLiveAgent(ctx context.Context, in *ReqSwitchChatLiveAgent, opts ...client.CallOption) (*emptypb.Empty, error)
	// 人工坐席状态变更
	LiveAgentStatusChange(ctx context.Context, in *ReqLiveAgentStatusChange, opts ...client.CallOption) (*emptypb.Empty, error)
	// 发送ai公众号用到的rpc接口
	// 微信-评价微信公众号ai回答
	RateAiAnswerWechat(ctx context.Context, in *ReqRateAiAnswerWechat, opts ...client.CallOption) (*emptypb.Empty, error)
	// 微信-获取问题答案（重复的问题则会忽略）
	GetAnswerWechat(ctx context.Context, in *ReqGetAnswerWechat, opts ...client.CallOption) (*RspGetAnswerWechat, error)
	// 微信-获取图片提问的答案
	GetAnswerWechatForFile(ctx context.Context, in *ReqGetAnswerWechat, opts ...client.CallOption) (*RspGetAnswerWechat, error)
	// 微信-获取继续回答的答案
	GetAnswerWechatForContinue(ctx context.Context, in *ReqGetAnswerWechat, opts ...client.CallOption) (*RspGetAnswerWechat, error)
	// 微信-结束微信会话
	FinishChatWechat(ctx context.Context, in *ReqFinishChatWechat, opts ...client.CallOption) (*emptypb.Empty, error)
	// 微信-获取微信访问白名单
	GetWhiteListWechat(ctx context.Context, in *ReqGetWhiteListWechat, opts ...client.CallOption) (*RspGetWhiteListWechat, error)
	// 微信-更新微信问答message状态及答案记录信息
	UpdateChatMessageStateWechat(ctx context.Context, in *ReqUpdateChatMessageStateWechat, opts ...client.CallOption) (*emptypb.Empty, error)
	// 微信-获取聊天记录游标
	GetMessageCursorWechat(ctx context.Context, in *ReqGetMessageCursorWechat, opts ...client.CallOption) (*RspGetMessageCursorWechat, error)
	// 微信-获取用户会话信息
	GetChatWechat(ctx context.Context, in *ReqGetChatWechat, opts ...client.CallOption) (*RspGetChatWechat, error)
	// 微信-创建用户会话
	CreateChatWechat(ctx context.Context, in *ReqCreateChatWechat, opts ...client.CallOption) (*RspCreateChatWechat, error)
	// 微信-获取会话上一个问题的状态（是否已回复）
	GetLastQuestionStateWechat(ctx context.Context, in *ReqGetLastQuestionStateWechat, opts ...client.CallOption) (*RspGetLastQuestionStateWechat, error)
	// 微信-更新发送的默认消息记录
	UpdateChatMessageRecordWechat(ctx context.Context, in *ReqUpdateChatMessageRecordWechat, opts ...client.CallOption) (*RspUpdateChatMessageRecordWechat, error)
	// 创建发送记录
	CreateSendRecord(ctx context.Context, in *ReqCreateSendRecord, opts ...client.CallOption) (*RspCreateSendRecord, error)
	// 更新发送记录
	UpdateSendRecord(ctx context.Context, in *ReqUpdateSendRecord, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取助手的聊天记录(主要用于从t_chat_message 到 t_chat_send_record 的数据迁移)
	DescribeAssistantMessage(ctx context.Context, in *ReqDescribeAssistantMessage, opts ...client.CallOption) (*RspDescribeAssistantMessage, error)
	// 插入助手的聊天记录(主要用于从t_chat_message 到 t_chat_send_record 的数据迁移)
	InsertAssistantMessageRecord(ctx context.Context, in *ReqInsertAssistantMessageRecord, opts ...client.CallOption) (*emptypb.Empty, error)
	// 迁移用户会话记录信息
	MigrationChatMessageInfo(ctx context.Context, in *ReqMigrationChatMessageInfo, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取有会话的地区编码
	DescribeChatRegionCode(ctx context.Context, in *ReqDescribeChatRegionCode, opts ...client.CallOption) (*RspDescribeChatRegionCode, error)
	// 获取有教学反馈的地区编码
	DescribeFeedbackRegionCode(ctx context.Context, in *ReqDescribeFeedbackRegionCode, opts ...client.CallOption) (*RspDescribeFeedbackRegionCode, error)
	// 查询用户会话记录
	DescribeUserChatRecords(ctx context.Context, in *ReqDescribeUserChatRecords, opts ...client.CallOption) (*RspDescribeUserChatRecords, error)
	// 获取chat log权限
	DescribeChatLogAuthItem(ctx context.Context, in *ReqDescribeChatLogAuthItem, opts ...client.CallOption) (*RspDescribeChatLogAuthItem, error)
	// 获取网页title
	FetchHtmlTitles(ctx context.Context, in *ReqFetchHtmlTitles, opts ...client.CallOption) (*RspFetchHtmlTitles, error)
	// 查询助手
	GetAssistants(ctx context.Context, in *ReqGetAssistants, opts ...client.CallOption) (*RspGetAssistants, error)
	// 批量创建助手
	BatchCreateAssistant(ctx context.Context, in *ReqBatchCreateAssistant, opts ...client.CallOption) (*RspBatchCreateAssistant, error)
	// 批量更新助手
	BatchUpdateAssistant(ctx context.Context, in *ReqBatchUpdateAssistant, opts ...client.CallOption) (*emptypb.Empty, error)
	// 删除助手
	DeleteAssistant(ctx context.Context, in *ReqDeleteAssistant, opts ...client.CallOption) (*emptypb.Empty, error)
	// 查询助手日志
	GetAssistantLogs(ctx context.Context, in *ReqGetAssistantLogs, opts ...client.CallOption) (*RspGetAssistantLogs, error)
	// 获取助手下拉选项
	GetAssistantOptions(ctx context.Context, in *emptypb.Empty, opts ...client.CallOption) (*RspGetAssistantOptions, error)
	// 查询文档分段信息
	GetDocChunks(ctx context.Context, in *ReqGetDocChunks, opts ...client.CallOption) (*RspGetDocChunks, error)
	// 文档分段
	ChunkDoc(ctx context.Context, in *ReqChunkDoc, opts ...client.CallOption) (*RspChunkDoc, error)
	// 查询文档分段任务列表
	GetChunkDocTasks(ctx context.Context, in *ReqGetChunkDocTasks, opts ...client.CallOption) (*RspGetChunkDocTasks, error)
	// 查询文档的向量化模型
	GetDocEmbeddingModels(ctx context.Context, in *ReqGetDocEmbeddingModels, opts ...client.CallOption) (*RspGetDocEmbeddingModels, error)
	// 检查助手白名单
	CheckAssistantAllowlist(ctx context.Context, in *ReqCheckAssistantAllowlist, opts ...client.CallOption) (*RspCheckAssistantAllowlist, error)
	// 获取最近使用过的助手Id
	GetRecentlyUsedAssistantIds(ctx context.Context, in *ReqGetRecentlyUsedAssistantIds, opts ...client.CallOption) (*RspGetRecentlyUsedAssistantIds, error)
	// 获取最近使用过的助手
	GetRecentlyUsedAssistants(ctx context.Context, in *ReqGetRecentlyUsedAssistants, opts ...client.CallOption) (*RspGetRecentlyUsedAssistants, error)
	// 获取所有助手的管理员信息
	GetAssistantAdmin(ctx context.Context, in *ReqGetAssistantAdmin, opts ...client.CallOption) (*RspGetAssistantAdmin, error)
	// GetAssistantChatUser 获取助手会话的用户
	GetAssistantChatUser(ctx context.Context, in *ReqGetAssistantChatUser, opts ...client.CallOption) (*RspGetAssistantChatUser, error)
	// 创建聊天分享
	CreateChatShare(ctx context.Context, in *ReqCreateChatShare, opts ...client.CallOption) (*RspCreateChatShare, error)
	// 从分享继续聊天
	ContinueChatFromShare(ctx context.Context, in *ReqContinueChatFromShare, opts ...client.CallOption) (*RspContinueChatFromShare, error)
	// 获取用户分享列表
	ListChatShares(ctx context.Context, in *ReqListChatShares, opts ...client.CallOption) (*RspListChatShares, error)
	// 更新分享状态
	UpdateChatShareStatus(ctx context.Context, in *ReqUpdateChatShareStatus, opts ...client.CallOption) (*emptypb.Empty, error)
	// 获取分享访问记录
	ListChatShareAccesses(ctx context.Context, in *ReqListChatShareAccesses, opts ...client.CallOption) (*RspListChatShareAccesses, error)
	// 获取分享详情
	GetChatShareMessages(ctx context.Context, in *ReqGetChatShareMessages, opts ...client.CallOption) (*RspGetChatShareMessages, error)
	// 查询share记录
	GetChatShareRecord(ctx context.Context, in *ReqGetChatShareRecord, opts ...client.CallOption) (*RspGetChatShareRecord, error)
	// 记录分享访问
	RecordChatShareAccess(ctx context.Context, in *ReqRecordChatShareAccess, opts ...client.CallOption) (*RspRecordChatShareAccess, error)
}

type aiService struct {
	c    client.Client
	name string
}

func NewAiService(name string, c client.Client) AiService {
	return &aiService{
		c:    c,
		name: name,
	}
}

func (c *aiService) UpdateMessageDocs(ctx context.Context, in *ReqUpdateMessageDocs, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateMessageDocs", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMessageDocSnapshot(ctx context.Context, in *ReqDescribeMessageDocSnapshot, opts ...client.CallOption) (*RspDescribeMessageDocSnapshot, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMessageDocSnapshot", in)
	out := new(RspDescribeMessageDocSnapshot)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) StopAnswerReply(ctx context.Context, in *ReqStopAnswerReply, opts ...client.CallOption) (*RspStopAnswerReply, error) {
	req := c.c.NewRequest(c.name, "AiService.StopAnswerReply", in)
	out := new(RspStopAnswerReply)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) StopQuestionReply(ctx context.Context, in *ReqStopQuestionReply, opts ...client.CallOption) (*RspStopQuestionReply, error) {
	req := c.c.NewRequest(c.name, "AiService.StopQuestionReply", in)
	out := new(RspStopQuestionReply)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribePreMessageTask(ctx context.Context, in *ReqDescribePreMessageTask, opts ...client.CallOption) (*RspReqDescribePreMessageTask, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribePreMessageTask", in)
	out := new(RspReqDescribePreMessageTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeChatMessageFileState(ctx context.Context, in *ReqDescribeChatMessageFileState, opts ...client.CallOption) (*RspDescribeChatMessageFileState, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeChatMessageFileState", in)
	out := new(RspDescribeChatMessageFileState)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeChatAgentTaskByPreTask(ctx context.Context, in *ReqDescribeChatAgentTaskByPreTask, opts ...client.CallOption) (*RspDescribeChatAgentTaskByPreTask, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeChatAgentTaskByPreTask", in)
	out := new(RspDescribeChatAgentTaskByPreTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateChatMessageCollections(ctx context.Context, in *ReqUpdateChatMessageCollections, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateChatMessageCollections", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) FixSearchCollectionItems(ctx context.Context, in *ReqFixSearchCollectionItems, opts ...client.CallOption) (*RspFixSearchCollectionItems, error) {
	req := c.c.NewRequest(c.name, "AiService.FixSearchCollectionItems", in)
	out := new(RspFixSearchCollectionItems)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeChatAgentTask(ctx context.Context, in *ReqDescribeChatAgentTask, opts ...client.CallOption) (*RspDescribeChatAgentTask, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeChatAgentTask", in)
	out := new(RspDescribeChatAgentTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CheckChatPermission(ctx context.Context, in *ReqCheckChatPermission, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CheckChatPermission", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeChatQuestionAnswersByPage(ctx context.Context, in *ReqDescribeChatQuestionAnswersByPage, opts ...client.CallOption) (*RspDescribeChatQuestionAnswersByPage, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeChatQuestionAnswersByPage", in)
	out := new(RspDescribeChatQuestionAnswersByPage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateChatOperation(ctx context.Context, in *ReqCreateChatOperation, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateChatOperation", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ParseChatDoc(ctx context.Context, in *ReqParseChatDoc, opts ...client.CallOption) (*RspParseChatDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ParseChatDoc", in)
	out := new(RspParseChatDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateMessageText(ctx context.Context, in *ReqUpdateMessageText, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateMessageText", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateQaMatchMessage(ctx context.Context, in *ReqCreateQaMatchMessage, opts ...client.CallOption) (*RspCreateQaMatchMessage, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateQaMatchMessage", in)
	out := new(RspCreateQaMatchMessage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMessageMatchQa(ctx context.Context, in *ReqDescribeMessageMatchQa, opts ...client.CallOption) (*RspDescribeMessageMatchQa, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMessageMatchQa", in)
	out := new(RspDescribeMessageMatchQa)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ResendMessageSync(ctx context.Context, in *ReqResendMessageSync, opts ...client.CallOption) (*RspResendMessageSync, error) {
	req := c.c.NewRequest(c.name, "AiService.ResendMessageSync", in)
	out := new(RspResendMessageSync)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeExportChatMessages(ctx context.Context, in *ReqDescribeExportChatMessages, opts ...client.CallOption) (*RspDescribeExportChatMessages, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeExportChatMessages", in)
	out := new(RspDescribeExportChatMessages)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateMessageSuggestQuestion(ctx context.Context, in *ReqCreateMessageSuggestQuestion, opts ...client.CallOption) (*RspCreateMessageSuggestQuestion, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateMessageSuggestQuestion", in)
	out := new(RspCreateMessageSuggestQuestion)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeExportTasks(ctx context.Context, in *ReqDescribeExportTasks, opts ...client.CallOption) (*RspDescribeExportTasks, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeExportTasks", in)
	out := new(RspDescribeExportTasks)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateExportTask(ctx context.Context, in *ReqCreateExportTask, opts ...client.CallOption) (*RspCreateExportTask, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateExportTask", in)
	out := new(RspCreateExportTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateExportTask(ctx context.Context, in *ReqUpdateExportTask, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateExportTask", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListQA(ctx context.Context, in *ReqListQA, opts ...client.CallOption) (*RspListQA, error) {
	req := c.c.NewRequest(c.name, "AiService.ListQA", in)
	out := new(RspListQA)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateQAInBulk(ctx context.Context, in *ReqCreateQAInBulk, opts ...client.CallOption) (*RspCreateQAInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateQAInBulk", in)
	out := new(RspCreateQAInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateQA(ctx context.Context, in *ReqCreateQA, opts ...client.CallOption) (*RspCreateQA, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateQA", in)
	out := new(RspCreateQA)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateQA(ctx context.Context, in *ReqUpdateQA, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateQA", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateQAInBulk(ctx context.Context, in *ReqUpdateQAInBulk, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateQAInBulk", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListTextFile(ctx context.Context, in *ReqListTextFile, opts ...client.CallOption) (*RspListTextFile, error) {
	req := c.c.NewRequest(c.name, "AiService.ListTextFile", in)
	out := new(RspListTextFile)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateTextFileInBulk(ctx context.Context, in *ReqCreateTextFileInBulk, opts ...client.CallOption) (*RspCreateTextFileInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateTextFileInBulk", in)
	out := new(RspCreateTextFileInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateTextFile(ctx context.Context, in *ReqUpdateTextFile, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateTextFile", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateTextFileInBulk(ctx context.Context, in *ReqUpdateTextFileInBulk, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateTextFileInBulk", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DeleteDocInBulk(ctx context.Context, in *ReqDeleteDocInBulk, opts ...client.CallOption) (*RspDeleteDocInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.DeleteDocInBulk", in)
	out := new(RspDeleteDocInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) SearchCollectionOneShot(ctx context.Context, in *ReqSearchCollectionOneShot, opts ...client.CallOption) (*RspSearchCollectionOneShot, error) {
	req := c.c.NewRequest(c.name, "AiService.SearchCollectionOneShot", in)
	out := new(RspSearchCollectionOneShot)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ReparseTextFiles(ctx context.Context, in *ReqReparseTextFiles, opts ...client.CallOption) (*RspReparseTextFiles, error) {
	req := c.c.NewRequest(c.name, "AiService.ReparseTextFiles", in)
	out := new(RspReparseTextFiles)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) SearchCollection(ctx context.Context, in *ReqSearchCollection, opts ...client.CallOption) (*RspSearchCollection, error) {
	req := c.c.NewRequest(c.name, "AiService.SearchCollection", in)
	out := new(RspSearchCollection)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListCollection(ctx context.Context, in *ReqListCollection, opts ...client.CallOption) (*RspListCollection, error) {
	req := c.c.NewRequest(c.name, "AiService.ListCollection", in)
	out := new(RspListCollection)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListContributor(ctx context.Context, in *ReqListContributor, opts ...client.CallOption) (*RspListContributor, error) {
	req := c.c.NewRequest(c.name, "AiService.ListContributor", in)
	out := new(RspListContributor)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListUpdateBy(ctx context.Context, in *ReqListUpdateBy, opts ...client.CallOption) (*RspListUpdateBy, error) {
	req := c.c.NewRequest(c.name, "AiService.ListUpdateBy", in)
	out := new(RspListUpdateBy)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListCreateBy(ctx context.Context, in *ReqListUpdateBy, opts ...client.CallOption) (*RspListUpdateBy, error) {
	req := c.c.NewRequest(c.name, "AiService.ListCreateBy", in)
	out := new(RspListUpdateBy)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListSharedAssistant(ctx context.Context, in *ReqListSharedAssistant, opts ...client.CallOption) (*RspListSharedAssistant, error) {
	req := c.c.NewRequest(c.name, "AiService.ListSharedAssistant", in)
	out := new(RspListSharedAssistant)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ValidateQAInBulk(ctx context.Context, in *ReqValidateQAInBulk, opts ...client.CallOption) (*RspValidateQAInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.ValidateQAInBulk", in)
	out := new(RspValidateQAInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ValidateTextFileInBulk(ctx context.Context, in *ReqValidateTextFileInBulk, opts ...client.CallOption) (*RspValidateTextFileInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.ValidateTextFileInBulk", in)
	out := new(RspValidateTextFileInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CloneDocInBulk(ctx context.Context, in *ReqCloneDocInBulk, opts ...client.CallOption) (*RspCloneDocInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.CloneDocInBulk", in)
	out := new(RspCloneDocInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) OnOffDocInBulk(ctx context.Context, in *ReqOnOffDocInBulk, opts ...client.CallOption) (*RspOnOffDocInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.OnOffDocInBulk", in)
	out := new(RspOnOffDocInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListDocByRef(ctx context.Context, in *ReqListDocByRef, opts ...client.CallOption) (*RspListDocByRef, error) {
	req := c.c.NewRequest(c.name, "AiService.ListDocByRef", in)
	out := new(RspListDocByRef)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateDocLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateDocLabels", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ConvertCustomLabel(ctx context.Context, in *ReqConvertCustomLabel, opts ...client.CallOption) (*RspConvertCustomLabel, error) {
	req := c.c.NewRequest(c.name, "AiService.ConvertCustomLabel", in)
	out := new(RspConvertCustomLabel)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetCustomLabelValueTopN(ctx context.Context, in *ReqGetCustomLabelValueTopN, opts ...client.CallOption) (*RspGetCustomLabelValueTopN, error) {
	req := c.c.NewRequest(c.name, "AiService.GetCustomLabelValueTopN", in)
	out := new(RspGetCustomLabelValueTopN)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateDocAttrInBulk(ctx context.Context, in *ReqUpdateDocAttrInBulk, opts ...client.CallOption) (*RspUpdateDocAttrInBulk, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateDocAttrInBulk", in)
	out := new(RspUpdateDocAttrInBulk)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetTextFileTip(ctx context.Context, in *ReqGetTextFileTip, opts ...client.CallOption) (*RspGetTextFileTip, error) {
	req := c.c.NewRequest(c.name, "AiService.GetTextFileTip", in)
	out := new(RspGetTextFileTip)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateDocQuery(ctx context.Context, in *ReqCreateDocQuery, opts ...client.CallOption) (*RspCreateDocQuery, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateDocQuery", in)
	out := new(RspCreateDocQuery)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetQaTip(ctx context.Context, in *ReqGetQaTip, opts ...client.CallOption) (*RspGetQaTip, error) {
	req := c.c.NewRequest(c.name, "AiService.GetQaTip", in)
	out := new(RspGetQaTip)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateAllDocMd5(ctx context.Context, in *ReqCreateAllDocMd5, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateAllDocMd5", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateDocShareConfigSender(ctx context.Context, in *ReqCreateDocShareConfigSender, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateDocShareConfigSender", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListeDocShareConfigSender(ctx context.Context, in *ReqListeDocShareConfigSender, opts ...client.CallOption) (*RspListeDocShareConfigSender, error) {
	req := c.c.NewRequest(c.name, "AiService.ListeDocShareConfigSender", in)
	out := new(RspListeDocShareConfigSender)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListAssistantCanShareDoc(ctx context.Context, in *ReqListAssistantCanShareDoc, opts ...client.CallOption) (*RspListAssistantCanShareDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ListAssistantCanShareDoc", in)
	out := new(RspListAssistantCanShareDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListTeamCanShareDoc(ctx context.Context, in *ReqListTeamCanShareDoc, opts ...client.CallOption) (*RspListTeamCanShareDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ListTeamCanShareDoc", in)
	out := new(RspListTeamCanShareDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListUserCanShareDoc(ctx context.Context, in *ReqListUserCanShareDoc, opts ...client.CallOption) (*RspListUserCanShareDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ListUserCanShareDoc", in)
	out := new(RspListUserCanShareDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListSharedTeam(ctx context.Context, in *ReqListSharedTeam, opts ...client.CallOption) (*RspListSharedTeam, error) {
	req := c.c.NewRequest(c.name, "AiService.ListSharedTeam", in)
	out := new(RspListSharedTeam)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListSharedUser(ctx context.Context, in *ReqListSharedUser, opts ...client.CallOption) (*RspListSharedUser, error) {
	req := c.c.NewRequest(c.name, "AiService.ListSharedUser", in)
	out := new(RspListSharedUser)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListMyAssistantIds(ctx context.Context, in *ReqListMyAssistantIds, opts ...client.CallOption) (*RspListMyAssistantIds, error) {
	req := c.c.NewRequest(c.name, "AiService.ListMyAssistantIds", in)
	out := new(RspListMyAssistantIds)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateDocShareConfigReceiverAssistant(ctx context.Context, in *ReqCreateDocShareConfigReceiverAssistant, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateDocShareConfigReceiverAssistant", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqCreateDocShareConfigReceiverUserTeam, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateDocShareConfigReceiverUserTeam", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListDocShareConfigReceiverAssistant(ctx context.Context, in *ReqListDocShareConfigReceiverAssistant, opts ...client.CallOption) (*RspListDocShareConfigReceiverAssistant, error) {
	req := c.c.NewRequest(c.name, "AiService.ListDocShareConfigReceiverAssistant", in)
	out := new(RspListDocShareConfigReceiverAssistant)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqListDocShareConfigReceiverUserTeam, opts ...client.CallOption) (*RspListDocShareConfigReceiverUserTeam, error) {
	req := c.c.NewRequest(c.name, "AiService.ListDocShareConfigReceiverUserTeam", in)
	out := new(RspListDocShareConfigReceiverUserTeam)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateDocShare(ctx context.Context, in *ReqCreateDocShare, opts ...client.CallOption) (*RspCreateDocShare, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateDocShare", in)
	out := new(RspCreateDocShare)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateDocShareAssistant(ctx context.Context, in *ReqCreateDocShare, opts ...client.CallOption) (*RspCreateDocShare, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateDocShareAssistant", in)
	out := new(RspCreateDocShare)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateOpenWechat(ctx context.Context, in *ReqCreateOpenWechat, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateOpenWechat", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeOpenWechat(ctx context.Context, in *ReqDescribeOpenWechat, opts ...client.CallOption) (*RspDescribeOpenWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeOpenWechat", in)
	out := new(RspDescribeOpenWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) AuthTencentCode(ctx context.Context, in *ReqAuthTencentCode, opts ...client.CallOption) (*RspAuthTencentCode, error) {
	req := c.c.NewRequest(c.name, "AiService.AuthTencentCode", in)
	out := new(RspAuthTencentCode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeTokenIsEmpty(ctx context.Context, in *ReqDescribeTokenIsEmpty, opts ...client.CallOption) (*RspDescribeTokenIsEmpty, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeTokenIsEmpty", in)
	out := new(RspDescribeTokenIsEmpty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListExternalSourceUser(ctx context.Context, in *ReqListExternalSourceUser, opts ...client.CallOption) (*RspListExternalSourceUser, error) {
	req := c.c.NewRequest(c.name, "AiService.ListExternalSourceUser", in)
	out := new(RspListExternalSourceUser)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateGTBText(ctx context.Context, in *ReqCreateGTBText, opts ...client.CallOption) (*RspCreateGTBText, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateGTBText", in)
	out := new(RspCreateGTBText)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeTencentDocTask(ctx context.Context, in *ReqDescribeTencentDocTask, opts ...client.CallOption) (*RspDescribeTencentDocTask, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeTencentDocTask", in)
	out := new(RspDescribeTencentDocTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DelTencentDocAuth(ctx context.Context, in *ReqDelTencentDocAuth, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DelTencentDocAuth", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeGTBText(ctx context.Context, in *ReqDescribeGTBText, opts ...client.CallOption) (*RspDescribeGTBText, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeGTBText", in)
	out := new(RspDescribeGTBText)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateTencentDocAuthUrl(ctx context.Context, in *ReqCreateTencentDocAuthUrl, opts ...client.CallOption) (*RspCreateTencentDocAuthUrl, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateTencentDocAuthUrl", in)
	out := new(RspCreateTencentDocAuthUrl)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeDocList(ctx context.Context, in *ReqDescribeTencentDocList, opts ...client.CallOption) (*RspDescribeTencentDocList, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeDocList", in)
	out := new(RspDescribeTencentDocList)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ImportTencentDoc(ctx context.Context, in *ReqImportTencentDoc, opts ...client.CallOption) (*RspImportTencentDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ImportTencentDoc", in)
	out := new(RspImportTencentDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ReimportTencentDoc(ctx context.Context, in *ReqReimportTencentDoc, opts ...client.CallOption) (*RspReimportTencentDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ReimportTencentDoc", in)
	out := new(RspReimportTencentDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ImportTencentDocWebClip(ctx context.Context, in *ReqImportTencentDocWebClip, opts ...client.CallOption) (*RspImportTencentDocWebClip, error) {
	req := c.c.NewRequest(c.name, "AiService.ImportTencentDocWebClip", in)
	out := new(RspImportTencentDocWebClip)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ModifyDocTab(ctx context.Context, in *ReqModifyDocTab, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.ModifyDocTab", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeDocTab(ctx context.Context, in *ReqDescribeDocTab, opts ...client.CallOption) (*RspDescribeDocTab, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeDocTab", in)
	out := new(RspDescribeDocTab)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMyDoc(ctx context.Context, in *ReqDescribeMyDoc, opts ...client.CallOption) (*RspDescribeMyDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMyDoc", in)
	out := new(RspDescribeMyDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateNebulaTask(ctx context.Context, in *ReqCreateNebulaTask, opts ...client.CallOption) (*RspCreateNebulaTask, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateNebulaTask", in)
	out := new(RspCreateNebulaTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeNebulaTask(ctx context.Context, in *ReqDescribeNebulaTask, opts ...client.CallOption) (*RspDescribeNebulaTask, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeNebulaTask", in)
	out := new(RspDescribeNebulaTask)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeNebulaTaskList(ctx context.Context, in *ReqDescribeNebulaTaskList, opts ...client.CallOption) (*RspDescribeNebulaTaskList, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeNebulaTaskList", in)
	out := new(RspDescribeNebulaTaskList)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeNebulaProjection(ctx context.Context, in *ReqDescribeNebulaProjection, opts ...client.CallOption) (*RspDescribeNebulaProjection, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeNebulaProjection", in)
	out := new(RspDescribeNebulaProjection)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeNebulaData(ctx context.Context, in *ReqDescribeNebulaData, opts ...client.CallOption) (*RspDescribeNebulaData, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeNebulaData", in)
	out := new(RspDescribeNebulaData)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) BatchCreateNebulaTasks(ctx context.Context, in *ReqBatchCreateNebulaTasks, opts ...client.CallOption) (*RspBatchCreateNebulaTasks, error) {
	req := c.c.NewRequest(c.name, "AiService.BatchCreateNebulaTasks", in)
	out := new(RspBatchCreateNebulaTasks)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateAssistantCollectionInit(ctx context.Context, in *ReqCreateAssistantCollectionInit, opts ...client.CallOption) (*RspCreateAssistantCollectionInit, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateAssistantCollectionInit", in)
	out := new(RspCreateAssistantCollectionInit)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateChatMessage(ctx context.Context, in *ReqCreateChatMessage, opts ...client.CallOption) (*RspCreateChatMessage, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateChatMessage", in)
	out := new(RspCreateChatMessage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateChatMessageThink(ctx context.Context, in *ReqUpdateChatMessageThink, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateChatMessageThink", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateChatMessage(ctx context.Context, in *ReqUpdateChatMessage, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateChatMessage", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) PublishChatMessage(ctx context.Context, in *ReqPublishChatMessage, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.PublishChatMessage", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateChatTaskMessage(ctx context.Context, in *ReqCreateChatTaskMessage, opts ...client.CallOption) (*RspCreateChatTaskMessage, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateChatTaskMessage", in)
	out := new(RspCreateChatTaskMessage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) SendMessageWithoutSaveSync(ctx context.Context, in *ReqSendMessageWithoutSaveSync, opts ...client.CallOption) (*RspSendMessageWithoutSaveSync, error) {
	req := c.c.NewRequest(c.name, "AiService.SendMessageWithoutSaveSync", in)
	out := new(RspSendMessageWithoutSaveSync)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMessage(ctx context.Context, in *ReqDescribeMessage, opts ...client.CallOption) (*RspDescribeMessage, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMessage", in)
	out := new(RspDescribeMessage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMessageByQuestionId(ctx context.Context, in *ReqDescribeMessageByQuestionId, opts ...client.CallOption) (*RspDescribeMessageByQuestionId, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMessageByQuestionId", in)
	out := new(RspDescribeMessageByQuestionId)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateUserChat(ctx context.Context, in *ReqCreateUserChat, opts ...client.CallOption) (*RspCreateUserChat, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateUserChat", in)
	out := new(RspCreateUserChat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateUserChatLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateUserChatLabels", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeUserChats(ctx context.Context, in *ReqDescribeUserChats, opts ...client.CallOption) (*RspDescribeUserChats, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeUserChats", in)
	out := new(RspDescribeUserChats)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DeleteUserChat(ctx context.Context, in *ReqDeleteUserChat, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DeleteUserChat", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DeleteChatMessage(ctx context.Context, in *ReqDeleteChatMessage, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DeleteChatMessage", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateSystemDocCopy(ctx context.Context, in *ReqCreateSystemDocCopy, opts ...client.CallOption) (*RspCreateSystemDocCopy, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateSystemDocCopy", in)
	out := new(RspCreateSystemDocCopy)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) EnableSystemDoc(ctx context.Context, in *ReqEnableSystemDoc, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.EnableSystemDoc", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DisableSystemDoc(ctx context.Context, in *ReqDisableSystemDoc, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DisableSystemDoc", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DeleteSystemDoc(ctx context.Context, in *ReqDeleteSystemDoc, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DeleteSystemDoc", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) RateAiAnswer(ctx context.Context, in *ReqRateAiAnswer, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.RateAiAnswer", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpsertUserFeedback(ctx context.Context, in *ReqUpsertUserFeedback, opts ...client.CallOption) (*RspUpsertFeedback, error) {
	req := c.c.NewRequest(c.name, "AiService.UpsertUserFeedback", in)
	out := new(RspUpsertFeedback)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpsertOpFeedback(ctx context.Context, in *ReqUpsertOpFeedback, opts ...client.CallOption) (*RspUpsertFeedback, error) {
	req := c.c.NewRequest(c.name, "AiService.UpsertOpFeedback", in)
	out := new(RspUpsertFeedback)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpsertMgmtFeedback(ctx context.Context, in *ReqUpsertMgmtFeedback, opts ...client.CallOption) (*RspUpsertFeedback, error) {
	req := c.c.NewRequest(c.name, "AiService.UpsertMgmtFeedback", in)
	out := new(RspUpsertFeedback)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetFeedbacks(ctx context.Context, in *ReqGetFeedbacks, opts ...client.CallOption) (*RspGetFeedbacks, error) {
	req := c.c.NewRequest(c.name, "AiService.GetFeedbacks", in)
	out := new(RspGetFeedbacks)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ReadFeedback(ctx context.Context, in *ReqReadFeedback, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.ReadFeedback", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) AcceptFeedback(ctx context.Context, in *ReqAcceptFeedback, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.AcceptFeedback", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetFeedbackLogs(ctx context.Context, in *ReqGetFeedbackLogs, opts ...client.CallOption) (*RspGetFeedbackLogs, error) {
	req := c.c.NewRequest(c.name, "AiService.GetFeedbackLogs", in)
	out := new(RspGetFeedbackLogs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetDocs(ctx context.Context, in *ReqGetDocs, opts ...client.CallOption) (*RspGetDocs, error) {
	req := c.c.NewRequest(c.name, "AiService.GetDocs", in)
	out := new(RspGetDocs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMessageDocs(ctx context.Context, in *ReqDescribeMessageDocs, opts ...client.CallOption) (*RspDescribeMessageDocs, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMessageDocs", in)
	out := new(RspDescribeMessageDocs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeSuggestLogs(ctx context.Context, in *ReqDescribeSuggestLog, opts ...client.CallOption) (*RspDescribeSuggestLog, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeSuggestLogs", in)
	out := new(RspDescribeSuggestLog)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeMessageLogs(ctx context.Context, in *ReqDescribeMessageLog, opts ...client.CallOption) (*RspDescribeMessageLog, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeMessageLogs", in)
	out := new(RspDescribeMessageLog)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) SyncFixChatMessageCollection(ctx context.Context, in *ReqSyncFixChatMessageCollection, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.SyncFixChatMessageCollection", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistantChatCreators(ctx context.Context, in *ReqGetAssistantChatCreators, opts ...client.CallOption) (*RspGetAssistantChatCreators, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistantChatCreators", in)
	out := new(RspGetAssistantChatCreators)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListChat(ctx context.Context, in *ReqListChat, opts ...client.CallOption) (*RspListChat, error) {
	req := c.c.NewRequest(c.name, "AiService.ListChat", in)
	out := new(RspListChat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetChatDetail(ctx context.Context, in *ReqGetChatDetail, opts ...client.CallOption) (*RspGetChatDetail, error) {
	req := c.c.NewRequest(c.name, "AiService.GetChatDetail", in)
	out := new(RspGetChatDetail)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListCustomLabel(ctx context.Context, in *ReqListCustomLabel, opts ...client.CallOption) (*RspListCustomLabel, error) {
	req := c.c.NewRequest(c.name, "AiService.ListCustomLabel", in)
	out := new(RspListCustomLabel)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpsertCustomLabels(ctx context.Context, in *ReqUpsertCustomLabels, opts ...client.CallOption) (*RspUpsertCustomLabels, error) {
	req := c.c.NewRequest(c.name, "AiService.UpsertCustomLabels", in)
	out := new(RspUpsertCustomLabels)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DeleteCustomLabels(ctx context.Context, in *ReqDeleteCustomLabels, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DeleteCustomLabels", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListAssistant(ctx context.Context, in *ReqListAssistant, opts ...client.CallOption) (*RspListAssistant, error) {
	req := c.c.NewRequest(c.name, "AiService.ListAssistant", in)
	out := new(RspListAssistant)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistant(ctx context.Context, in *ReqGetAssistant, opts ...client.CallOption) (*RspGetAssistant, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistant", in)
	out := new(RspGetAssistant)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistantInfoMap(ctx context.Context, in *ReqGetAssistantInfoMap, opts ...client.CallOption) (*RspGetAssistantInfoMap, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistantInfoMap", in)
	out := new(RspGetAssistantInfoMap)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListChatLiveAgent(ctx context.Context, in *ReqListChatLiveAgent, opts ...client.CallOption) (*RspListChatLiveAgent, error) {
	req := c.c.NewRequest(c.name, "AiService.ListChatLiveAgent", in)
	out := new(RspListChatLiveAgent)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) SwitchChatLiveAgent(ctx context.Context, in *ReqSwitchChatLiveAgent, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.SwitchChatLiveAgent", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) LiveAgentStatusChange(ctx context.Context, in *ReqLiveAgentStatusChange, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.LiveAgentStatusChange", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) RateAiAnswerWechat(ctx context.Context, in *ReqRateAiAnswerWechat, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.RateAiAnswerWechat", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAnswerWechat(ctx context.Context, in *ReqGetAnswerWechat, opts ...client.CallOption) (*RspGetAnswerWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAnswerWechat", in)
	out := new(RspGetAnswerWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAnswerWechatForFile(ctx context.Context, in *ReqGetAnswerWechat, opts ...client.CallOption) (*RspGetAnswerWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAnswerWechatForFile", in)
	out := new(RspGetAnswerWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAnswerWechatForContinue(ctx context.Context, in *ReqGetAnswerWechat, opts ...client.CallOption) (*RspGetAnswerWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAnswerWechatForContinue", in)
	out := new(RspGetAnswerWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) FinishChatWechat(ctx context.Context, in *ReqFinishChatWechat, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.FinishChatWechat", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetWhiteListWechat(ctx context.Context, in *ReqGetWhiteListWechat, opts ...client.CallOption) (*RspGetWhiteListWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetWhiteListWechat", in)
	out := new(RspGetWhiteListWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateChatMessageStateWechat(ctx context.Context, in *ReqUpdateChatMessageStateWechat, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateChatMessageStateWechat", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetMessageCursorWechat(ctx context.Context, in *ReqGetMessageCursorWechat, opts ...client.CallOption) (*RspGetMessageCursorWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetMessageCursorWechat", in)
	out := new(RspGetMessageCursorWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetChatWechat(ctx context.Context, in *ReqGetChatWechat, opts ...client.CallOption) (*RspGetChatWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetChatWechat", in)
	out := new(RspGetChatWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateChatWechat(ctx context.Context, in *ReqCreateChatWechat, opts ...client.CallOption) (*RspCreateChatWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateChatWechat", in)
	out := new(RspCreateChatWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetLastQuestionStateWechat(ctx context.Context, in *ReqGetLastQuestionStateWechat, opts ...client.CallOption) (*RspGetLastQuestionStateWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.GetLastQuestionStateWechat", in)
	out := new(RspGetLastQuestionStateWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateChatMessageRecordWechat(ctx context.Context, in *ReqUpdateChatMessageRecordWechat, opts ...client.CallOption) (*RspUpdateChatMessageRecordWechat, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateChatMessageRecordWechat", in)
	out := new(RspUpdateChatMessageRecordWechat)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateSendRecord(ctx context.Context, in *ReqCreateSendRecord, opts ...client.CallOption) (*RspCreateSendRecord, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateSendRecord", in)
	out := new(RspCreateSendRecord)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateSendRecord(ctx context.Context, in *ReqUpdateSendRecord, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateSendRecord", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeAssistantMessage(ctx context.Context, in *ReqDescribeAssistantMessage, opts ...client.CallOption) (*RspDescribeAssistantMessage, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeAssistantMessage", in)
	out := new(RspDescribeAssistantMessage)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) InsertAssistantMessageRecord(ctx context.Context, in *ReqInsertAssistantMessageRecord, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.InsertAssistantMessageRecord", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) MigrationChatMessageInfo(ctx context.Context, in *ReqMigrationChatMessageInfo, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.MigrationChatMessageInfo", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeChatRegionCode(ctx context.Context, in *ReqDescribeChatRegionCode, opts ...client.CallOption) (*RspDescribeChatRegionCode, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeChatRegionCode", in)
	out := new(RspDescribeChatRegionCode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeFeedbackRegionCode(ctx context.Context, in *ReqDescribeFeedbackRegionCode, opts ...client.CallOption) (*RspDescribeFeedbackRegionCode, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeFeedbackRegionCode", in)
	out := new(RspDescribeFeedbackRegionCode)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeUserChatRecords(ctx context.Context, in *ReqDescribeUserChatRecords, opts ...client.CallOption) (*RspDescribeUserChatRecords, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeUserChatRecords", in)
	out := new(RspDescribeUserChatRecords)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DescribeChatLogAuthItem(ctx context.Context, in *ReqDescribeChatLogAuthItem, opts ...client.CallOption) (*RspDescribeChatLogAuthItem, error) {
	req := c.c.NewRequest(c.name, "AiService.DescribeChatLogAuthItem", in)
	out := new(RspDescribeChatLogAuthItem)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) FetchHtmlTitles(ctx context.Context, in *ReqFetchHtmlTitles, opts ...client.CallOption) (*RspFetchHtmlTitles, error) {
	req := c.c.NewRequest(c.name, "AiService.FetchHtmlTitles", in)
	out := new(RspFetchHtmlTitles)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistants(ctx context.Context, in *ReqGetAssistants, opts ...client.CallOption) (*RspGetAssistants, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistants", in)
	out := new(RspGetAssistants)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) BatchCreateAssistant(ctx context.Context, in *ReqBatchCreateAssistant, opts ...client.CallOption) (*RspBatchCreateAssistant, error) {
	req := c.c.NewRequest(c.name, "AiService.BatchCreateAssistant", in)
	out := new(RspBatchCreateAssistant)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) BatchUpdateAssistant(ctx context.Context, in *ReqBatchUpdateAssistant, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.BatchUpdateAssistant", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) DeleteAssistant(ctx context.Context, in *ReqDeleteAssistant, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.DeleteAssistant", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistantLogs(ctx context.Context, in *ReqGetAssistantLogs, opts ...client.CallOption) (*RspGetAssistantLogs, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistantLogs", in)
	out := new(RspGetAssistantLogs)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistantOptions(ctx context.Context, in *emptypb.Empty, opts ...client.CallOption) (*RspGetAssistantOptions, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistantOptions", in)
	out := new(RspGetAssistantOptions)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetDocChunks(ctx context.Context, in *ReqGetDocChunks, opts ...client.CallOption) (*RspGetDocChunks, error) {
	req := c.c.NewRequest(c.name, "AiService.GetDocChunks", in)
	out := new(RspGetDocChunks)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ChunkDoc(ctx context.Context, in *ReqChunkDoc, opts ...client.CallOption) (*RspChunkDoc, error) {
	req := c.c.NewRequest(c.name, "AiService.ChunkDoc", in)
	out := new(RspChunkDoc)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetChunkDocTasks(ctx context.Context, in *ReqGetChunkDocTasks, opts ...client.CallOption) (*RspGetChunkDocTasks, error) {
	req := c.c.NewRequest(c.name, "AiService.GetChunkDocTasks", in)
	out := new(RspGetChunkDocTasks)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetDocEmbeddingModels(ctx context.Context, in *ReqGetDocEmbeddingModels, opts ...client.CallOption) (*RspGetDocEmbeddingModels, error) {
	req := c.c.NewRequest(c.name, "AiService.GetDocEmbeddingModels", in)
	out := new(RspGetDocEmbeddingModels)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CheckAssistantAllowlist(ctx context.Context, in *ReqCheckAssistantAllowlist, opts ...client.CallOption) (*RspCheckAssistantAllowlist, error) {
	req := c.c.NewRequest(c.name, "AiService.CheckAssistantAllowlist", in)
	out := new(RspCheckAssistantAllowlist)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetRecentlyUsedAssistantIds(ctx context.Context, in *ReqGetRecentlyUsedAssistantIds, opts ...client.CallOption) (*RspGetRecentlyUsedAssistantIds, error) {
	req := c.c.NewRequest(c.name, "AiService.GetRecentlyUsedAssistantIds", in)
	out := new(RspGetRecentlyUsedAssistantIds)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetRecentlyUsedAssistants(ctx context.Context, in *ReqGetRecentlyUsedAssistants, opts ...client.CallOption) (*RspGetRecentlyUsedAssistants, error) {
	req := c.c.NewRequest(c.name, "AiService.GetRecentlyUsedAssistants", in)
	out := new(RspGetRecentlyUsedAssistants)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistantAdmin(ctx context.Context, in *ReqGetAssistantAdmin, opts ...client.CallOption) (*RspGetAssistantAdmin, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistantAdmin", in)
	out := new(RspGetAssistantAdmin)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetAssistantChatUser(ctx context.Context, in *ReqGetAssistantChatUser, opts ...client.CallOption) (*RspGetAssistantChatUser, error) {
	req := c.c.NewRequest(c.name, "AiService.GetAssistantChatUser", in)
	out := new(RspGetAssistantChatUser)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) CreateChatShare(ctx context.Context, in *ReqCreateChatShare, opts ...client.CallOption) (*RspCreateChatShare, error) {
	req := c.c.NewRequest(c.name, "AiService.CreateChatShare", in)
	out := new(RspCreateChatShare)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ContinueChatFromShare(ctx context.Context, in *ReqContinueChatFromShare, opts ...client.CallOption) (*RspContinueChatFromShare, error) {
	req := c.c.NewRequest(c.name, "AiService.ContinueChatFromShare", in)
	out := new(RspContinueChatFromShare)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListChatShares(ctx context.Context, in *ReqListChatShares, opts ...client.CallOption) (*RspListChatShares, error) {
	req := c.c.NewRequest(c.name, "AiService.ListChatShares", in)
	out := new(RspListChatShares)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) UpdateChatShareStatus(ctx context.Context, in *ReqUpdateChatShareStatus, opts ...client.CallOption) (*emptypb.Empty, error) {
	req := c.c.NewRequest(c.name, "AiService.UpdateChatShareStatus", in)
	out := new(emptypb.Empty)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) ListChatShareAccesses(ctx context.Context, in *ReqListChatShareAccesses, opts ...client.CallOption) (*RspListChatShareAccesses, error) {
	req := c.c.NewRequest(c.name, "AiService.ListChatShareAccesses", in)
	out := new(RspListChatShareAccesses)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetChatShareMessages(ctx context.Context, in *ReqGetChatShareMessages, opts ...client.CallOption) (*RspGetChatShareMessages, error) {
	req := c.c.NewRequest(c.name, "AiService.GetChatShareMessages", in)
	out := new(RspGetChatShareMessages)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) GetChatShareRecord(ctx context.Context, in *ReqGetChatShareRecord, opts ...client.CallOption) (*RspGetChatShareRecord, error) {
	req := c.c.NewRequest(c.name, "AiService.GetChatShareRecord", in)
	out := new(RspGetChatShareRecord)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiService) RecordChatShareAccess(ctx context.Context, in *ReqRecordChatShareAccess, opts ...client.CallOption) (*RspRecordChatShareAccess, error) {
	req := c.c.NewRequest(c.name, "AiService.RecordChatShareAccess", in)
	out := new(RspRecordChatShareAccess)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for AiService service

type AiServiceHandler interface {
	// 更新消息doc快照
	UpdateMessageDocs(context.Context, *ReqUpdateMessageDocs, *emptypb.Empty) error
	// 获取消息doc快照
	DescribeMessageDocSnapshot(context.Context, *ReqDescribeMessageDocSnapshot, *RspDescribeMessageDocSnapshot) error
	// 停止消息发送
	StopAnswerReply(context.Context, *ReqStopAnswerReply, *RspStopAnswerReply) error
	// 停止消息发送
	StopQuestionReply(context.Context, *ReqStopQuestionReply, *RspStopQuestionReply) error
	// 查询前置消息
	DescribePreMessageTask(context.Context, *ReqDescribePreMessageTask, *RspReqDescribePreMessageTask) error
	// 查询文件解析状态
	DescribeChatMessageFileState(context.Context, *ReqDescribeChatMessageFileState, *RspDescribeChatMessageFileState) error
	// 查询当前chat需要执行的tasks
	DescribeChatAgentTaskByPreTask(context.Context, *ReqDescribeChatAgentTaskByPreTask, *RspDescribeChatAgentTaskByPreTask) error
	// 更新message collections
	UpdateChatMessageCollections(context.Context, *ReqUpdateChatMessageCollections, *emptypb.Empty) error
	// 修复search collection items
	FixSearchCollectionItems(context.Context, *ReqFixSearchCollectionItems, *RspFixSearchCollectionItems) error
	// 查询agent任务
	DescribeChatAgentTask(context.Context, *ReqDescribeChatAgentTask, *RspDescribeChatAgentTask) error
	// 检查会话权限
	CheckChatPermission(context.Context, *ReqCheckChatPermission, *emptypb.Empty) error
	// 获取会话聊天记录
	DescribeChatQuestionAnswersByPage(context.Context, *ReqDescribeChatQuestionAnswersByPage, *RspDescribeChatQuestionAnswersByPage) error
	// 创建更新
	CreateChatOperation(context.Context, *ReqCreateChatOperation, *emptypb.Empty) error
	// 将对话中的文件转换成文本
	ParseChatDoc(context.Context, *ReqParseChatDoc, *RspParseChatDoc) error
	// 更新message 文本
	UpdateMessageText(context.Context, *ReqUpdateMessageText, *emptypb.Empty) error
	// 创建QA匹配模式命中回答
	CreateQaMatchMessage(context.Context, *ReqCreateQaMatchMessage, *RspCreateQaMatchMessage) error
	// 查询qa模式匹配
	DescribeMessageMatchQa(context.Context, *ReqDescribeMessageMatchQa, *RspDescribeMessageMatchQa) error
	// 重新发送
	ResendMessageSync(context.Context, *ReqResendMessageSync, *RspResendMessageSync) error
	// 获取导出消息体
	DescribeExportChatMessages(context.Context, *ReqDescribeExportChatMessages, *RspDescribeExportChatMessages) error
	// 创建消息建议问题
	CreateMessageSuggestQuestion(context.Context, *ReqCreateMessageSuggestQuestion, *RspCreateMessageSuggestQuestion) error
	// 查询导出任务列表
	DescribeExportTasks(context.Context, *ReqDescribeExportTasks, *RspDescribeExportTasks) error
	// 创建导出任务
	CreateExportTask(context.Context, *ReqCreateExportTask, *RspCreateExportTask) error
	// 更新导出任务
	UpdateExportTask(context.Context, *ReqUpdateExportTask, *emptypb.Empty) error
	// 获取QA列表
	ListQA(context.Context, *ReqListQA, *RspListQA) error
	// 批量创建QA
	CreateQAInBulk(context.Context, *ReqCreateQAInBulk, *RspCreateQAInBulk) error
	// 创建QA
	CreateQA(context.Context, *ReqCreateQA, *RspCreateQA) error
	// 更新QA
	UpdateQA(context.Context, *ReqUpdateQA, *emptypb.Empty) error
	// 批量更新QA
	UpdateQAInBulk(context.Context, *ReqUpdateQAInBulk, *emptypb.Empty) error
	// 获取文本/文件列表
	ListTextFile(context.Context, *ReqListTextFile, *RspListTextFile) error
	// 创建文本/文科
	CreateTextFileInBulk(context.Context, *ReqCreateTextFileInBulk, *RspCreateTextFileInBulk) error
	// 更新文本/文件
	UpdateTextFile(context.Context, *ReqUpdateTextFile, *emptypb.Empty) error
	// 批量更新文本/文件
	UpdateTextFileInBulk(context.Context, *ReqUpdateTextFileInBulk, *emptypb.Empty) error
	// 删除QA/文本/文件
	DeleteDocInBulk(context.Context, *ReqDeleteDocInBulk, *RspDeleteDocInBulk) error
	// 向量搜索带命中
	SearchCollectionOneShot(context.Context, *ReqSearchCollectionOneShot, *RspSearchCollectionOneShot) error
	// 重新解析doc文件
	ReparseTextFiles(context.Context, *ReqReparseTextFiles, *RspReparseTextFiles) error
	// 向量搜索
	SearchCollection(context.Context, *ReqSearchCollection, *RspSearchCollection) error
	// deprecated 获取collection列表
	ListCollection(context.Context, *ReqListCollection, *RspListCollection) error
	// 获取贡献者列表
	ListContributor(context.Context, *ReqListContributor, *RspListContributor) error
	// 获取最近更新人列表
	ListUpdateBy(context.Context, *ReqListUpdateBy, *RspListUpdateBy) error
	// 获取最近创建人人列表
	ListCreateBy(context.Context, *ReqListUpdateBy, *RspListUpdateBy) error
	// 获取已分享的助手列表，用于表头筛选
	ListSharedAssistant(context.Context, *ReqListSharedAssistant, *RspListSharedAssistant) error
	// 校验QA
	ValidateQAInBulk(context.Context, *ReqValidateQAInBulk, *RspValidateQAInBulk) error
	// 校验文本/文件
	ValidateTextFileInBulk(context.Context, *ReqValidateTextFileInBulk, *RspValidateTextFileInBulk) error
	// 克隆QA/文本/文件
	CloneDocInBulk(context.Context, *ReqCloneDocInBulk, *RspCloneDocInBulk) error
	// 启用/禁用doc
	OnOffDocInBulk(context.Context, *ReqOnOffDocInBulk, *RspOnOffDocInBulk) error
	// 根据文档ref_id查询文档
	ListDocByRef(context.Context, *ReqListDocByRef, *RspListDocByRef) error
	// 更新doc标签
	UpdateDocLabels(context.Context, *ReqUpdateCustomChatLabels, *emptypb.Empty) error
	// 转换标签
	ConvertCustomLabel(context.Context, *ReqConvertCustomLabel, *RspConvertCustomLabel) error
	// 获取标签topn值
	GetCustomLabelValueTopN(context.Context, *ReqGetCustomLabelValueTopN, *RspGetCustomLabelValueTopN) error
	// 批量更新doc的特定字段值
	UpdateDocAttrInBulk(context.Context, *ReqUpdateDocAttrInBulk, *RspUpdateDocAttrInBulk) error
	// 查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
	GetTextFileTip(context.Context, *ReqGetTextFileTip, *RspGetTextFileTip) error
	// 创建doc查询
	CreateDocQuery(context.Context, *ReqCreateDocQuery, *RspCreateDocQuery) error
	// 查询QA的知识提示（问题超长，内容重复）等信息
	GetQaTip(context.Context, *ReqGetQaTip, *RspGetQaTip) error
	// 创建全部文档md5
	CreateAllDocMd5(context.Context, *ReqCreateAllDocMd5, *emptypb.Empty) error
	// 创建助手发送方设置
	CreateDocShareConfigSender(context.Context, *ReqCreateDocShareConfigSender, *emptypb.Empty) error
	// 查看创建知识库接收者设置
	ListeDocShareConfigSender(context.Context, *ReqListeDocShareConfigSender, *RspListeDocShareConfigSender) error
	// 查询可分享的助手列表
	ListAssistantCanShareDoc(context.Context, *ReqListAssistantCanShareDoc, *RspListAssistantCanShareDoc) error
	// 查询可分享的团队列表
	ListTeamCanShareDoc(context.Context, *ReqListTeamCanShareDoc, *RspListTeamCanShareDoc) error
	// 查询可分享的用户列表
	ListUserCanShareDoc(context.Context, *ReqListUserCanShareDoc, *RspListUserCanShareDoc) error
	// 查询已分享的团队列表
	ListSharedTeam(context.Context, *ReqListSharedTeam, *RspListSharedTeam) error
	// 查询已分享的用户列表
	ListSharedUser(context.Context, *ReqListSharedUser, *RspListSharedUser) error
	// 查询我设置的助手
	ListMyAssistantIds(context.Context, *ReqListMyAssistantIds, *RspListMyAssistantIds) error
	// 创建助手接收方设置
	CreateDocShareConfigReceiverAssistant(context.Context, *ReqCreateDocShareConfigReceiverAssistant, *emptypb.Empty) error
	// 创建个人/团队接收方设置
	CreateDocShareConfigReceiverUserTeam(context.Context, *ReqCreateDocShareConfigReceiverUserTeam, *emptypb.Empty) error
	// 查询助手接收方设置
	ListDocShareConfigReceiverAssistant(context.Context, *ReqListDocShareConfigReceiverAssistant, *RspListDocShareConfigReceiverAssistant) error
	// 查询个人/团队接收方设置
	ListDocShareConfigReceiverUserTeam(context.Context, *ReqListDocShareConfigReceiverUserTeam, *RspListDocShareConfigReceiverUserTeam) error
	// 创建知识库分享至助手、个人、团队
	CreateDocShare(context.Context, *ReqCreateDocShare, *RspCreateDocShare) error
	// 创建知识库同步or取消至助手（已废弃，请使用CreateDocShare）
	CreateDocShareAssistant(context.Context, *ReqCreateDocShare, *RspCreateDocShare) error
	// 创建企微服务商租户
	CreateOpenWechat(context.Context, *ReqCreateOpenWechat, *emptypb.Empty) error
	// 查询企微服务商租户
	DescribeOpenWechat(context.Context, *ReqDescribeOpenWechat, *RspDescribeOpenWechat) error
	// 腾讯文档授权code处理
	AuthTencentCode(context.Context, *ReqAuthTencentCode, *RspAuthTencentCode) error
	// 查询腾讯文档缓存是否为空
	DescribeTokenIsEmpty(context.Context, *ReqDescribeTokenIsEmpty, *RspDescribeTokenIsEmpty) error
	// 查询腾讯文档外部用户列表
	ListExternalSourceUser(context.Context, *ReqListExternalSourceUser, *RspListExternalSourceUser) error
	// 批量导入绿技行
	CreateGTBText(context.Context, *ReqCreateGTBText, *RspCreateGTBText) error
	// 查询腾讯文档任务状态
	DescribeTencentDocTask(context.Context, *ReqDescribeTencentDocTask, *RspDescribeTencentDocTask) error
	// 删除腾讯文档授权
	DelTencentDocAuth(context.Context, *ReqDelTencentDocAuth, *emptypb.Empty) error
	// 查询绿技行状态
	DescribeGTBText(context.Context, *ReqDescribeGTBText, *RspDescribeGTBText) error
	// 创建腾讯文档AuthURL
	CreateTencentDocAuthUrl(context.Context, *ReqCreateTencentDocAuthUrl, *RspCreateTencentDocAuthUrl) error
	// 查询腾讯文档列表
	DescribeDocList(context.Context, *ReqDescribeTencentDocList, *RspDescribeTencentDocList) error
	// 腾讯文档导入
	ImportTencentDoc(context.Context, *ReqImportTencentDoc, *RspImportTencentDoc) error
	// 重新导入腾讯文档
	ReimportTencentDoc(context.Context, *ReqReimportTencentDoc, *RspReimportTencentDoc) error
	// 导入腾讯文档网页剪存文档
	ImportTencentDocWebClip(context.Context, *ReqImportTencentDocWebClip, *RspImportTencentDocWebClip) error
	// 知识库文档标签修改
	ModifyDocTab(context.Context, *ReqModifyDocTab, *emptypb.Empty) error
	// 查询知识库文档标签
	DescribeDocTab(context.Context, *ReqDescribeDocTab, *RspDescribeDocTab) error
	// 查询我的知识库文档
	DescribeMyDoc(context.Context, *ReqDescribeMyDoc, *RspDescribeMyDoc) error
	// 创建星云任务
	CreateNebulaTask(context.Context, *ReqCreateNebulaTask, *RspCreateNebulaTask) error
	// 查询星云任务
	DescribeNebulaTask(context.Context, *ReqDescribeNebulaTask, *RspDescribeNebulaTask) error
	// 查询星云任务列表
	DescribeNebulaTaskList(context.Context, *ReqDescribeNebulaTaskList, *RspDescribeNebulaTaskList) error
	// 查看投影坐标
	DescribeNebulaProjection(context.Context, *ReqDescribeNebulaProjection, *RspDescribeNebulaProjection) error
	// 查看投影元数据
	DescribeNebulaData(context.Context, *ReqDescribeNebulaData, *RspDescribeNebulaData) error
	// 批量创建星云任务，自动化任务
	BatchCreateNebulaTasks(context.Context, *ReqBatchCreateNebulaTasks, *RspBatchCreateNebulaTasks) error
	// 创建助手已有collection数据初始化处理
	CreateAssistantCollectionInit(context.Context, *ReqCreateAssistantCollectionInit, *RspCreateAssistantCollectionInit) error
	// 创建会话消息
	CreateChatMessage(context.Context, *ReqCreateChatMessage, *RspCreateChatMessage) error
	// 更新会话消息
	UpdateChatMessageThink(context.Context, *ReqUpdateChatMessageThink, *emptypb.Empty) error
	// 更新会话消息
	UpdateChatMessage(context.Context, *ReqUpdateChatMessage, *emptypb.Empty) error
	// 推送消息
	PublishChatMessage(context.Context, *ReqPublishChatMessage, *emptypb.Empty) error
	// 创建AI Agent任务
	CreateChatTaskMessage(context.Context, *ReqCreateChatTaskMessage, *RspCreateChatTaskMessage) error
	// 发送请求AI消息同步
	SendMessageWithoutSaveSync(context.Context, *ReqSendMessageWithoutSaveSync, *RspSendMessageWithoutSaveSync) error
	// 获取消息
	DescribeMessage(context.Context, *ReqDescribeMessage, *RspDescribeMessage) error
	// 获取答案
	DescribeMessageByQuestionId(context.Context, *ReqDescribeMessageByQuestionId, *RspDescribeMessageByQuestionId) error
	// 创建会话
	CreateUserChat(context.Context, *ReqCreateUserChat, *RspCreateUserChat) error
	// 更新chat标签
	UpdateUserChatLabels(context.Context, *ReqUpdateCustomChatLabels, *emptypb.Empty) error
	// 获取会话列表
	DescribeUserChats(context.Context, *ReqDescribeUserChats, *RspDescribeUserChats) error
	// 删除会话
	DeleteUserChat(context.Context, *ReqDeleteUserChat, *emptypb.Empty) error
	// 删除消息
	DeleteChatMessage(context.Context, *ReqDeleteChatMessage, *emptypb.Empty) error
	// 创建系统文档副本
	CreateSystemDocCopy(context.Context, *ReqCreateSystemDocCopy, *RspCreateSystemDocCopy) error
	// 启用系统文档
	EnableSystemDoc(context.Context, *ReqEnableSystemDoc, *emptypb.Empty) error
	// 停用系统文档
	DisableSystemDoc(context.Context, *ReqDisableSystemDoc, *emptypb.Empty) error
	// 删除系统文档
	DeleteSystemDoc(context.Context, *ReqDeleteSystemDoc, *emptypb.Empty) error
	// 评价AI回答
	RateAiAnswer(context.Context, *ReqRateAiAnswer, *emptypb.Empty) error
	// 更新/创建用户反馈
	UpsertUserFeedback(context.Context, *ReqUpsertUserFeedback, *RspUpsertFeedback) error
	// 更新/创建运营反馈
	UpsertOpFeedback(context.Context, *ReqUpsertOpFeedback, *RspUpsertFeedback) error
	// 更新碳LIVE反馈
	UpsertMgmtFeedback(context.Context, *ReqUpsertMgmtFeedback, *RspUpsertFeedback) error
	// 查询用户反馈列表
	GetFeedbacks(context.Context, *ReqGetFeedbacks, *RspGetFeedbacks) error
	// 已读用户反馈
	ReadFeedback(context.Context, *ReqReadFeedback, *emptypb.Empty) error
	// 采用用户反馈
	AcceptFeedback(context.Context, *ReqAcceptFeedback, *emptypb.Empty) error
	// 查询用户反馈操作日志
	GetFeedbackLogs(context.Context, *ReqGetFeedbackLogs, *RspGetFeedbackLogs) error
	// 查询文档列表
	GetDocs(context.Context, *ReqGetDocs, *RspGetDocs) error
	// 获取消息doc
	DescribeMessageDocs(context.Context, *ReqDescribeMessageDocs, *RspDescribeMessageDocs) error
	// 获取Suggest log
	DescribeSuggestLogs(context.Context, *ReqDescribeSuggestLog, *RspDescribeSuggestLog) error
	// 获取消息log
	DescribeMessageLogs(context.Context, *ReqDescribeMessageLog, *RspDescribeMessageLog) error
	// 修复更新collection
	SyncFixChatMessageCollection(context.Context, *ReqSyncFixChatMessageCollection, *emptypb.Empty) error
	// 获取助手的聊天创建者列表
	GetAssistantChatCreators(context.Context, *ReqGetAssistantChatCreators, *RspGetAssistantChatCreators) error
	// 运营端获取AI对话管理列表
	ListChat(context.Context, *ReqListChat, *RspListChat) error
	// 运营端获取AI对话详情
	GetChatDetail(context.Context, *ReqGetChatDetail, *RspGetChatDetail) error
	// 获取AI对话自定义标签
	ListCustomLabel(context.Context, *ReqListCustomLabel, *RspListCustomLabel) error
	// 插入/更新对话标签
	UpsertCustomLabels(context.Context, *ReqUpsertCustomLabels, *RspUpsertCustomLabels) error
	// 删除对话标签
	DeleteCustomLabels(context.Context, *ReqDeleteCustomLabels, *emptypb.Empty) error
	// 查询ai助手列表
	ListAssistant(context.Context, *ReqListAssistant, *RspListAssistant) error
	// 获取ai助手详情
	GetAssistant(context.Context, *ReqGetAssistant, *RspGetAssistant) error
	// 获取小助手信息map
	GetAssistantInfoMap(context.Context, *ReqGetAssistantInfoMap, *RspGetAssistantInfoMap) error
	// 获取人工坐席列表
	ListChatLiveAgent(context.Context, *ReqListChatLiveAgent, *RspListChatLiveAgent) error
	// 切换人工坐席
	SwitchChatLiveAgent(context.Context, *ReqSwitchChatLiveAgent, *emptypb.Empty) error
	// 人工坐席状态变更
	LiveAgentStatusChange(context.Context, *ReqLiveAgentStatusChange, *emptypb.Empty) error
	// 发送ai公众号用到的rpc接口
	// 微信-评价微信公众号ai回答
	RateAiAnswerWechat(context.Context, *ReqRateAiAnswerWechat, *emptypb.Empty) error
	// 微信-获取问题答案（重复的问题则会忽略）
	GetAnswerWechat(context.Context, *ReqGetAnswerWechat, *RspGetAnswerWechat) error
	// 微信-获取图片提问的答案
	GetAnswerWechatForFile(context.Context, *ReqGetAnswerWechat, *RspGetAnswerWechat) error
	// 微信-获取继续回答的答案
	GetAnswerWechatForContinue(context.Context, *ReqGetAnswerWechat, *RspGetAnswerWechat) error
	// 微信-结束微信会话
	FinishChatWechat(context.Context, *ReqFinishChatWechat, *emptypb.Empty) error
	// 微信-获取微信访问白名单
	GetWhiteListWechat(context.Context, *ReqGetWhiteListWechat, *RspGetWhiteListWechat) error
	// 微信-更新微信问答message状态及答案记录信息
	UpdateChatMessageStateWechat(context.Context, *ReqUpdateChatMessageStateWechat, *emptypb.Empty) error
	// 微信-获取聊天记录游标
	GetMessageCursorWechat(context.Context, *ReqGetMessageCursorWechat, *RspGetMessageCursorWechat) error
	// 微信-获取用户会话信息
	GetChatWechat(context.Context, *ReqGetChatWechat, *RspGetChatWechat) error
	// 微信-创建用户会话
	CreateChatWechat(context.Context, *ReqCreateChatWechat, *RspCreateChatWechat) error
	// 微信-获取会话上一个问题的状态（是否已回复）
	GetLastQuestionStateWechat(context.Context, *ReqGetLastQuestionStateWechat, *RspGetLastQuestionStateWechat) error
	// 微信-更新发送的默认消息记录
	UpdateChatMessageRecordWechat(context.Context, *ReqUpdateChatMessageRecordWechat, *RspUpdateChatMessageRecordWechat) error
	// 创建发送记录
	CreateSendRecord(context.Context, *ReqCreateSendRecord, *RspCreateSendRecord) error
	// 更新发送记录
	UpdateSendRecord(context.Context, *ReqUpdateSendRecord, *emptypb.Empty) error
	// 获取助手的聊天记录(主要用于从t_chat_message 到 t_chat_send_record 的数据迁移)
	DescribeAssistantMessage(context.Context, *ReqDescribeAssistantMessage, *RspDescribeAssistantMessage) error
	// 插入助手的聊天记录(主要用于从t_chat_message 到 t_chat_send_record 的数据迁移)
	InsertAssistantMessageRecord(context.Context, *ReqInsertAssistantMessageRecord, *emptypb.Empty) error
	// 迁移用户会话记录信息
	MigrationChatMessageInfo(context.Context, *ReqMigrationChatMessageInfo, *emptypb.Empty) error
	// 获取有会话的地区编码
	DescribeChatRegionCode(context.Context, *ReqDescribeChatRegionCode, *RspDescribeChatRegionCode) error
	// 获取有教学反馈的地区编码
	DescribeFeedbackRegionCode(context.Context, *ReqDescribeFeedbackRegionCode, *RspDescribeFeedbackRegionCode) error
	// 查询用户会话记录
	DescribeUserChatRecords(context.Context, *ReqDescribeUserChatRecords, *RspDescribeUserChatRecords) error
	// 获取chat log权限
	DescribeChatLogAuthItem(context.Context, *ReqDescribeChatLogAuthItem, *RspDescribeChatLogAuthItem) error
	// 获取网页title
	FetchHtmlTitles(context.Context, *ReqFetchHtmlTitles, *RspFetchHtmlTitles) error
	// 查询助手
	GetAssistants(context.Context, *ReqGetAssistants, *RspGetAssistants) error
	// 批量创建助手
	BatchCreateAssistant(context.Context, *ReqBatchCreateAssistant, *RspBatchCreateAssistant) error
	// 批量更新助手
	BatchUpdateAssistant(context.Context, *ReqBatchUpdateAssistant, *emptypb.Empty) error
	// 删除助手
	DeleteAssistant(context.Context, *ReqDeleteAssistant, *emptypb.Empty) error
	// 查询助手日志
	GetAssistantLogs(context.Context, *ReqGetAssistantLogs, *RspGetAssistantLogs) error
	// 获取助手下拉选项
	GetAssistantOptions(context.Context, *emptypb.Empty, *RspGetAssistantOptions) error
	// 查询文档分段信息
	GetDocChunks(context.Context, *ReqGetDocChunks, *RspGetDocChunks) error
	// 文档分段
	ChunkDoc(context.Context, *ReqChunkDoc, *RspChunkDoc) error
	// 查询文档分段任务列表
	GetChunkDocTasks(context.Context, *ReqGetChunkDocTasks, *RspGetChunkDocTasks) error
	// 查询文档的向量化模型
	GetDocEmbeddingModels(context.Context, *ReqGetDocEmbeddingModels, *RspGetDocEmbeddingModels) error
	// 检查助手白名单
	CheckAssistantAllowlist(context.Context, *ReqCheckAssistantAllowlist, *RspCheckAssistantAllowlist) error
	// 获取最近使用过的助手Id
	GetRecentlyUsedAssistantIds(context.Context, *ReqGetRecentlyUsedAssistantIds, *RspGetRecentlyUsedAssistantIds) error
	// 获取最近使用过的助手
	GetRecentlyUsedAssistants(context.Context, *ReqGetRecentlyUsedAssistants, *RspGetRecentlyUsedAssistants) error
	// 获取所有助手的管理员信息
	GetAssistantAdmin(context.Context, *ReqGetAssistantAdmin, *RspGetAssistantAdmin) error
	// GetAssistantChatUser 获取助手会话的用户
	GetAssistantChatUser(context.Context, *ReqGetAssistantChatUser, *RspGetAssistantChatUser) error
	// 创建聊天分享
	CreateChatShare(context.Context, *ReqCreateChatShare, *RspCreateChatShare) error
	// 从分享继续聊天
	ContinueChatFromShare(context.Context, *ReqContinueChatFromShare, *RspContinueChatFromShare) error
	// 获取用户分享列表
	ListChatShares(context.Context, *ReqListChatShares, *RspListChatShares) error
	// 更新分享状态
	UpdateChatShareStatus(context.Context, *ReqUpdateChatShareStatus, *emptypb.Empty) error
	// 获取分享访问记录
	ListChatShareAccesses(context.Context, *ReqListChatShareAccesses, *RspListChatShareAccesses) error
	// 获取分享详情
	GetChatShareMessages(context.Context, *ReqGetChatShareMessages, *RspGetChatShareMessages) error
	// 查询share记录
	GetChatShareRecord(context.Context, *ReqGetChatShareRecord, *RspGetChatShareRecord) error
	// 记录分享访问
	RecordChatShareAccess(context.Context, *ReqRecordChatShareAccess, *RspRecordChatShareAccess) error
}

func RegisterAiServiceHandler(s server.Server, hdlr AiServiceHandler, opts ...server.HandlerOption) error {
	type aiService interface {
		UpdateMessageDocs(ctx context.Context, in *ReqUpdateMessageDocs, out *emptypb.Empty) error
		DescribeMessageDocSnapshot(ctx context.Context, in *ReqDescribeMessageDocSnapshot, out *RspDescribeMessageDocSnapshot) error
		StopAnswerReply(ctx context.Context, in *ReqStopAnswerReply, out *RspStopAnswerReply) error
		StopQuestionReply(ctx context.Context, in *ReqStopQuestionReply, out *RspStopQuestionReply) error
		DescribePreMessageTask(ctx context.Context, in *ReqDescribePreMessageTask, out *RspReqDescribePreMessageTask) error
		DescribeChatMessageFileState(ctx context.Context, in *ReqDescribeChatMessageFileState, out *RspDescribeChatMessageFileState) error
		DescribeChatAgentTaskByPreTask(ctx context.Context, in *ReqDescribeChatAgentTaskByPreTask, out *RspDescribeChatAgentTaskByPreTask) error
		UpdateChatMessageCollections(ctx context.Context, in *ReqUpdateChatMessageCollections, out *emptypb.Empty) error
		FixSearchCollectionItems(ctx context.Context, in *ReqFixSearchCollectionItems, out *RspFixSearchCollectionItems) error
		DescribeChatAgentTask(ctx context.Context, in *ReqDescribeChatAgentTask, out *RspDescribeChatAgentTask) error
		CheckChatPermission(ctx context.Context, in *ReqCheckChatPermission, out *emptypb.Empty) error
		DescribeChatQuestionAnswersByPage(ctx context.Context, in *ReqDescribeChatQuestionAnswersByPage, out *RspDescribeChatQuestionAnswersByPage) error
		CreateChatOperation(ctx context.Context, in *ReqCreateChatOperation, out *emptypb.Empty) error
		ParseChatDoc(ctx context.Context, in *ReqParseChatDoc, out *RspParseChatDoc) error
		UpdateMessageText(ctx context.Context, in *ReqUpdateMessageText, out *emptypb.Empty) error
		CreateQaMatchMessage(ctx context.Context, in *ReqCreateQaMatchMessage, out *RspCreateQaMatchMessage) error
		DescribeMessageMatchQa(ctx context.Context, in *ReqDescribeMessageMatchQa, out *RspDescribeMessageMatchQa) error
		ResendMessageSync(ctx context.Context, in *ReqResendMessageSync, out *RspResendMessageSync) error
		DescribeExportChatMessages(ctx context.Context, in *ReqDescribeExportChatMessages, out *RspDescribeExportChatMessages) error
		CreateMessageSuggestQuestion(ctx context.Context, in *ReqCreateMessageSuggestQuestion, out *RspCreateMessageSuggestQuestion) error
		DescribeExportTasks(ctx context.Context, in *ReqDescribeExportTasks, out *RspDescribeExportTasks) error
		CreateExportTask(ctx context.Context, in *ReqCreateExportTask, out *RspCreateExportTask) error
		UpdateExportTask(ctx context.Context, in *ReqUpdateExportTask, out *emptypb.Empty) error
		ListQA(ctx context.Context, in *ReqListQA, out *RspListQA) error
		CreateQAInBulk(ctx context.Context, in *ReqCreateQAInBulk, out *RspCreateQAInBulk) error
		CreateQA(ctx context.Context, in *ReqCreateQA, out *RspCreateQA) error
		UpdateQA(ctx context.Context, in *ReqUpdateQA, out *emptypb.Empty) error
		UpdateQAInBulk(ctx context.Context, in *ReqUpdateQAInBulk, out *emptypb.Empty) error
		ListTextFile(ctx context.Context, in *ReqListTextFile, out *RspListTextFile) error
		CreateTextFileInBulk(ctx context.Context, in *ReqCreateTextFileInBulk, out *RspCreateTextFileInBulk) error
		UpdateTextFile(ctx context.Context, in *ReqUpdateTextFile, out *emptypb.Empty) error
		UpdateTextFileInBulk(ctx context.Context, in *ReqUpdateTextFileInBulk, out *emptypb.Empty) error
		DeleteDocInBulk(ctx context.Context, in *ReqDeleteDocInBulk, out *RspDeleteDocInBulk) error
		SearchCollectionOneShot(ctx context.Context, in *ReqSearchCollectionOneShot, out *RspSearchCollectionOneShot) error
		ReparseTextFiles(ctx context.Context, in *ReqReparseTextFiles, out *RspReparseTextFiles) error
		SearchCollection(ctx context.Context, in *ReqSearchCollection, out *RspSearchCollection) error
		ListCollection(ctx context.Context, in *ReqListCollection, out *RspListCollection) error
		ListContributor(ctx context.Context, in *ReqListContributor, out *RspListContributor) error
		ListUpdateBy(ctx context.Context, in *ReqListUpdateBy, out *RspListUpdateBy) error
		ListCreateBy(ctx context.Context, in *ReqListUpdateBy, out *RspListUpdateBy) error
		ListSharedAssistant(ctx context.Context, in *ReqListSharedAssistant, out *RspListSharedAssistant) error
		ValidateQAInBulk(ctx context.Context, in *ReqValidateQAInBulk, out *RspValidateQAInBulk) error
		ValidateTextFileInBulk(ctx context.Context, in *ReqValidateTextFileInBulk, out *RspValidateTextFileInBulk) error
		CloneDocInBulk(ctx context.Context, in *ReqCloneDocInBulk, out *RspCloneDocInBulk) error
		OnOffDocInBulk(ctx context.Context, in *ReqOnOffDocInBulk, out *RspOnOffDocInBulk) error
		ListDocByRef(ctx context.Context, in *ReqListDocByRef, out *RspListDocByRef) error
		UpdateDocLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, out *emptypb.Empty) error
		ConvertCustomLabel(ctx context.Context, in *ReqConvertCustomLabel, out *RspConvertCustomLabel) error
		GetCustomLabelValueTopN(ctx context.Context, in *ReqGetCustomLabelValueTopN, out *RspGetCustomLabelValueTopN) error
		UpdateDocAttrInBulk(ctx context.Context, in *ReqUpdateDocAttrInBulk, out *RspUpdateDocAttrInBulk) error
		GetTextFileTip(ctx context.Context, in *ReqGetTextFileTip, out *RspGetTextFileTip) error
		CreateDocQuery(ctx context.Context, in *ReqCreateDocQuery, out *RspCreateDocQuery) error
		GetQaTip(ctx context.Context, in *ReqGetQaTip, out *RspGetQaTip) error
		CreateAllDocMd5(ctx context.Context, in *ReqCreateAllDocMd5, out *emptypb.Empty) error
		CreateDocShareConfigSender(ctx context.Context, in *ReqCreateDocShareConfigSender, out *emptypb.Empty) error
		ListeDocShareConfigSender(ctx context.Context, in *ReqListeDocShareConfigSender, out *RspListeDocShareConfigSender) error
		ListAssistantCanShareDoc(ctx context.Context, in *ReqListAssistantCanShareDoc, out *RspListAssistantCanShareDoc) error
		ListTeamCanShareDoc(ctx context.Context, in *ReqListTeamCanShareDoc, out *RspListTeamCanShareDoc) error
		ListUserCanShareDoc(ctx context.Context, in *ReqListUserCanShareDoc, out *RspListUserCanShareDoc) error
		ListSharedTeam(ctx context.Context, in *ReqListSharedTeam, out *RspListSharedTeam) error
		ListSharedUser(ctx context.Context, in *ReqListSharedUser, out *RspListSharedUser) error
		ListMyAssistantIds(ctx context.Context, in *ReqListMyAssistantIds, out *RspListMyAssistantIds) error
		CreateDocShareConfigReceiverAssistant(ctx context.Context, in *ReqCreateDocShareConfigReceiverAssistant, out *emptypb.Empty) error
		CreateDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqCreateDocShareConfigReceiverUserTeam, out *emptypb.Empty) error
		ListDocShareConfigReceiverAssistant(ctx context.Context, in *ReqListDocShareConfigReceiverAssistant, out *RspListDocShareConfigReceiverAssistant) error
		ListDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqListDocShareConfigReceiverUserTeam, out *RspListDocShareConfigReceiverUserTeam) error
		CreateDocShare(ctx context.Context, in *ReqCreateDocShare, out *RspCreateDocShare) error
		CreateDocShareAssistant(ctx context.Context, in *ReqCreateDocShare, out *RspCreateDocShare) error
		CreateOpenWechat(ctx context.Context, in *ReqCreateOpenWechat, out *emptypb.Empty) error
		DescribeOpenWechat(ctx context.Context, in *ReqDescribeOpenWechat, out *RspDescribeOpenWechat) error
		AuthTencentCode(ctx context.Context, in *ReqAuthTencentCode, out *RspAuthTencentCode) error
		DescribeTokenIsEmpty(ctx context.Context, in *ReqDescribeTokenIsEmpty, out *RspDescribeTokenIsEmpty) error
		ListExternalSourceUser(ctx context.Context, in *ReqListExternalSourceUser, out *RspListExternalSourceUser) error
		CreateGTBText(ctx context.Context, in *ReqCreateGTBText, out *RspCreateGTBText) error
		DescribeTencentDocTask(ctx context.Context, in *ReqDescribeTencentDocTask, out *RspDescribeTencentDocTask) error
		DelTencentDocAuth(ctx context.Context, in *ReqDelTencentDocAuth, out *emptypb.Empty) error
		DescribeGTBText(ctx context.Context, in *ReqDescribeGTBText, out *RspDescribeGTBText) error
		CreateTencentDocAuthUrl(ctx context.Context, in *ReqCreateTencentDocAuthUrl, out *RspCreateTencentDocAuthUrl) error
		DescribeDocList(ctx context.Context, in *ReqDescribeTencentDocList, out *RspDescribeTencentDocList) error
		ImportTencentDoc(ctx context.Context, in *ReqImportTencentDoc, out *RspImportTencentDoc) error
		ReimportTencentDoc(ctx context.Context, in *ReqReimportTencentDoc, out *RspReimportTencentDoc) error
		ImportTencentDocWebClip(ctx context.Context, in *ReqImportTencentDocWebClip, out *RspImportTencentDocWebClip) error
		ModifyDocTab(ctx context.Context, in *ReqModifyDocTab, out *emptypb.Empty) error
		DescribeDocTab(ctx context.Context, in *ReqDescribeDocTab, out *RspDescribeDocTab) error
		DescribeMyDoc(ctx context.Context, in *ReqDescribeMyDoc, out *RspDescribeMyDoc) error
		CreateNebulaTask(ctx context.Context, in *ReqCreateNebulaTask, out *RspCreateNebulaTask) error
		DescribeNebulaTask(ctx context.Context, in *ReqDescribeNebulaTask, out *RspDescribeNebulaTask) error
		DescribeNebulaTaskList(ctx context.Context, in *ReqDescribeNebulaTaskList, out *RspDescribeNebulaTaskList) error
		DescribeNebulaProjection(ctx context.Context, in *ReqDescribeNebulaProjection, out *RspDescribeNebulaProjection) error
		DescribeNebulaData(ctx context.Context, in *ReqDescribeNebulaData, out *RspDescribeNebulaData) error
		BatchCreateNebulaTasks(ctx context.Context, in *ReqBatchCreateNebulaTasks, out *RspBatchCreateNebulaTasks) error
		CreateAssistantCollectionInit(ctx context.Context, in *ReqCreateAssistantCollectionInit, out *RspCreateAssistantCollectionInit) error
		CreateChatMessage(ctx context.Context, in *ReqCreateChatMessage, out *RspCreateChatMessage) error
		UpdateChatMessageThink(ctx context.Context, in *ReqUpdateChatMessageThink, out *emptypb.Empty) error
		UpdateChatMessage(ctx context.Context, in *ReqUpdateChatMessage, out *emptypb.Empty) error
		PublishChatMessage(ctx context.Context, in *ReqPublishChatMessage, out *emptypb.Empty) error
		CreateChatTaskMessage(ctx context.Context, in *ReqCreateChatTaskMessage, out *RspCreateChatTaskMessage) error
		SendMessageWithoutSaveSync(ctx context.Context, in *ReqSendMessageWithoutSaveSync, out *RspSendMessageWithoutSaveSync) error
		DescribeMessage(ctx context.Context, in *ReqDescribeMessage, out *RspDescribeMessage) error
		DescribeMessageByQuestionId(ctx context.Context, in *ReqDescribeMessageByQuestionId, out *RspDescribeMessageByQuestionId) error
		CreateUserChat(ctx context.Context, in *ReqCreateUserChat, out *RspCreateUserChat) error
		UpdateUserChatLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, out *emptypb.Empty) error
		DescribeUserChats(ctx context.Context, in *ReqDescribeUserChats, out *RspDescribeUserChats) error
		DeleteUserChat(ctx context.Context, in *ReqDeleteUserChat, out *emptypb.Empty) error
		DeleteChatMessage(ctx context.Context, in *ReqDeleteChatMessage, out *emptypb.Empty) error
		CreateSystemDocCopy(ctx context.Context, in *ReqCreateSystemDocCopy, out *RspCreateSystemDocCopy) error
		EnableSystemDoc(ctx context.Context, in *ReqEnableSystemDoc, out *emptypb.Empty) error
		DisableSystemDoc(ctx context.Context, in *ReqDisableSystemDoc, out *emptypb.Empty) error
		DeleteSystemDoc(ctx context.Context, in *ReqDeleteSystemDoc, out *emptypb.Empty) error
		RateAiAnswer(ctx context.Context, in *ReqRateAiAnswer, out *emptypb.Empty) error
		UpsertUserFeedback(ctx context.Context, in *ReqUpsertUserFeedback, out *RspUpsertFeedback) error
		UpsertOpFeedback(ctx context.Context, in *ReqUpsertOpFeedback, out *RspUpsertFeedback) error
		UpsertMgmtFeedback(ctx context.Context, in *ReqUpsertMgmtFeedback, out *RspUpsertFeedback) error
		GetFeedbacks(ctx context.Context, in *ReqGetFeedbacks, out *RspGetFeedbacks) error
		ReadFeedback(ctx context.Context, in *ReqReadFeedback, out *emptypb.Empty) error
		AcceptFeedback(ctx context.Context, in *ReqAcceptFeedback, out *emptypb.Empty) error
		GetFeedbackLogs(ctx context.Context, in *ReqGetFeedbackLogs, out *RspGetFeedbackLogs) error
		GetDocs(ctx context.Context, in *ReqGetDocs, out *RspGetDocs) error
		DescribeMessageDocs(ctx context.Context, in *ReqDescribeMessageDocs, out *RspDescribeMessageDocs) error
		DescribeSuggestLogs(ctx context.Context, in *ReqDescribeSuggestLog, out *RspDescribeSuggestLog) error
		DescribeMessageLogs(ctx context.Context, in *ReqDescribeMessageLog, out *RspDescribeMessageLog) error
		SyncFixChatMessageCollection(ctx context.Context, in *ReqSyncFixChatMessageCollection, out *emptypb.Empty) error
		GetAssistantChatCreators(ctx context.Context, in *ReqGetAssistantChatCreators, out *RspGetAssistantChatCreators) error
		ListChat(ctx context.Context, in *ReqListChat, out *RspListChat) error
		GetChatDetail(ctx context.Context, in *ReqGetChatDetail, out *RspGetChatDetail) error
		ListCustomLabel(ctx context.Context, in *ReqListCustomLabel, out *RspListCustomLabel) error
		UpsertCustomLabels(ctx context.Context, in *ReqUpsertCustomLabels, out *RspUpsertCustomLabels) error
		DeleteCustomLabels(ctx context.Context, in *ReqDeleteCustomLabels, out *emptypb.Empty) error
		ListAssistant(ctx context.Context, in *ReqListAssistant, out *RspListAssistant) error
		GetAssistant(ctx context.Context, in *ReqGetAssistant, out *RspGetAssistant) error
		GetAssistantInfoMap(ctx context.Context, in *ReqGetAssistantInfoMap, out *RspGetAssistantInfoMap) error
		ListChatLiveAgent(ctx context.Context, in *ReqListChatLiveAgent, out *RspListChatLiveAgent) error
		SwitchChatLiveAgent(ctx context.Context, in *ReqSwitchChatLiveAgent, out *emptypb.Empty) error
		LiveAgentStatusChange(ctx context.Context, in *ReqLiveAgentStatusChange, out *emptypb.Empty) error
		RateAiAnswerWechat(ctx context.Context, in *ReqRateAiAnswerWechat, out *emptypb.Empty) error
		GetAnswerWechat(ctx context.Context, in *ReqGetAnswerWechat, out *RspGetAnswerWechat) error
		GetAnswerWechatForFile(ctx context.Context, in *ReqGetAnswerWechat, out *RspGetAnswerWechat) error
		GetAnswerWechatForContinue(ctx context.Context, in *ReqGetAnswerWechat, out *RspGetAnswerWechat) error
		FinishChatWechat(ctx context.Context, in *ReqFinishChatWechat, out *emptypb.Empty) error
		GetWhiteListWechat(ctx context.Context, in *ReqGetWhiteListWechat, out *RspGetWhiteListWechat) error
		UpdateChatMessageStateWechat(ctx context.Context, in *ReqUpdateChatMessageStateWechat, out *emptypb.Empty) error
		GetMessageCursorWechat(ctx context.Context, in *ReqGetMessageCursorWechat, out *RspGetMessageCursorWechat) error
		GetChatWechat(ctx context.Context, in *ReqGetChatWechat, out *RspGetChatWechat) error
		CreateChatWechat(ctx context.Context, in *ReqCreateChatWechat, out *RspCreateChatWechat) error
		GetLastQuestionStateWechat(ctx context.Context, in *ReqGetLastQuestionStateWechat, out *RspGetLastQuestionStateWechat) error
		UpdateChatMessageRecordWechat(ctx context.Context, in *ReqUpdateChatMessageRecordWechat, out *RspUpdateChatMessageRecordWechat) error
		CreateSendRecord(ctx context.Context, in *ReqCreateSendRecord, out *RspCreateSendRecord) error
		UpdateSendRecord(ctx context.Context, in *ReqUpdateSendRecord, out *emptypb.Empty) error
		DescribeAssistantMessage(ctx context.Context, in *ReqDescribeAssistantMessage, out *RspDescribeAssistantMessage) error
		InsertAssistantMessageRecord(ctx context.Context, in *ReqInsertAssistantMessageRecord, out *emptypb.Empty) error
		MigrationChatMessageInfo(ctx context.Context, in *ReqMigrationChatMessageInfo, out *emptypb.Empty) error
		DescribeChatRegionCode(ctx context.Context, in *ReqDescribeChatRegionCode, out *RspDescribeChatRegionCode) error
		DescribeFeedbackRegionCode(ctx context.Context, in *ReqDescribeFeedbackRegionCode, out *RspDescribeFeedbackRegionCode) error
		DescribeUserChatRecords(ctx context.Context, in *ReqDescribeUserChatRecords, out *RspDescribeUserChatRecords) error
		DescribeChatLogAuthItem(ctx context.Context, in *ReqDescribeChatLogAuthItem, out *RspDescribeChatLogAuthItem) error
		FetchHtmlTitles(ctx context.Context, in *ReqFetchHtmlTitles, out *RspFetchHtmlTitles) error
		GetAssistants(ctx context.Context, in *ReqGetAssistants, out *RspGetAssistants) error
		BatchCreateAssistant(ctx context.Context, in *ReqBatchCreateAssistant, out *RspBatchCreateAssistant) error
		BatchUpdateAssistant(ctx context.Context, in *ReqBatchUpdateAssistant, out *emptypb.Empty) error
		DeleteAssistant(ctx context.Context, in *ReqDeleteAssistant, out *emptypb.Empty) error
		GetAssistantLogs(ctx context.Context, in *ReqGetAssistantLogs, out *RspGetAssistantLogs) error
		GetAssistantOptions(ctx context.Context, in *emptypb.Empty, out *RspGetAssistantOptions) error
		GetDocChunks(ctx context.Context, in *ReqGetDocChunks, out *RspGetDocChunks) error
		ChunkDoc(ctx context.Context, in *ReqChunkDoc, out *RspChunkDoc) error
		GetChunkDocTasks(ctx context.Context, in *ReqGetChunkDocTasks, out *RspGetChunkDocTasks) error
		GetDocEmbeddingModels(ctx context.Context, in *ReqGetDocEmbeddingModels, out *RspGetDocEmbeddingModels) error
		CheckAssistantAllowlist(ctx context.Context, in *ReqCheckAssistantAllowlist, out *RspCheckAssistantAllowlist) error
		GetRecentlyUsedAssistantIds(ctx context.Context, in *ReqGetRecentlyUsedAssistantIds, out *RspGetRecentlyUsedAssistantIds) error
		GetRecentlyUsedAssistants(ctx context.Context, in *ReqGetRecentlyUsedAssistants, out *RspGetRecentlyUsedAssistants) error
		GetAssistantAdmin(ctx context.Context, in *ReqGetAssistantAdmin, out *RspGetAssistantAdmin) error
		GetAssistantChatUser(ctx context.Context, in *ReqGetAssistantChatUser, out *RspGetAssistantChatUser) error
		CreateChatShare(ctx context.Context, in *ReqCreateChatShare, out *RspCreateChatShare) error
		ContinueChatFromShare(ctx context.Context, in *ReqContinueChatFromShare, out *RspContinueChatFromShare) error
		ListChatShares(ctx context.Context, in *ReqListChatShares, out *RspListChatShares) error
		UpdateChatShareStatus(ctx context.Context, in *ReqUpdateChatShareStatus, out *emptypb.Empty) error
		ListChatShareAccesses(ctx context.Context, in *ReqListChatShareAccesses, out *RspListChatShareAccesses) error
		GetChatShareMessages(ctx context.Context, in *ReqGetChatShareMessages, out *RspGetChatShareMessages) error
		GetChatShareRecord(ctx context.Context, in *ReqGetChatShareRecord, out *RspGetChatShareRecord) error
		RecordChatShareAccess(ctx context.Context, in *ReqRecordChatShareAccess, out *RspRecordChatShareAccess) error
	}
	type AiService struct {
		aiService
	}
	h := &aiServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&AiService{h}, opts...))
}

type aiServiceHandler struct {
	AiServiceHandler
}

func (h *aiServiceHandler) UpdateMessageDocs(ctx context.Context, in *ReqUpdateMessageDocs, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateMessageDocs(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMessageDocSnapshot(ctx context.Context, in *ReqDescribeMessageDocSnapshot, out *RspDescribeMessageDocSnapshot) error {
	return h.AiServiceHandler.DescribeMessageDocSnapshot(ctx, in, out)
}

func (h *aiServiceHandler) StopAnswerReply(ctx context.Context, in *ReqStopAnswerReply, out *RspStopAnswerReply) error {
	return h.AiServiceHandler.StopAnswerReply(ctx, in, out)
}

func (h *aiServiceHandler) StopQuestionReply(ctx context.Context, in *ReqStopQuestionReply, out *RspStopQuestionReply) error {
	return h.AiServiceHandler.StopQuestionReply(ctx, in, out)
}

func (h *aiServiceHandler) DescribePreMessageTask(ctx context.Context, in *ReqDescribePreMessageTask, out *RspReqDescribePreMessageTask) error {
	return h.AiServiceHandler.DescribePreMessageTask(ctx, in, out)
}

func (h *aiServiceHandler) DescribeChatMessageFileState(ctx context.Context, in *ReqDescribeChatMessageFileState, out *RspDescribeChatMessageFileState) error {
	return h.AiServiceHandler.DescribeChatMessageFileState(ctx, in, out)
}

func (h *aiServiceHandler) DescribeChatAgentTaskByPreTask(ctx context.Context, in *ReqDescribeChatAgentTaskByPreTask, out *RspDescribeChatAgentTaskByPreTask) error {
	return h.AiServiceHandler.DescribeChatAgentTaskByPreTask(ctx, in, out)
}

func (h *aiServiceHandler) UpdateChatMessageCollections(ctx context.Context, in *ReqUpdateChatMessageCollections, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateChatMessageCollections(ctx, in, out)
}

func (h *aiServiceHandler) FixSearchCollectionItems(ctx context.Context, in *ReqFixSearchCollectionItems, out *RspFixSearchCollectionItems) error {
	return h.AiServiceHandler.FixSearchCollectionItems(ctx, in, out)
}

func (h *aiServiceHandler) DescribeChatAgentTask(ctx context.Context, in *ReqDescribeChatAgentTask, out *RspDescribeChatAgentTask) error {
	return h.AiServiceHandler.DescribeChatAgentTask(ctx, in, out)
}

func (h *aiServiceHandler) CheckChatPermission(ctx context.Context, in *ReqCheckChatPermission, out *emptypb.Empty) error {
	return h.AiServiceHandler.CheckChatPermission(ctx, in, out)
}

func (h *aiServiceHandler) DescribeChatQuestionAnswersByPage(ctx context.Context, in *ReqDescribeChatQuestionAnswersByPage, out *RspDescribeChatQuestionAnswersByPage) error {
	return h.AiServiceHandler.DescribeChatQuestionAnswersByPage(ctx, in, out)
}

func (h *aiServiceHandler) CreateChatOperation(ctx context.Context, in *ReqCreateChatOperation, out *emptypb.Empty) error {
	return h.AiServiceHandler.CreateChatOperation(ctx, in, out)
}

func (h *aiServiceHandler) ParseChatDoc(ctx context.Context, in *ReqParseChatDoc, out *RspParseChatDoc) error {
	return h.AiServiceHandler.ParseChatDoc(ctx, in, out)
}

func (h *aiServiceHandler) UpdateMessageText(ctx context.Context, in *ReqUpdateMessageText, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateMessageText(ctx, in, out)
}

func (h *aiServiceHandler) CreateQaMatchMessage(ctx context.Context, in *ReqCreateQaMatchMessage, out *RspCreateQaMatchMessage) error {
	return h.AiServiceHandler.CreateQaMatchMessage(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMessageMatchQa(ctx context.Context, in *ReqDescribeMessageMatchQa, out *RspDescribeMessageMatchQa) error {
	return h.AiServiceHandler.DescribeMessageMatchQa(ctx, in, out)
}

func (h *aiServiceHandler) ResendMessageSync(ctx context.Context, in *ReqResendMessageSync, out *RspResendMessageSync) error {
	return h.AiServiceHandler.ResendMessageSync(ctx, in, out)
}

func (h *aiServiceHandler) DescribeExportChatMessages(ctx context.Context, in *ReqDescribeExportChatMessages, out *RspDescribeExportChatMessages) error {
	return h.AiServiceHandler.DescribeExportChatMessages(ctx, in, out)
}

func (h *aiServiceHandler) CreateMessageSuggestQuestion(ctx context.Context, in *ReqCreateMessageSuggestQuestion, out *RspCreateMessageSuggestQuestion) error {
	return h.AiServiceHandler.CreateMessageSuggestQuestion(ctx, in, out)
}

func (h *aiServiceHandler) DescribeExportTasks(ctx context.Context, in *ReqDescribeExportTasks, out *RspDescribeExportTasks) error {
	return h.AiServiceHandler.DescribeExportTasks(ctx, in, out)
}

func (h *aiServiceHandler) CreateExportTask(ctx context.Context, in *ReqCreateExportTask, out *RspCreateExportTask) error {
	return h.AiServiceHandler.CreateExportTask(ctx, in, out)
}

func (h *aiServiceHandler) UpdateExportTask(ctx context.Context, in *ReqUpdateExportTask, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateExportTask(ctx, in, out)
}

func (h *aiServiceHandler) ListQA(ctx context.Context, in *ReqListQA, out *RspListQA) error {
	return h.AiServiceHandler.ListQA(ctx, in, out)
}

func (h *aiServiceHandler) CreateQAInBulk(ctx context.Context, in *ReqCreateQAInBulk, out *RspCreateQAInBulk) error {
	return h.AiServiceHandler.CreateQAInBulk(ctx, in, out)
}

func (h *aiServiceHandler) CreateQA(ctx context.Context, in *ReqCreateQA, out *RspCreateQA) error {
	return h.AiServiceHandler.CreateQA(ctx, in, out)
}

func (h *aiServiceHandler) UpdateQA(ctx context.Context, in *ReqUpdateQA, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateQA(ctx, in, out)
}

func (h *aiServiceHandler) UpdateQAInBulk(ctx context.Context, in *ReqUpdateQAInBulk, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateQAInBulk(ctx, in, out)
}

func (h *aiServiceHandler) ListTextFile(ctx context.Context, in *ReqListTextFile, out *RspListTextFile) error {
	return h.AiServiceHandler.ListTextFile(ctx, in, out)
}

func (h *aiServiceHandler) CreateTextFileInBulk(ctx context.Context, in *ReqCreateTextFileInBulk, out *RspCreateTextFileInBulk) error {
	return h.AiServiceHandler.CreateTextFileInBulk(ctx, in, out)
}

func (h *aiServiceHandler) UpdateTextFile(ctx context.Context, in *ReqUpdateTextFile, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateTextFile(ctx, in, out)
}

func (h *aiServiceHandler) UpdateTextFileInBulk(ctx context.Context, in *ReqUpdateTextFileInBulk, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateTextFileInBulk(ctx, in, out)
}

func (h *aiServiceHandler) DeleteDocInBulk(ctx context.Context, in *ReqDeleteDocInBulk, out *RspDeleteDocInBulk) error {
	return h.AiServiceHandler.DeleteDocInBulk(ctx, in, out)
}

func (h *aiServiceHandler) SearchCollectionOneShot(ctx context.Context, in *ReqSearchCollectionOneShot, out *RspSearchCollectionOneShot) error {
	return h.AiServiceHandler.SearchCollectionOneShot(ctx, in, out)
}

func (h *aiServiceHandler) ReparseTextFiles(ctx context.Context, in *ReqReparseTextFiles, out *RspReparseTextFiles) error {
	return h.AiServiceHandler.ReparseTextFiles(ctx, in, out)
}

func (h *aiServiceHandler) SearchCollection(ctx context.Context, in *ReqSearchCollection, out *RspSearchCollection) error {
	return h.AiServiceHandler.SearchCollection(ctx, in, out)
}

func (h *aiServiceHandler) ListCollection(ctx context.Context, in *ReqListCollection, out *RspListCollection) error {
	return h.AiServiceHandler.ListCollection(ctx, in, out)
}

func (h *aiServiceHandler) ListContributor(ctx context.Context, in *ReqListContributor, out *RspListContributor) error {
	return h.AiServiceHandler.ListContributor(ctx, in, out)
}

func (h *aiServiceHandler) ListUpdateBy(ctx context.Context, in *ReqListUpdateBy, out *RspListUpdateBy) error {
	return h.AiServiceHandler.ListUpdateBy(ctx, in, out)
}

func (h *aiServiceHandler) ListCreateBy(ctx context.Context, in *ReqListUpdateBy, out *RspListUpdateBy) error {
	return h.AiServiceHandler.ListCreateBy(ctx, in, out)
}

func (h *aiServiceHandler) ListSharedAssistant(ctx context.Context, in *ReqListSharedAssistant, out *RspListSharedAssistant) error {
	return h.AiServiceHandler.ListSharedAssistant(ctx, in, out)
}

func (h *aiServiceHandler) ValidateQAInBulk(ctx context.Context, in *ReqValidateQAInBulk, out *RspValidateQAInBulk) error {
	return h.AiServiceHandler.ValidateQAInBulk(ctx, in, out)
}

func (h *aiServiceHandler) ValidateTextFileInBulk(ctx context.Context, in *ReqValidateTextFileInBulk, out *RspValidateTextFileInBulk) error {
	return h.AiServiceHandler.ValidateTextFileInBulk(ctx, in, out)
}

func (h *aiServiceHandler) CloneDocInBulk(ctx context.Context, in *ReqCloneDocInBulk, out *RspCloneDocInBulk) error {
	return h.AiServiceHandler.CloneDocInBulk(ctx, in, out)
}

func (h *aiServiceHandler) OnOffDocInBulk(ctx context.Context, in *ReqOnOffDocInBulk, out *RspOnOffDocInBulk) error {
	return h.AiServiceHandler.OnOffDocInBulk(ctx, in, out)
}

func (h *aiServiceHandler) ListDocByRef(ctx context.Context, in *ReqListDocByRef, out *RspListDocByRef) error {
	return h.AiServiceHandler.ListDocByRef(ctx, in, out)
}

func (h *aiServiceHandler) UpdateDocLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateDocLabels(ctx, in, out)
}

func (h *aiServiceHandler) ConvertCustomLabel(ctx context.Context, in *ReqConvertCustomLabel, out *RspConvertCustomLabel) error {
	return h.AiServiceHandler.ConvertCustomLabel(ctx, in, out)
}

func (h *aiServiceHandler) GetCustomLabelValueTopN(ctx context.Context, in *ReqGetCustomLabelValueTopN, out *RspGetCustomLabelValueTopN) error {
	return h.AiServiceHandler.GetCustomLabelValueTopN(ctx, in, out)
}

func (h *aiServiceHandler) UpdateDocAttrInBulk(ctx context.Context, in *ReqUpdateDocAttrInBulk, out *RspUpdateDocAttrInBulk) error {
	return h.AiServiceHandler.UpdateDocAttrInBulk(ctx, in, out)
}

func (h *aiServiceHandler) GetTextFileTip(ctx context.Context, in *ReqGetTextFileTip, out *RspGetTextFileTip) error {
	return h.AiServiceHandler.GetTextFileTip(ctx, in, out)
}

func (h *aiServiceHandler) CreateDocQuery(ctx context.Context, in *ReqCreateDocQuery, out *RspCreateDocQuery) error {
	return h.AiServiceHandler.CreateDocQuery(ctx, in, out)
}

func (h *aiServiceHandler) GetQaTip(ctx context.Context, in *ReqGetQaTip, out *RspGetQaTip) error {
	return h.AiServiceHandler.GetQaTip(ctx, in, out)
}

func (h *aiServiceHandler) CreateAllDocMd5(ctx context.Context, in *ReqCreateAllDocMd5, out *emptypb.Empty) error {
	return h.AiServiceHandler.CreateAllDocMd5(ctx, in, out)
}

func (h *aiServiceHandler) CreateDocShareConfigSender(ctx context.Context, in *ReqCreateDocShareConfigSender, out *emptypb.Empty) error {
	return h.AiServiceHandler.CreateDocShareConfigSender(ctx, in, out)
}

func (h *aiServiceHandler) ListeDocShareConfigSender(ctx context.Context, in *ReqListeDocShareConfigSender, out *RspListeDocShareConfigSender) error {
	return h.AiServiceHandler.ListeDocShareConfigSender(ctx, in, out)
}

func (h *aiServiceHandler) ListAssistantCanShareDoc(ctx context.Context, in *ReqListAssistantCanShareDoc, out *RspListAssistantCanShareDoc) error {
	return h.AiServiceHandler.ListAssistantCanShareDoc(ctx, in, out)
}

func (h *aiServiceHandler) ListTeamCanShareDoc(ctx context.Context, in *ReqListTeamCanShareDoc, out *RspListTeamCanShareDoc) error {
	return h.AiServiceHandler.ListTeamCanShareDoc(ctx, in, out)
}

func (h *aiServiceHandler) ListUserCanShareDoc(ctx context.Context, in *ReqListUserCanShareDoc, out *RspListUserCanShareDoc) error {
	return h.AiServiceHandler.ListUserCanShareDoc(ctx, in, out)
}

func (h *aiServiceHandler) ListSharedTeam(ctx context.Context, in *ReqListSharedTeam, out *RspListSharedTeam) error {
	return h.AiServiceHandler.ListSharedTeam(ctx, in, out)
}

func (h *aiServiceHandler) ListSharedUser(ctx context.Context, in *ReqListSharedUser, out *RspListSharedUser) error {
	return h.AiServiceHandler.ListSharedUser(ctx, in, out)
}

func (h *aiServiceHandler) ListMyAssistantIds(ctx context.Context, in *ReqListMyAssistantIds, out *RspListMyAssistantIds) error {
	return h.AiServiceHandler.ListMyAssistantIds(ctx, in, out)
}

func (h *aiServiceHandler) CreateDocShareConfigReceiverAssistant(ctx context.Context, in *ReqCreateDocShareConfigReceiverAssistant, out *emptypb.Empty) error {
	return h.AiServiceHandler.CreateDocShareConfigReceiverAssistant(ctx, in, out)
}

func (h *aiServiceHandler) CreateDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqCreateDocShareConfigReceiverUserTeam, out *emptypb.Empty) error {
	return h.AiServiceHandler.CreateDocShareConfigReceiverUserTeam(ctx, in, out)
}

func (h *aiServiceHandler) ListDocShareConfigReceiverAssistant(ctx context.Context, in *ReqListDocShareConfigReceiverAssistant, out *RspListDocShareConfigReceiverAssistant) error {
	return h.AiServiceHandler.ListDocShareConfigReceiverAssistant(ctx, in, out)
}

func (h *aiServiceHandler) ListDocShareConfigReceiverUserTeam(ctx context.Context, in *ReqListDocShareConfigReceiverUserTeam, out *RspListDocShareConfigReceiverUserTeam) error {
	return h.AiServiceHandler.ListDocShareConfigReceiverUserTeam(ctx, in, out)
}

func (h *aiServiceHandler) CreateDocShare(ctx context.Context, in *ReqCreateDocShare, out *RspCreateDocShare) error {
	return h.AiServiceHandler.CreateDocShare(ctx, in, out)
}

func (h *aiServiceHandler) CreateDocShareAssistant(ctx context.Context, in *ReqCreateDocShare, out *RspCreateDocShare) error {
	return h.AiServiceHandler.CreateDocShareAssistant(ctx, in, out)
}

func (h *aiServiceHandler) CreateOpenWechat(ctx context.Context, in *ReqCreateOpenWechat, out *emptypb.Empty) error {
	return h.AiServiceHandler.CreateOpenWechat(ctx, in, out)
}

func (h *aiServiceHandler) DescribeOpenWechat(ctx context.Context, in *ReqDescribeOpenWechat, out *RspDescribeOpenWechat) error {
	return h.AiServiceHandler.DescribeOpenWechat(ctx, in, out)
}

func (h *aiServiceHandler) AuthTencentCode(ctx context.Context, in *ReqAuthTencentCode, out *RspAuthTencentCode) error {
	return h.AiServiceHandler.AuthTencentCode(ctx, in, out)
}

func (h *aiServiceHandler) DescribeTokenIsEmpty(ctx context.Context, in *ReqDescribeTokenIsEmpty, out *RspDescribeTokenIsEmpty) error {
	return h.AiServiceHandler.DescribeTokenIsEmpty(ctx, in, out)
}

func (h *aiServiceHandler) ListExternalSourceUser(ctx context.Context, in *ReqListExternalSourceUser, out *RspListExternalSourceUser) error {
	return h.AiServiceHandler.ListExternalSourceUser(ctx, in, out)
}

func (h *aiServiceHandler) CreateGTBText(ctx context.Context, in *ReqCreateGTBText, out *RspCreateGTBText) error {
	return h.AiServiceHandler.CreateGTBText(ctx, in, out)
}

func (h *aiServiceHandler) DescribeTencentDocTask(ctx context.Context, in *ReqDescribeTencentDocTask, out *RspDescribeTencentDocTask) error {
	return h.AiServiceHandler.DescribeTencentDocTask(ctx, in, out)
}

func (h *aiServiceHandler) DelTencentDocAuth(ctx context.Context, in *ReqDelTencentDocAuth, out *emptypb.Empty) error {
	return h.AiServiceHandler.DelTencentDocAuth(ctx, in, out)
}

func (h *aiServiceHandler) DescribeGTBText(ctx context.Context, in *ReqDescribeGTBText, out *RspDescribeGTBText) error {
	return h.AiServiceHandler.DescribeGTBText(ctx, in, out)
}

func (h *aiServiceHandler) CreateTencentDocAuthUrl(ctx context.Context, in *ReqCreateTencentDocAuthUrl, out *RspCreateTencentDocAuthUrl) error {
	return h.AiServiceHandler.CreateTencentDocAuthUrl(ctx, in, out)
}

func (h *aiServiceHandler) DescribeDocList(ctx context.Context, in *ReqDescribeTencentDocList, out *RspDescribeTencentDocList) error {
	return h.AiServiceHandler.DescribeDocList(ctx, in, out)
}

func (h *aiServiceHandler) ImportTencentDoc(ctx context.Context, in *ReqImportTencentDoc, out *RspImportTencentDoc) error {
	return h.AiServiceHandler.ImportTencentDoc(ctx, in, out)
}

func (h *aiServiceHandler) ReimportTencentDoc(ctx context.Context, in *ReqReimportTencentDoc, out *RspReimportTencentDoc) error {
	return h.AiServiceHandler.ReimportTencentDoc(ctx, in, out)
}

func (h *aiServiceHandler) ImportTencentDocWebClip(ctx context.Context, in *ReqImportTencentDocWebClip, out *RspImportTencentDocWebClip) error {
	return h.AiServiceHandler.ImportTencentDocWebClip(ctx, in, out)
}

func (h *aiServiceHandler) ModifyDocTab(ctx context.Context, in *ReqModifyDocTab, out *emptypb.Empty) error {
	return h.AiServiceHandler.ModifyDocTab(ctx, in, out)
}

func (h *aiServiceHandler) DescribeDocTab(ctx context.Context, in *ReqDescribeDocTab, out *RspDescribeDocTab) error {
	return h.AiServiceHandler.DescribeDocTab(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMyDoc(ctx context.Context, in *ReqDescribeMyDoc, out *RspDescribeMyDoc) error {
	return h.AiServiceHandler.DescribeMyDoc(ctx, in, out)
}

func (h *aiServiceHandler) CreateNebulaTask(ctx context.Context, in *ReqCreateNebulaTask, out *RspCreateNebulaTask) error {
	return h.AiServiceHandler.CreateNebulaTask(ctx, in, out)
}

func (h *aiServiceHandler) DescribeNebulaTask(ctx context.Context, in *ReqDescribeNebulaTask, out *RspDescribeNebulaTask) error {
	return h.AiServiceHandler.DescribeNebulaTask(ctx, in, out)
}

func (h *aiServiceHandler) DescribeNebulaTaskList(ctx context.Context, in *ReqDescribeNebulaTaskList, out *RspDescribeNebulaTaskList) error {
	return h.AiServiceHandler.DescribeNebulaTaskList(ctx, in, out)
}

func (h *aiServiceHandler) DescribeNebulaProjection(ctx context.Context, in *ReqDescribeNebulaProjection, out *RspDescribeNebulaProjection) error {
	return h.AiServiceHandler.DescribeNebulaProjection(ctx, in, out)
}

func (h *aiServiceHandler) DescribeNebulaData(ctx context.Context, in *ReqDescribeNebulaData, out *RspDescribeNebulaData) error {
	return h.AiServiceHandler.DescribeNebulaData(ctx, in, out)
}

func (h *aiServiceHandler) BatchCreateNebulaTasks(ctx context.Context, in *ReqBatchCreateNebulaTasks, out *RspBatchCreateNebulaTasks) error {
	return h.AiServiceHandler.BatchCreateNebulaTasks(ctx, in, out)
}

func (h *aiServiceHandler) CreateAssistantCollectionInit(ctx context.Context, in *ReqCreateAssistantCollectionInit, out *RspCreateAssistantCollectionInit) error {
	return h.AiServiceHandler.CreateAssistantCollectionInit(ctx, in, out)
}

func (h *aiServiceHandler) CreateChatMessage(ctx context.Context, in *ReqCreateChatMessage, out *RspCreateChatMessage) error {
	return h.AiServiceHandler.CreateChatMessage(ctx, in, out)
}

func (h *aiServiceHandler) UpdateChatMessageThink(ctx context.Context, in *ReqUpdateChatMessageThink, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateChatMessageThink(ctx, in, out)
}

func (h *aiServiceHandler) UpdateChatMessage(ctx context.Context, in *ReqUpdateChatMessage, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateChatMessage(ctx, in, out)
}

func (h *aiServiceHandler) PublishChatMessage(ctx context.Context, in *ReqPublishChatMessage, out *emptypb.Empty) error {
	return h.AiServiceHandler.PublishChatMessage(ctx, in, out)
}

func (h *aiServiceHandler) CreateChatTaskMessage(ctx context.Context, in *ReqCreateChatTaskMessage, out *RspCreateChatTaskMessage) error {
	return h.AiServiceHandler.CreateChatTaskMessage(ctx, in, out)
}

func (h *aiServiceHandler) SendMessageWithoutSaveSync(ctx context.Context, in *ReqSendMessageWithoutSaveSync, out *RspSendMessageWithoutSaveSync) error {
	return h.AiServiceHandler.SendMessageWithoutSaveSync(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMessage(ctx context.Context, in *ReqDescribeMessage, out *RspDescribeMessage) error {
	return h.AiServiceHandler.DescribeMessage(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMessageByQuestionId(ctx context.Context, in *ReqDescribeMessageByQuestionId, out *RspDescribeMessageByQuestionId) error {
	return h.AiServiceHandler.DescribeMessageByQuestionId(ctx, in, out)
}

func (h *aiServiceHandler) CreateUserChat(ctx context.Context, in *ReqCreateUserChat, out *RspCreateUserChat) error {
	return h.AiServiceHandler.CreateUserChat(ctx, in, out)
}

func (h *aiServiceHandler) UpdateUserChatLabels(ctx context.Context, in *ReqUpdateCustomChatLabels, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateUserChatLabels(ctx, in, out)
}

func (h *aiServiceHandler) DescribeUserChats(ctx context.Context, in *ReqDescribeUserChats, out *RspDescribeUserChats) error {
	return h.AiServiceHandler.DescribeUserChats(ctx, in, out)
}

func (h *aiServiceHandler) DeleteUserChat(ctx context.Context, in *ReqDeleteUserChat, out *emptypb.Empty) error {
	return h.AiServiceHandler.DeleteUserChat(ctx, in, out)
}

func (h *aiServiceHandler) DeleteChatMessage(ctx context.Context, in *ReqDeleteChatMessage, out *emptypb.Empty) error {
	return h.AiServiceHandler.DeleteChatMessage(ctx, in, out)
}

func (h *aiServiceHandler) CreateSystemDocCopy(ctx context.Context, in *ReqCreateSystemDocCopy, out *RspCreateSystemDocCopy) error {
	return h.AiServiceHandler.CreateSystemDocCopy(ctx, in, out)
}

func (h *aiServiceHandler) EnableSystemDoc(ctx context.Context, in *ReqEnableSystemDoc, out *emptypb.Empty) error {
	return h.AiServiceHandler.EnableSystemDoc(ctx, in, out)
}

func (h *aiServiceHandler) DisableSystemDoc(ctx context.Context, in *ReqDisableSystemDoc, out *emptypb.Empty) error {
	return h.AiServiceHandler.DisableSystemDoc(ctx, in, out)
}

func (h *aiServiceHandler) DeleteSystemDoc(ctx context.Context, in *ReqDeleteSystemDoc, out *emptypb.Empty) error {
	return h.AiServiceHandler.DeleteSystemDoc(ctx, in, out)
}

func (h *aiServiceHandler) RateAiAnswer(ctx context.Context, in *ReqRateAiAnswer, out *emptypb.Empty) error {
	return h.AiServiceHandler.RateAiAnswer(ctx, in, out)
}

func (h *aiServiceHandler) UpsertUserFeedback(ctx context.Context, in *ReqUpsertUserFeedback, out *RspUpsertFeedback) error {
	return h.AiServiceHandler.UpsertUserFeedback(ctx, in, out)
}

func (h *aiServiceHandler) UpsertOpFeedback(ctx context.Context, in *ReqUpsertOpFeedback, out *RspUpsertFeedback) error {
	return h.AiServiceHandler.UpsertOpFeedback(ctx, in, out)
}

func (h *aiServiceHandler) UpsertMgmtFeedback(ctx context.Context, in *ReqUpsertMgmtFeedback, out *RspUpsertFeedback) error {
	return h.AiServiceHandler.UpsertMgmtFeedback(ctx, in, out)
}

func (h *aiServiceHandler) GetFeedbacks(ctx context.Context, in *ReqGetFeedbacks, out *RspGetFeedbacks) error {
	return h.AiServiceHandler.GetFeedbacks(ctx, in, out)
}

func (h *aiServiceHandler) ReadFeedback(ctx context.Context, in *ReqReadFeedback, out *emptypb.Empty) error {
	return h.AiServiceHandler.ReadFeedback(ctx, in, out)
}

func (h *aiServiceHandler) AcceptFeedback(ctx context.Context, in *ReqAcceptFeedback, out *emptypb.Empty) error {
	return h.AiServiceHandler.AcceptFeedback(ctx, in, out)
}

func (h *aiServiceHandler) GetFeedbackLogs(ctx context.Context, in *ReqGetFeedbackLogs, out *RspGetFeedbackLogs) error {
	return h.AiServiceHandler.GetFeedbackLogs(ctx, in, out)
}

func (h *aiServiceHandler) GetDocs(ctx context.Context, in *ReqGetDocs, out *RspGetDocs) error {
	return h.AiServiceHandler.GetDocs(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMessageDocs(ctx context.Context, in *ReqDescribeMessageDocs, out *RspDescribeMessageDocs) error {
	return h.AiServiceHandler.DescribeMessageDocs(ctx, in, out)
}

func (h *aiServiceHandler) DescribeSuggestLogs(ctx context.Context, in *ReqDescribeSuggestLog, out *RspDescribeSuggestLog) error {
	return h.AiServiceHandler.DescribeSuggestLogs(ctx, in, out)
}

func (h *aiServiceHandler) DescribeMessageLogs(ctx context.Context, in *ReqDescribeMessageLog, out *RspDescribeMessageLog) error {
	return h.AiServiceHandler.DescribeMessageLogs(ctx, in, out)
}

func (h *aiServiceHandler) SyncFixChatMessageCollection(ctx context.Context, in *ReqSyncFixChatMessageCollection, out *emptypb.Empty) error {
	return h.AiServiceHandler.SyncFixChatMessageCollection(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistantChatCreators(ctx context.Context, in *ReqGetAssistantChatCreators, out *RspGetAssistantChatCreators) error {
	return h.AiServiceHandler.GetAssistantChatCreators(ctx, in, out)
}

func (h *aiServiceHandler) ListChat(ctx context.Context, in *ReqListChat, out *RspListChat) error {
	return h.AiServiceHandler.ListChat(ctx, in, out)
}

func (h *aiServiceHandler) GetChatDetail(ctx context.Context, in *ReqGetChatDetail, out *RspGetChatDetail) error {
	return h.AiServiceHandler.GetChatDetail(ctx, in, out)
}

func (h *aiServiceHandler) ListCustomLabel(ctx context.Context, in *ReqListCustomLabel, out *RspListCustomLabel) error {
	return h.AiServiceHandler.ListCustomLabel(ctx, in, out)
}

func (h *aiServiceHandler) UpsertCustomLabels(ctx context.Context, in *ReqUpsertCustomLabels, out *RspUpsertCustomLabels) error {
	return h.AiServiceHandler.UpsertCustomLabels(ctx, in, out)
}

func (h *aiServiceHandler) DeleteCustomLabels(ctx context.Context, in *ReqDeleteCustomLabels, out *emptypb.Empty) error {
	return h.AiServiceHandler.DeleteCustomLabels(ctx, in, out)
}

func (h *aiServiceHandler) ListAssistant(ctx context.Context, in *ReqListAssistant, out *RspListAssistant) error {
	return h.AiServiceHandler.ListAssistant(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistant(ctx context.Context, in *ReqGetAssistant, out *RspGetAssistant) error {
	return h.AiServiceHandler.GetAssistant(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistantInfoMap(ctx context.Context, in *ReqGetAssistantInfoMap, out *RspGetAssistantInfoMap) error {
	return h.AiServiceHandler.GetAssistantInfoMap(ctx, in, out)
}

func (h *aiServiceHandler) ListChatLiveAgent(ctx context.Context, in *ReqListChatLiveAgent, out *RspListChatLiveAgent) error {
	return h.AiServiceHandler.ListChatLiveAgent(ctx, in, out)
}

func (h *aiServiceHandler) SwitchChatLiveAgent(ctx context.Context, in *ReqSwitchChatLiveAgent, out *emptypb.Empty) error {
	return h.AiServiceHandler.SwitchChatLiveAgent(ctx, in, out)
}

func (h *aiServiceHandler) LiveAgentStatusChange(ctx context.Context, in *ReqLiveAgentStatusChange, out *emptypb.Empty) error {
	return h.AiServiceHandler.LiveAgentStatusChange(ctx, in, out)
}

func (h *aiServiceHandler) RateAiAnswerWechat(ctx context.Context, in *ReqRateAiAnswerWechat, out *emptypb.Empty) error {
	return h.AiServiceHandler.RateAiAnswerWechat(ctx, in, out)
}

func (h *aiServiceHandler) GetAnswerWechat(ctx context.Context, in *ReqGetAnswerWechat, out *RspGetAnswerWechat) error {
	return h.AiServiceHandler.GetAnswerWechat(ctx, in, out)
}

func (h *aiServiceHandler) GetAnswerWechatForFile(ctx context.Context, in *ReqGetAnswerWechat, out *RspGetAnswerWechat) error {
	return h.AiServiceHandler.GetAnswerWechatForFile(ctx, in, out)
}

func (h *aiServiceHandler) GetAnswerWechatForContinue(ctx context.Context, in *ReqGetAnswerWechat, out *RspGetAnswerWechat) error {
	return h.AiServiceHandler.GetAnswerWechatForContinue(ctx, in, out)
}

func (h *aiServiceHandler) FinishChatWechat(ctx context.Context, in *ReqFinishChatWechat, out *emptypb.Empty) error {
	return h.AiServiceHandler.FinishChatWechat(ctx, in, out)
}

func (h *aiServiceHandler) GetWhiteListWechat(ctx context.Context, in *ReqGetWhiteListWechat, out *RspGetWhiteListWechat) error {
	return h.AiServiceHandler.GetWhiteListWechat(ctx, in, out)
}

func (h *aiServiceHandler) UpdateChatMessageStateWechat(ctx context.Context, in *ReqUpdateChatMessageStateWechat, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateChatMessageStateWechat(ctx, in, out)
}

func (h *aiServiceHandler) GetMessageCursorWechat(ctx context.Context, in *ReqGetMessageCursorWechat, out *RspGetMessageCursorWechat) error {
	return h.AiServiceHandler.GetMessageCursorWechat(ctx, in, out)
}

func (h *aiServiceHandler) GetChatWechat(ctx context.Context, in *ReqGetChatWechat, out *RspGetChatWechat) error {
	return h.AiServiceHandler.GetChatWechat(ctx, in, out)
}

func (h *aiServiceHandler) CreateChatWechat(ctx context.Context, in *ReqCreateChatWechat, out *RspCreateChatWechat) error {
	return h.AiServiceHandler.CreateChatWechat(ctx, in, out)
}

func (h *aiServiceHandler) GetLastQuestionStateWechat(ctx context.Context, in *ReqGetLastQuestionStateWechat, out *RspGetLastQuestionStateWechat) error {
	return h.AiServiceHandler.GetLastQuestionStateWechat(ctx, in, out)
}

func (h *aiServiceHandler) UpdateChatMessageRecordWechat(ctx context.Context, in *ReqUpdateChatMessageRecordWechat, out *RspUpdateChatMessageRecordWechat) error {
	return h.AiServiceHandler.UpdateChatMessageRecordWechat(ctx, in, out)
}

func (h *aiServiceHandler) CreateSendRecord(ctx context.Context, in *ReqCreateSendRecord, out *RspCreateSendRecord) error {
	return h.AiServiceHandler.CreateSendRecord(ctx, in, out)
}

func (h *aiServiceHandler) UpdateSendRecord(ctx context.Context, in *ReqUpdateSendRecord, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateSendRecord(ctx, in, out)
}

func (h *aiServiceHandler) DescribeAssistantMessage(ctx context.Context, in *ReqDescribeAssistantMessage, out *RspDescribeAssistantMessage) error {
	return h.AiServiceHandler.DescribeAssistantMessage(ctx, in, out)
}

func (h *aiServiceHandler) InsertAssistantMessageRecord(ctx context.Context, in *ReqInsertAssistantMessageRecord, out *emptypb.Empty) error {
	return h.AiServiceHandler.InsertAssistantMessageRecord(ctx, in, out)
}

func (h *aiServiceHandler) MigrationChatMessageInfo(ctx context.Context, in *ReqMigrationChatMessageInfo, out *emptypb.Empty) error {
	return h.AiServiceHandler.MigrationChatMessageInfo(ctx, in, out)
}

func (h *aiServiceHandler) DescribeChatRegionCode(ctx context.Context, in *ReqDescribeChatRegionCode, out *RspDescribeChatRegionCode) error {
	return h.AiServiceHandler.DescribeChatRegionCode(ctx, in, out)
}

func (h *aiServiceHandler) DescribeFeedbackRegionCode(ctx context.Context, in *ReqDescribeFeedbackRegionCode, out *RspDescribeFeedbackRegionCode) error {
	return h.AiServiceHandler.DescribeFeedbackRegionCode(ctx, in, out)
}

func (h *aiServiceHandler) DescribeUserChatRecords(ctx context.Context, in *ReqDescribeUserChatRecords, out *RspDescribeUserChatRecords) error {
	return h.AiServiceHandler.DescribeUserChatRecords(ctx, in, out)
}

func (h *aiServiceHandler) DescribeChatLogAuthItem(ctx context.Context, in *ReqDescribeChatLogAuthItem, out *RspDescribeChatLogAuthItem) error {
	return h.AiServiceHandler.DescribeChatLogAuthItem(ctx, in, out)
}

func (h *aiServiceHandler) FetchHtmlTitles(ctx context.Context, in *ReqFetchHtmlTitles, out *RspFetchHtmlTitles) error {
	return h.AiServiceHandler.FetchHtmlTitles(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistants(ctx context.Context, in *ReqGetAssistants, out *RspGetAssistants) error {
	return h.AiServiceHandler.GetAssistants(ctx, in, out)
}

func (h *aiServiceHandler) BatchCreateAssistant(ctx context.Context, in *ReqBatchCreateAssistant, out *RspBatchCreateAssistant) error {
	return h.AiServiceHandler.BatchCreateAssistant(ctx, in, out)
}

func (h *aiServiceHandler) BatchUpdateAssistant(ctx context.Context, in *ReqBatchUpdateAssistant, out *emptypb.Empty) error {
	return h.AiServiceHandler.BatchUpdateAssistant(ctx, in, out)
}

func (h *aiServiceHandler) DeleteAssistant(ctx context.Context, in *ReqDeleteAssistant, out *emptypb.Empty) error {
	return h.AiServiceHandler.DeleteAssistant(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistantLogs(ctx context.Context, in *ReqGetAssistantLogs, out *RspGetAssistantLogs) error {
	return h.AiServiceHandler.GetAssistantLogs(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistantOptions(ctx context.Context, in *emptypb.Empty, out *RspGetAssistantOptions) error {
	return h.AiServiceHandler.GetAssistantOptions(ctx, in, out)
}

func (h *aiServiceHandler) GetDocChunks(ctx context.Context, in *ReqGetDocChunks, out *RspGetDocChunks) error {
	return h.AiServiceHandler.GetDocChunks(ctx, in, out)
}

func (h *aiServiceHandler) ChunkDoc(ctx context.Context, in *ReqChunkDoc, out *RspChunkDoc) error {
	return h.AiServiceHandler.ChunkDoc(ctx, in, out)
}

func (h *aiServiceHandler) GetChunkDocTasks(ctx context.Context, in *ReqGetChunkDocTasks, out *RspGetChunkDocTasks) error {
	return h.AiServiceHandler.GetChunkDocTasks(ctx, in, out)
}

func (h *aiServiceHandler) GetDocEmbeddingModels(ctx context.Context, in *ReqGetDocEmbeddingModels, out *RspGetDocEmbeddingModels) error {
	return h.AiServiceHandler.GetDocEmbeddingModels(ctx, in, out)
}

func (h *aiServiceHandler) CheckAssistantAllowlist(ctx context.Context, in *ReqCheckAssistantAllowlist, out *RspCheckAssistantAllowlist) error {
	return h.AiServiceHandler.CheckAssistantAllowlist(ctx, in, out)
}

func (h *aiServiceHandler) GetRecentlyUsedAssistantIds(ctx context.Context, in *ReqGetRecentlyUsedAssistantIds, out *RspGetRecentlyUsedAssistantIds) error {
	return h.AiServiceHandler.GetRecentlyUsedAssistantIds(ctx, in, out)
}

func (h *aiServiceHandler) GetRecentlyUsedAssistants(ctx context.Context, in *ReqGetRecentlyUsedAssistants, out *RspGetRecentlyUsedAssistants) error {
	return h.AiServiceHandler.GetRecentlyUsedAssistants(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistantAdmin(ctx context.Context, in *ReqGetAssistantAdmin, out *RspGetAssistantAdmin) error {
	return h.AiServiceHandler.GetAssistantAdmin(ctx, in, out)
}

func (h *aiServiceHandler) GetAssistantChatUser(ctx context.Context, in *ReqGetAssistantChatUser, out *RspGetAssistantChatUser) error {
	return h.AiServiceHandler.GetAssistantChatUser(ctx, in, out)
}

func (h *aiServiceHandler) CreateChatShare(ctx context.Context, in *ReqCreateChatShare, out *RspCreateChatShare) error {
	return h.AiServiceHandler.CreateChatShare(ctx, in, out)
}

func (h *aiServiceHandler) ContinueChatFromShare(ctx context.Context, in *ReqContinueChatFromShare, out *RspContinueChatFromShare) error {
	return h.AiServiceHandler.ContinueChatFromShare(ctx, in, out)
}

func (h *aiServiceHandler) ListChatShares(ctx context.Context, in *ReqListChatShares, out *RspListChatShares) error {
	return h.AiServiceHandler.ListChatShares(ctx, in, out)
}

func (h *aiServiceHandler) UpdateChatShareStatus(ctx context.Context, in *ReqUpdateChatShareStatus, out *emptypb.Empty) error {
	return h.AiServiceHandler.UpdateChatShareStatus(ctx, in, out)
}

func (h *aiServiceHandler) ListChatShareAccesses(ctx context.Context, in *ReqListChatShareAccesses, out *RspListChatShareAccesses) error {
	return h.AiServiceHandler.ListChatShareAccesses(ctx, in, out)
}

func (h *aiServiceHandler) GetChatShareMessages(ctx context.Context, in *ReqGetChatShareMessages, out *RspGetChatShareMessages) error {
	return h.AiServiceHandler.GetChatShareMessages(ctx, in, out)
}

func (h *aiServiceHandler) GetChatShareRecord(ctx context.Context, in *ReqGetChatShareRecord, out *RspGetChatShareRecord) error {
	return h.AiServiceHandler.GetChatShareRecord(ctx, in, out)
}

func (h *aiServiceHandler) RecordChatShareAccess(ctx context.Context, in *ReqRecordChatShareAccess, out *RspRecordChatShareAccess) error {
	return h.AiServiceHandler.RecordChatShareAccess(ctx, in, out)
}
