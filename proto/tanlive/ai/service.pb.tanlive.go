// Code generated by protoc-gen-tanlive. DO NOT EDIT.

package ai

import (
	validator "e.coding.net/tencent-ssv/tanlive/gokits/validator"
	base "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	proto "google.golang.org/protobuf/proto"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Title":       "required",
		"AssistantId": "required",
		"RegionCode":  "required",
	}, &ReqCreateUserChat{})
}

func (x *ReqCreateUserChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
	}, &ReqCreateChatMessage{})
}

func (x *ReqCreateChatMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Message": "required",
		"UserId":  "required",
	}, &ReqPublishChatMessage{})
}

func (x *ReqPublishChatMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantDetail": "required",
	}, &ReqSendMessageSync{})
}

func (x *ReqSendMessageSync) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Text":            "required",
		"QuestionId":      "required",
		"AssistantDetail": "required",
	}, &ReqSendMessageWithoutSaveSync{})
}

func (x *ReqSendMessageWithoutSaveSync) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
	}, &ReqDescribeMessage{})
}

func (x *ReqDescribeMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"QuestionId": "required",
	}, &ReqDescribeMessageByQuestionId{})
}

func (x *ReqDescribeMessageByQuestionId) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id": "required",
	}, &ReqUpdateChatMessage{})
}

func (x *ReqUpdateChatMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
	}, &ReqDescribeUserChats{})
}

func (x *ReqDescribeUserChats) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId": "required",
		"ChatId": "required",
	}, &ReqDeleteUserChat{})
}

func (x *ReqDeleteUserChat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"CopyFrom": "required",
		"CreateBy": "required",
	}, &ReqCreateSystemDocCopy{})
}

func (x *ReqCreateSystemDocCopy) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":    "required",
		"Operator": "required",
	}, &ReqEnableSystemDoc{})
}

func (x *ReqEnableSystemDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":    "required",
		"Operator": "required",
	}, &ReqDisableSystemDoc{})
}

func (x *ReqDisableSystemDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":    "required",
		"Operator": "required",
	}, &ReqDeleteSystemDoc{})
}

func (x *ReqDeleteSystemDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ChatMessageId": "required",
		"RatingScale":   "required",
		"UpdateBy":      "required",
	}, &ReqRateAiAnswer{})
}

func (x *ReqRateAiAnswer) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ByAssistantId": "required",
		"ByAnswerId":    "required",
		"Question":      "required_with=AssistantId",
		"Answer":        "required",
		"References":    "omitempty,dive",
		"Operator":      "required",
	}, &ReqUpsertUserFeedback{})
}

func (x *ReqUpsertUserFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AnswerRating":        "required",
		"DocId":               "omitempty,dive,required",
		"Operator":            "required",
		"OperatorAssistantId": "required",
		"ByAnswerId":          "required",
		"ByFeedbackId":        "required",
	}, &ReqUpsertOpFeedback{})
}

func (x *ReqUpsertOpFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AnswerRating": "required",
		"MgmtFeedback": "required",
		"Operator":     "required",
		"ByAnswerId":   "required",
		"ByFeedbackId": "required",
		"MgmtDocId":    "omitempty,dive,required",
	}, &ReqUpsertMgmtFeedback{})
}

func (x *ReqUpsertMgmtFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FeedbackId": "required",
	}, &ReqReadFeedback{})
}

func (x *ReqReadFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"FeedbackId": "required",
		"Operator":   "required",
	}, &ReqAcceptFeedback{})
}

func (x *ReqAcceptFeedback) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqGetDocs{})
}

func (x *ReqGetDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantIds": "required,dive,required",
	}, &ReqGetAssistantChatCreators{})
}

func (x *ReqGetAssistantChatCreators) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
		"HashId":    "required",
	}, &ReqStopAnswerReply{})
}

func (x *ReqStopAnswerReply) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocNames": "required",
	}, &ReqDescribeMessageDocs{})
}

func (x *ReqDescribeMessageDocs) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
	}, &ReqDescribeMessageLog{})
}

func (x *ReqDescribeMessageLog) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
	}, &ReqDescribeSuggestLog{})
}

func (x *ReqDescribeSuggestLog) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId":   "required",
		"ReceiverState": "required",
	}, &ReqCreateDocShareConfigReceiverAssistant{})
}

func (x *ReqCreateDocShareConfigReceiverAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Code": "required",
	}, &ReqAuthTencentCode{})
}

func (x *ReqAuthTencentCode) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Message":         "required",
		"AssistantDetail": "required",
	}, &ReqGetAnswerWechat{})
}

func (x *ReqGetAnswerWechat) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ChatId": "min=1",
	}, &ReqDescribeUserChatRecords{})
}

func (x *ReqDescribeUserChatRecords) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
	}, &ReqDescribeAssistantMessage{})
}

func (x *ReqDescribeAssistantMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId":       "required",
		"QuestionText":    "required",
		"AnswerText":      "required",
		"AssistantDetail": "required",
	}, &ReqCreateMessageSuggestQuestion{})
}

func (x *ReqCreateMessageSuggestQuestion) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Configs":  "required",
		"CreateBy": "required",
	}, &ReqBatchCreateAssistant{})
}

func (x *ReqBatchCreateAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Items":    "required,dive,required",
		"UpdateBy": "required",
	}, &ReqBatchUpdateAssistant{})
}

func (x *ReqBatchUpdateAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Config": "required",
		"Mask":   "required",
	}, &ReqBatchUpdateAssistant_Item{})
}

func (x *ReqBatchUpdateAssistant_Item) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UpdateBy":      "required",
		"ByAssistantId": "required",
		"ByBatchNo":     "required",
	}, &ReqDeleteAssistant{})
}

func (x *ReqDeleteAssistant) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required,dive,required",
	}, &ReqDeleteAssistant_AssistantId{})
}

func (x *ReqDeleteAssistant_AssistantId) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":          "omitempty,dive,required",
		"AssistantId": "omitempty,dive,required",
		"Action":      "omitempty,dive,required",
	}, &ReqGetAssistantLogs_Filter{})
}

func (x *ReqGetAssistantLogs_Filter) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantId": "required",
	}, &ReqCheckAssistantAllowlist{})
}

func (x *ReqCheckAssistantAllowlist) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Phone": "phone",
	}, &ReqCheckAssistantAllowlist_PhonePara{})
}

func (x *ReqCheckAssistantAllowlist_PhonePara) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetDocChunks{})
}

func (x *ReqGetDocChunks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId":      "required",
		"AutoPara":   "required",
		"ManualPara": "required",
	}, &ReqChunkDoc{})
}

func (x *ReqChunkDoc) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required,dive,required",
	}, &ReqGetChunkDocTasks{})
}

func (x *ReqGetChunkDocTasks) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"DocId": "required",
	}, &ReqGetDocEmbeddingModels{})
}

func (x *ReqGetDocEmbeddingModels) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"QuestionId":      "required",
		"AssistantDetail": "required",
	}, &ReqResendMessageSync{})
}

func (x *ReqResendMessageSync) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Id":   "required",
		"Mask": "required",
	}, &ReqUpdateDocAttrInBulk{})
}

func (x *ReqUpdateDocAttrInBulk) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Docs": "required",
	}, &ReqCreateQaMatchMessage{})
}

func (x *ReqCreateQaMatchMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"UserId":   "required",
		"ChatType": "required",
	}, &ReqGetRecentlyUsedAssistantIds{})
}

func (x *ReqGetRecentlyUsedAssistantIds) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"HashId":    "required",
		"MessageId": "required",
		"ChatId":    "required",
	}, &ReqCreateChatOperation{})
}

func (x *ReqCreateChatOperation) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ChatId": "required",
	}, &ReqDescribeChatQuestionAnswersByPage{})
}

func (x *ReqDescribeChatQuestionAnswersByPage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"AssistantDetail": "required",
	}, &ReqCreateChatTaskMessage{})
}

func (x *ReqCreateChatTaskMessage) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"StartTime": "required",
	}, &ReqUpdateChatMessageCollections{})
}

func (x *ReqUpdateChatMessageCollections) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
	}, &ReqDescribeChatMessageFileState{})
}

func (x *ReqDescribeChatMessageFileState) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"MessageId": "required",
	}, &ReqStopQuestionReply{})
}

func (x *ReqStopQuestionReply) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId": "required",
	}, &ReqGetChatShareRecord{})
}

func (x *ReqGetChatShareRecord) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ChatId":     "required",
		"MessageIds": "required,min=1",
		"UserId":     "required",
	}, &ReqCreateChatShare{})
}

func (x *ReqCreateChatShare) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"Messages":    "required",
		"UserId":      "required",
		"ShareId":     "required",
		"AssistantId": "required",
		"RegionCode":  "required",
	}, &ReqContinueChatFromShare{})
}

func (x *ReqContinueChatFromShare) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId": "required",
		"Status":  "required",
	}, &ReqUpdateChatShareStatus{})
}

func (x *ReqUpdateChatShareStatus) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId": "required",
	}, &ReqListChatShareAccesses{})
}

func (x *ReqListChatShareAccesses) Validate() error {
	return validator.Validator().Struct(x)
}

func init() {
	validator.Validator().RegisterStructValidationMapRules(map[string]string{
		"ShareId": "required",
	}, &ReqRecordChatShareAccess{})
}

func (x *ReqRecordChatShareAccess) Validate() error {
	return validator.Validator().Struct(x)
}

func (x *ReqCreateChatMessage) MaskInLog() any {
	if x == nil {
		return (*ReqCreateChatMessage)(nil)
	}

	y := proto.Clone(x).(*ReqCreateChatMessage)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *ReqCreateChatMessage) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *ReqCreateChatMessage) MaskInBff() any {
	if x == nil {
		return (*ReqCreateChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *RspCreateChatMessage) MaskInLog() any {
	if x == nil {
		return (*RspCreateChatMessage)(nil)
	}

	y := proto.Clone(x).(*RspCreateChatMessage)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *RspCreateChatMessage) MaskInRpc() any {
	if x == nil {
		return (*RspCreateChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *RspCreateChatMessage) MaskInBff() any {
	if x == nil {
		return (*RspCreateChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *ReqPublishChatMessage) MaskInLog() any {
	if x == nil {
		return (*ReqPublishChatMessage)(nil)
	}

	y := proto.Clone(x).(*ReqPublishChatMessage)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*EventChatMessage)
	}

	return y
}

func (x *ReqPublishChatMessage) MaskInRpc() any {
	if x == nil {
		return (*ReqPublishChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*EventChatMessage)
	}

	return y
}

func (x *ReqPublishChatMessage) MaskInBff() any {
	if x == nil {
		return (*ReqPublishChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*EventChatMessage)
	}

	return y
}

func (x *ReqSendMessageSync) MaskInLog() any {
	if x == nil {
		return (*ReqSendMessageSync)(nil)
	}

	y := proto.Clone(x).(*ReqSendMessageSync)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}

	return y
}

func (x *ReqSendMessageSync) MaskInRpc() any {
	if x == nil {
		return (*ReqSendMessageSync)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}

	return y
}

func (x *ReqSendMessageSync) MaskInBff() any {
	if x == nil {
		return (*ReqSendMessageSync)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}

	return y
}

func (x *RspSendMessageSync) MaskInLog() any {
	if x == nil {
		return (*RspSendMessageSync)(nil)
	}

	y := proto.Clone(x).(*RspSendMessageSync)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *RspSendMessageSync) MaskInRpc() any {
	if x == nil {
		return (*RspSendMessageSync)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *RspSendMessageSync) MaskInBff() any {
	if x == nil {
		return (*RspSendMessageSync)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *RspSendMessagesSync) MaskInLog() any {
	if x == nil {
		return (*RspSendMessagesSync)(nil)
	}

	y := proto.Clone(x).(*RspSendMessagesSync)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *RspSendMessagesSync) MaskInRpc() any {
	if x == nil {
		return (*RspSendMessagesSync)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *RspSendMessagesSync) MaskInBff() any {
	if x == nil {
		return (*RspSendMessagesSync)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *ReqSendMessageWithoutSaveSync) MaskInLog() any {
	if x == nil {
		return (*ReqSendMessageWithoutSaveSync)(nil)
	}

	y := proto.Clone(x).(*ReqSendMessageWithoutSaveSync)
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}

	return y
}

func (x *ReqSendMessageWithoutSaveSync) MaskInRpc() any {
	if x == nil {
		return (*ReqSendMessageWithoutSaveSync)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}

	return y
}

func (x *ReqSendMessageWithoutSaveSync) MaskInBff() any {
	if x == nil {
		return (*ReqSendMessageWithoutSaveSync)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}

	return y
}

func (x *RspSendMessageWithoutSaveSync) MaskInLog() any {
	if x == nil {
		return (*RspSendMessageWithoutSaveSync)(nil)
	}

	y := proto.Clone(x).(*RspSendMessageWithoutSaveSync)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *RspSendMessageWithoutSaveSync) MaskInRpc() any {
	if x == nil {
		return (*RspSendMessageWithoutSaveSync)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *RspSendMessageWithoutSaveSync) MaskInBff() any {
	if x == nil {
		return (*RspSendMessageWithoutSaveSync)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *RspDescribeMessage) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessage)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessage)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*MessageConfig)
	}

	return y
}

func (x *RspDescribeMessage) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*MessageConfig)
	}

	return y
}

func (x *RspDescribeMessage) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*MessageConfig)
	}

	return y
}

func (x *RspDescribeMessageByQuestionId) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessageByQuestionId)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessageByQuestionId)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *RspDescribeMessageByQuestionId) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessageByQuestionId)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *RspDescribeMessageByQuestionId) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessageByQuestionId)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *ReqUpdateTextFileInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTextFileInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTextFileInBulk)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFileInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFileInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqUpdateTextFile)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateTextFile)
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := x
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}

	return y
}

func (x *ReqUpdateTextFile) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateTextFile)(nil)
	}

	y := x
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*DocAssistantState)
		}
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}

	return y
}

func (x *ReqDeleteDocInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqDeleteDocInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqDeleteDocInBulk)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ReqDeleteDocInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqDeleteDocInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ReqDeleteDocInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqDeleteDocInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ReqReparseTextFiles) MaskInLog() any {
	if x == nil {
		return (*ReqReparseTextFiles)(nil)
	}

	y := proto.Clone(x).(*ReqReparseTextFiles)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ReqReparseTextFiles) MaskInRpc() any {
	if x == nil {
		return (*ReqReparseTextFiles)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ReqReparseTextFiles) MaskInBff() any {
	if x == nil {
		return (*ReqReparseTextFiles)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ReqOnOffDocInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqOnOffDocInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqOnOffDocInBulk)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ReqOnOffDocInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqOnOffDocInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ReqOnOffDocInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqOnOffDocInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *RspOnOffDocInBulk) MaskInLog() any {
	if x == nil {
		return (*RspOnOffDocInBulk)(nil)
	}

	y := proto.Clone(x).(*RspOnOffDocInBulk)
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInLog().(*RspOnOffDocInBulk_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.RepeatCollections[k] = vv.MaskInLog().(*RspOnOffDocInBulk_RepeatCollection)
		}
	}
	for k, v := range y.ExceedQaContainsMatchLimit {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExceedQaContainsMatchLimit[k] = vv.MaskInLog().(*RspOnOffDocInBulk_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspOnOffDocInBulk) MaskInRpc() any {
	if x == nil {
		return (*RspOnOffDocInBulk)(nil)
	}

	y := x
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInRpc().(*RspOnOffDocInBulk_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.RepeatCollections[k] = vv.MaskInRpc().(*RspOnOffDocInBulk_RepeatCollection)
		}
	}
	for k, v := range y.ExceedQaContainsMatchLimit {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExceedQaContainsMatchLimit[k] = vv.MaskInRpc().(*RspOnOffDocInBulk_QaContainsMatchCount)
		}
	}

	return y
}

func (x *RspOnOffDocInBulk) MaskInBff() any {
	if x == nil {
		return (*RspOnOffDocInBulk)(nil)
	}

	y := x
	for k, v := range y.PreRepeatCollections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.PreRepeatCollections[k] = vv.MaskInBff().(*RspOnOffDocInBulk_RepeatCollection)
		}
	}
	for k, v := range y.RepeatCollections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.RepeatCollections[k] = vv.MaskInBff().(*RspOnOffDocInBulk_RepeatCollection)
		}
	}
	for k, v := range y.ExceedQaContainsMatchLimit {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExceedQaContainsMatchLimit[k] = vv.MaskInBff().(*RspOnOffDocInBulk_QaContainsMatchCount)
		}
	}

	return y
}

func (x *ReqCreateTextFileInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqCreateTextFileInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqCreateTextFileInBulk)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*TextFile)
		}
	}
	for k, v := range y.SharedAssistant {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedAssistant[k] = vv.MaskInLog().(*ReqCreateTextFileInBulk_Slice)
		}
	}

	return y
}

func (x *ReqCreateTextFileInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*TextFile)
		}
	}
	for k, v := range y.SharedAssistant {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedAssistant[k] = vv.MaskInRpc().(*ReqCreateTextFileInBulk_Slice)
		}
	}

	return y
}

func (x *ReqCreateTextFileInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqCreateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*TextFile)
		}
	}
	for k, v := range y.SharedAssistant {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedAssistant[k] = vv.MaskInBff().(*ReqCreateTextFileInBulk_Slice)
		}
	}

	return y
}

func (x *ReqListTextFile) MaskInLog() any {
	if x == nil {
		return (*ReqListTextFile)(nil)
	}

	y := proto.Clone(x).(*ReqListTextFile)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ContributorFilter)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UpdateBy[k] = vv.MaskInLog().(*OperatorFilter)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	if v, ok := any(y.Search).(interface{ MaskInLog() any }); ok {
		y.Search = v.MaskInLog().(*ReqListTextFile_Search)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.TenantCond).(interface{ MaskInLog() any }); ok {
		y.TenantCond = v.MaskInLog().(*ReqListTextFile_TenantCond)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*LabelFilter)
		}
	}
	if v, ok := any(y.SharedState).(interface{ MaskInLog() any }); ok {
		y.SharedState = v.MaskInLog().(*ListDocSharedFileter)
	}
	if v, ok := any(y.SharedReceivers).(interface{ MaskInLog() any }); ok {
		y.SharedReceivers = v.MaskInLog().(*DocSharedReceiverFilter)
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CreateBy[k] = vv.MaskInLog().(*OperatorFilter)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInLog() any }); ok {
		y.TipFilter = v.MaskInLog().(*ReqListTextFile_TipFilter)
	}

	return y
}

func (x *ReqListTextFile) MaskInRpc() any {
	if x == nil {
		return (*ReqListTextFile)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ContributorFilter)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UpdateBy[k] = vv.MaskInRpc().(*OperatorFilter)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	if v, ok := any(y.Search).(interface{ MaskInRpc() any }); ok {
		y.Search = v.MaskInRpc().(*ReqListTextFile_Search)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.TenantCond).(interface{ MaskInRpc() any }); ok {
		y.TenantCond = v.MaskInRpc().(*ReqListTextFile_TenantCond)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*LabelFilter)
		}
	}
	if v, ok := any(y.SharedState).(interface{ MaskInRpc() any }); ok {
		y.SharedState = v.MaskInRpc().(*ListDocSharedFileter)
	}
	if v, ok := any(y.SharedReceivers).(interface{ MaskInRpc() any }); ok {
		y.SharedReceivers = v.MaskInRpc().(*DocSharedReceiverFilter)
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CreateBy[k] = vv.MaskInRpc().(*OperatorFilter)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInRpc() any }); ok {
		y.TipFilter = v.MaskInRpc().(*ReqListTextFile_TipFilter)
	}

	return y
}

func (x *ReqListTextFile) MaskInBff() any {
	if x == nil {
		return (*ReqListTextFile)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ContributorFilter)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UpdateBy[k] = vv.MaskInBff().(*OperatorFilter)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	if v, ok := any(y.Search).(interface{ MaskInBff() any }); ok {
		y.Search = v.MaskInBff().(*ReqListTextFile_Search)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.TenantCond).(interface{ MaskInBff() any }); ok {
		y.TenantCond = v.MaskInBff().(*ReqListTextFile_TenantCond)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*LabelFilter)
		}
	}
	if v, ok := any(y.SharedState).(interface{ MaskInBff() any }); ok {
		y.SharedState = v.MaskInBff().(*ListDocSharedFileter)
	}
	if v, ok := any(y.SharedReceivers).(interface{ MaskInBff() any }); ok {
		y.SharedReceivers = v.MaskInBff().(*DocSharedReceiverFilter)
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CreateBy[k] = vv.MaskInBff().(*OperatorFilter)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInBff() any }); ok {
		y.TipFilter = v.MaskInBff().(*ReqListTextFile_TipFilter)
	}

	return y
}

func (x *ReqListTextFile_TenantCond) MaskInLog() any {
	if x == nil {
		return (*ReqListTextFile_TenantCond)(nil)
	}

	y := proto.Clone(x).(*ReqListTextFile_TenantCond)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ContributorFilter)
		}
	}

	return y
}

func (x *ReqListTextFile_TenantCond) MaskInRpc() any {
	if x == nil {
		return (*ReqListTextFile_TenantCond)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ContributorFilter)
		}
	}

	return y
}

func (x *ReqListTextFile_TenantCond) MaskInBff() any {
	if x == nil {
		return (*ReqListTextFile_TenantCond)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ContributorFilter)
		}
	}

	return y
}

func (x *ReqListTextFile_TipFilter) MaskInLog() any {
	if x == nil {
		return (*ReqListTextFile_TipFilter)(nil)
	}

	y := proto.Clone(x).(*ReqListTextFile_TipFilter)
	if v, ok := any(y.WarningGroup).(interface{ MaskInLog() any }); ok {
		y.WarningGroup = v.MaskInLog().(*ReqListTextFile_TipFilter_WarningGroup)
	}

	return y
}

func (x *ReqListTextFile_TipFilter) MaskInRpc() any {
	if x == nil {
		return (*ReqListTextFile_TipFilter)(nil)
	}

	y := x
	if v, ok := any(y.WarningGroup).(interface{ MaskInRpc() any }); ok {
		y.WarningGroup = v.MaskInRpc().(*ReqListTextFile_TipFilter_WarningGroup)
	}

	return y
}

func (x *ReqListTextFile_TipFilter) MaskInBff() any {
	if x == nil {
		return (*ReqListTextFile_TipFilter)(nil)
	}

	y := x
	if v, ok := any(y.WarningGroup).(interface{ MaskInBff() any }); ok {
		y.WarningGroup = v.MaskInBff().(*ReqListTextFile_TipFilter_WarningGroup)
	}

	return y
}

func (x *ListDocSharedFileter) MaskInLog() any {
	if x == nil {
		return (*ListDocSharedFileter)(nil)
	}

	y := proto.Clone(x).(*ListDocSharedFileter)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*ContributorFilter)
	}

	return y
}

func (x *ListDocSharedFileter) MaskInRpc() any {
	if x == nil {
		return (*ListDocSharedFileter)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*ContributorFilter)
	}

	return y
}

func (x *ListDocSharedFileter) MaskInBff() any {
	if x == nil {
		return (*ListDocSharedFileter)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*ContributorFilter)
	}

	return y
}

func (x *RspListTextFile) MaskInLog() any {
	if x == nil {
		return (*RspListTextFile)(nil)
	}

	y := proto.Clone(x).(*RspListTextFile)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*FullTextFile)
		}
	}

	return y
}

func (x *RspListTextFile) MaskInRpc() any {
	if x == nil {
		return (*RspListTextFile)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*FullTextFile)
		}
	}

	return y
}

func (x *RspListTextFile) MaskInBff() any {
	if x == nil {
		return (*RspListTextFile)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*FullTextFile)
		}
	}

	return y
}

func (x *ContributorFilter) MaskInLog() any {
	if x == nil {
		return (*ContributorFilter)(nil)
	}

	y := proto.Clone(x).(*ContributorFilter)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ContributorFilter) MaskInRpc() any {
	if x == nil {
		return (*ContributorFilter)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ContributorFilter) MaskInBff() any {
	if x == nil {
		return (*ContributorFilter)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *OperatorFilter) MaskInLog() any {
	if x == nil {
		return (*OperatorFilter)(nil)
	}

	y := proto.Clone(x).(*OperatorFilter)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *OperatorFilter) MaskInRpc() any {
	if x == nil {
		return (*OperatorFilter)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *OperatorFilter) MaskInBff() any {
	if x == nil {
		return (*OperatorFilter)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ReqListQA) MaskInLog() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := proto.Clone(x).(*ReqListQA)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ContributorFilter)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UpdateBy[k] = vv.MaskInLog().(*OperatorFilter)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	if v, ok := any(y.Search).(interface{ MaskInLog() any }); ok {
		y.Search = v.MaskInLog().(*ReqListQA_Search)
	}
	if v, ok := any(y.TenantCond).(interface{ MaskInLog() any }); ok {
		y.TenantCond = v.MaskInLog().(*ReqListQA_TenantCond)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*LabelFilter)
		}
	}
	if v, ok := any(y.SharedState).(interface{ MaskInLog() any }); ok {
		y.SharedState = v.MaskInLog().(*ListDocSharedFileter)
	}
	if v, ok := any(y.SharedReceivers).(interface{ MaskInLog() any }); ok {
		y.SharedReceivers = v.MaskInLog().(*DocSharedReceiverFilter)
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.CreateBy[k] = vv.MaskInLog().(*OperatorFilter)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInLog() any }); ok {
		y.TipFilter = v.MaskInLog().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA) MaskInRpc() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ContributorFilter)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UpdateBy[k] = vv.MaskInRpc().(*OperatorFilter)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	if v, ok := any(y.Search).(interface{ MaskInRpc() any }); ok {
		y.Search = v.MaskInRpc().(*ReqListQA_Search)
	}
	if v, ok := any(y.TenantCond).(interface{ MaskInRpc() any }); ok {
		y.TenantCond = v.MaskInRpc().(*ReqListQA_TenantCond)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*LabelFilter)
		}
	}
	if v, ok := any(y.SharedState).(interface{ MaskInRpc() any }); ok {
		y.SharedState = v.MaskInRpc().(*ListDocSharedFileter)
	}
	if v, ok := any(y.SharedReceivers).(interface{ MaskInRpc() any }); ok {
		y.SharedReceivers = v.MaskInRpc().(*DocSharedReceiverFilter)
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.CreateBy[k] = vv.MaskInRpc().(*OperatorFilter)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInRpc() any }); ok {
		y.TipFilter = v.MaskInRpc().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA) MaskInBff() any {
	if x == nil {
		return (*ReqListQA)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ContributorFilter)
		}
	}
	for k, v := range y.UpdateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UpdateBy[k] = vv.MaskInBff().(*OperatorFilter)
		}
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	if v, ok := any(y.Search).(interface{ MaskInBff() any }); ok {
		y.Search = v.MaskInBff().(*ReqListQA_Search)
	}
	if v, ok := any(y.TenantCond).(interface{ MaskInBff() any }); ok {
		y.TenantCond = v.MaskInBff().(*ReqListQA_TenantCond)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*LabelFilter)
		}
	}
	if v, ok := any(y.SharedState).(interface{ MaskInBff() any }); ok {
		y.SharedState = v.MaskInBff().(*ListDocSharedFileter)
	}
	if v, ok := any(y.SharedReceivers).(interface{ MaskInBff() any }); ok {
		y.SharedReceivers = v.MaskInBff().(*DocSharedReceiverFilter)
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*OrderByLabel)
	}
	for k, v := range y.CreateBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.CreateBy[k] = vv.MaskInBff().(*OperatorFilter)
		}
	}
	if v, ok := any(y.TipFilter).(interface{ MaskInBff() any }); ok {
		y.TipFilter = v.MaskInBff().(*ReqListQA_TipFilter)
	}

	return y
}

func (x *ReqListQA_TenantCond) MaskInLog() any {
	if x == nil {
		return (*ReqListQA_TenantCond)(nil)
	}

	y := proto.Clone(x).(*ReqListQA_TenantCond)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*ContributorFilter)
		}
	}

	return y
}

func (x *ReqListQA_TenantCond) MaskInRpc() any {
	if x == nil {
		return (*ReqListQA_TenantCond)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*ContributorFilter)
		}
	}

	return y
}

func (x *ReqListQA_TenantCond) MaskInBff() any {
	if x == nil {
		return (*ReqListQA_TenantCond)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*ContributorFilter)
		}
	}

	return y
}

func (x *ReqListQA_TipFilter) MaskInLog() any {
	if x == nil {
		return (*ReqListQA_TipFilter)(nil)
	}

	y := proto.Clone(x).(*ReqListQA_TipFilter)
	if v, ok := any(y.WarningGroup).(interface{ MaskInLog() any }); ok {
		y.WarningGroup = v.MaskInLog().(*ReqListQA_TipFilter_WarningGroup)
	}

	return y
}

func (x *ReqListQA_TipFilter) MaskInRpc() any {
	if x == nil {
		return (*ReqListQA_TipFilter)(nil)
	}

	y := x
	if v, ok := any(y.WarningGroup).(interface{ MaskInRpc() any }); ok {
		y.WarningGroup = v.MaskInRpc().(*ReqListQA_TipFilter_WarningGroup)
	}

	return y
}

func (x *ReqListQA_TipFilter) MaskInBff() any {
	if x == nil {
		return (*ReqListQA_TipFilter)(nil)
	}

	y := x
	if v, ok := any(y.WarningGroup).(interface{ MaskInBff() any }); ok {
		y.WarningGroup = v.MaskInBff().(*ReqListQA_TipFilter_WarningGroup)
	}

	return y
}

func (x *RspListQA) MaskInLog() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := proto.Clone(x).(*RspListQA)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*QA)
		}
	}

	return y
}

func (x *RspListQA) MaskInRpc() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*QA)
		}
	}

	return y
}

func (x *RspListQA) MaskInBff() any {
	if x == nil {
		return (*RspListQA)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*QA)
		}
	}

	return y
}

func (x *ReqCreateQA) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQA)
	if v, ok := any(y.Item).(interface{ MaskInLog() any }); ok {
		y.Item = v.MaskInLog().(*QA)
	}

	return y
}

func (x *ReqCreateQA) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := x
	if v, ok := any(y.Item).(interface{ MaskInRpc() any }); ok {
		y.Item = v.MaskInRpc().(*QA)
	}

	return y
}

func (x *ReqCreateQA) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQA)(nil)
	}

	y := x
	if v, ok := any(y.Item).(interface{ MaskInBff() any }); ok {
		y.Item = v.MaskInBff().(*QA)
	}

	return y
}

func (x *ReqCreateQAInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQAInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQAInBulk)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*QA)
		}
	}
	for k, v := range y.SharedAssistant {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SharedAssistant[k] = vv.MaskInLog().(*ReqCreateQAInBulk_Slice)
		}
	}

	return y
}

func (x *ReqCreateQAInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*QA)
		}
	}
	for k, v := range y.SharedAssistant {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SharedAssistant[k] = vv.MaskInRpc().(*ReqCreateQAInBulk_Slice)
		}
	}

	return y
}

func (x *ReqCreateQAInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*QA)
		}
	}
	for k, v := range y.SharedAssistant {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SharedAssistant[k] = vv.MaskInBff().(*ReqCreateQAInBulk_Slice)
		}
	}

	return y
}

func (x *ReqUpdateChatMessage) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateChatMessage)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateChatMessage)
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInLog() any }); ok {
		y.CollectionSnapshot = v.MaskInLog().(*MessageCollectionSnapshot)
	}

	return y
}

func (x *ReqUpdateChatMessage) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInRpc() any }); ok {
		y.CollectionSnapshot = v.MaskInRpc().(*MessageCollectionSnapshot)
	}

	return y
}

func (x *ReqUpdateChatMessage) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateChatMessage)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInBff() any }); ok {
		y.CollectionSnapshot = v.MaskInBff().(*MessageCollectionSnapshot)
	}

	return y
}

func (x *ReqUpdateQA) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateQA)
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.States[k] = vv.MaskInLog().(*DocAssistantState)
		}
	}
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateQA) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.States[k] = vv.MaskInRpc().(*DocAssistantState)
		}
	}
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateQA) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateQA)(nil)
	}

	y := x
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}
	for k, v := range y.States {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.States[k] = vv.MaskInBff().(*DocAssistantState)
		}
	}
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateQAInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateQAInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateQAInBulk)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateQAInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *ReqUpdateQAInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqUpdateQA)
		}
	}

	return y
}

func (x *RspDescribeUserChats) MaskInLog() any {
	if x == nil {
		return (*RspDescribeUserChats)(nil)
	}

	y := proto.Clone(x).(*RspDescribeUserChats)
	for k, v := range y.Chat {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chat[k] = vv.MaskInLog().(*Chat)
		}
	}

	return y
}

func (x *RspDescribeUserChats) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeUserChats)(nil)
	}

	y := x
	for k, v := range y.Chat {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chat[k] = vv.MaskInRpc().(*Chat)
		}
	}

	return y
}

func (x *RspDescribeUserChats) MaskInBff() any {
	if x == nil {
		return (*RspDescribeUserChats)(nil)
	}

	y := x
	for k, v := range y.Chat {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chat[k] = vv.MaskInBff().(*Chat)
		}
	}

	return y
}

func (x *RspSearchCollectionOneShot) MaskInLog() any {
	if x == nil {
		return (*RspSearchCollectionOneShot)(nil)
	}

	y := proto.Clone(x).(*RspSearchCollectionOneShot)
	for k, v := range y.SearchItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SearchItems[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}
	for k, v := range y.SearchTextItems {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SearchTextItems[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspSearchCollectionOneShot) MaskInRpc() any {
	if x == nil {
		return (*RspSearchCollectionOneShot)(nil)
	}

	y := x
	for k, v := range y.SearchItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SearchItems[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}
	for k, v := range y.SearchTextItems {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SearchTextItems[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspSearchCollectionOneShot) MaskInBff() any {
	if x == nil {
		return (*RspSearchCollectionOneShot)(nil)
	}

	y := x
	for k, v := range y.SearchItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SearchItems[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}
	for k, v := range y.SearchTextItems {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SearchTextItems[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspSearchCollection) MaskInLog() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := proto.Clone(x).(*RspSearchCollection)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspSearchCollection) MaskInRpc() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspSearchCollection) MaskInBff() any {
	if x == nil {
		return (*RspSearchCollection)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspListCollection) MaskInLog() any {
	if x == nil {
		return (*RspListCollection)(nil)
	}

	y := proto.Clone(x).(*RspListCollection)
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Collections[k] = vv.MaskInLog().(*Collection)
		}
	}

	return y
}

func (x *RspListCollection) MaskInRpc() any {
	if x == nil {
		return (*RspListCollection)(nil)
	}

	y := x
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Collections[k] = vv.MaskInRpc().(*Collection)
		}
	}

	return y
}

func (x *RspListCollection) MaskInBff() any {
	if x == nil {
		return (*RspListCollection)(nil)
	}

	y := x
	for k, v := range y.Collections {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Collections[k] = vv.MaskInBff().(*Collection)
		}
	}

	return y
}

func (x *ReqListContributor) MaskInLog() any {
	if x == nil {
		return (*ReqListContributor)(nil)
	}

	y := proto.Clone(x).(*ReqListContributor)
	if v, ok := any(y.Or).(interface{ MaskInLog() any }); ok {
		y.Or = v.MaskInLog().(*ReqListContributor_OrGroup)
	}

	return y
}

func (x *ReqListContributor) MaskInRpc() any {
	if x == nil {
		return (*ReqListContributor)(nil)
	}

	y := x
	if v, ok := any(y.Or).(interface{ MaskInRpc() any }); ok {
		y.Or = v.MaskInRpc().(*ReqListContributor_OrGroup)
	}

	return y
}

func (x *ReqListContributor) MaskInBff() any {
	if x == nil {
		return (*ReqListContributor)(nil)
	}

	y := x
	if v, ok := any(y.Or).(interface{ MaskInBff() any }); ok {
		y.Or = v.MaskInBff().(*ReqListContributor_OrGroup)
	}

	return y
}

func (x *ReqListContributor_OrGroup) MaskInLog() any {
	if x == nil {
		return (*ReqListContributor_OrGroup)(nil)
	}

	y := proto.Clone(x).(*ReqListContributor_OrGroup)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqListContributor_OrGroup) MaskInRpc() any {
	if x == nil {
		return (*ReqListContributor_OrGroup)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqListContributor_OrGroup) MaskInBff() any {
	if x == nil {
		return (*ReqListContributor_OrGroup)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *RspListContributor) MaskInLog() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := proto.Clone(x).(*RspListContributor)
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*Contributor)
		}
	}
	for k, v := range y.ContributorMap {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ContributorMap[k] = vv.MaskInLog().(*RspListContributorSlice)
		}
	}

	return y
}

func (x *RspListContributor) MaskInRpc() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	for k, v := range y.ContributorMap {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ContributorMap[k] = vv.MaskInRpc().(*RspListContributorSlice)
		}
	}

	return y
}

func (x *RspListContributor) MaskInBff() any {
	if x == nil {
		return (*RspListContributor)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*Contributor)
		}
	}
	for k, v := range y.ContributorMap {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ContributorMap[k] = vv.MaskInBff().(*RspListContributorSlice)
		}
	}

	return y
}

func (x *RspListContributorSlice) MaskInLog() any {
	if x == nil {
		return (*RspListContributorSlice)(nil)
	}

	y := proto.Clone(x).(*RspListContributorSlice)
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributors[k] = vv.MaskInLog().(*Contributor)
		}
	}

	return y
}

func (x *RspListContributorSlice) MaskInRpc() any {
	if x == nil {
		return (*RspListContributorSlice)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributors[k] = vv.MaskInRpc().(*Contributor)
		}
	}

	return y
}

func (x *RspListContributorSlice) MaskInBff() any {
	if x == nil {
		return (*RspListContributorSlice)(nil)
	}

	y := x
	for k, v := range y.Contributors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributors[k] = vv.MaskInBff().(*Contributor)
		}
	}

	return y
}

func (x *ReqListUpdateBy) MaskInLog() any {
	if x == nil {
		return (*ReqListUpdateBy)(nil)
	}

	y := proto.Clone(x).(*ReqListUpdateBy)
	if v, ok := any(y.Or).(interface{ MaskInLog() any }); ok {
		y.Or = v.MaskInLog().(*ReqListUpdateBy_OrGroup)
	}

	return y
}

func (x *ReqListUpdateBy) MaskInRpc() any {
	if x == nil {
		return (*ReqListUpdateBy)(nil)
	}

	y := x
	if v, ok := any(y.Or).(interface{ MaskInRpc() any }); ok {
		y.Or = v.MaskInRpc().(*ReqListUpdateBy_OrGroup)
	}

	return y
}

func (x *ReqListUpdateBy) MaskInBff() any {
	if x == nil {
		return (*ReqListUpdateBy)(nil)
	}

	y := x
	if v, ok := any(y.Or).(interface{ MaskInBff() any }); ok {
		y.Or = v.MaskInBff().(*ReqListUpdateBy_OrGroup)
	}

	return y
}

func (x *ReqListUpdateBy_OrGroup) MaskInLog() any {
	if x == nil {
		return (*ReqListUpdateBy_OrGroup)(nil)
	}

	y := proto.Clone(x).(*ReqListUpdateBy_OrGroup)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqListUpdateBy_OrGroup) MaskInRpc() any {
	if x == nil {
		return (*ReqListUpdateBy_OrGroup)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqListUpdateBy_OrGroup) MaskInBff() any {
	if x == nil {
		return (*ReqListUpdateBy_OrGroup)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *RspListUpdateBy) MaskInLog() any {
	if x == nil {
		return (*RspListUpdateBy)(nil)
	}

	y := proto.Clone(x).(*RspListUpdateBy)
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Operators[k] = vv.MaskInLog().(*Operator)
		}
	}

	return y
}

func (x *RspListUpdateBy) MaskInRpc() any {
	if x == nil {
		return (*RspListUpdateBy)(nil)
	}

	y := x
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Operators[k] = vv.MaskInRpc().(*Operator)
		}
	}

	return y
}

func (x *RspListUpdateBy) MaskInBff() any {
	if x == nil {
		return (*RspListUpdateBy)(nil)
	}

	y := x
	for k, v := range y.Operators {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Operators[k] = vv.MaskInBff().(*Operator)
		}
	}

	return y
}

func (x *ReqListSharedAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqListSharedAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqListSharedAssistant)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqListSharedAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqListSharedAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqListSharedAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqListSharedAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *RspListSharedAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListSharedAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListSharedAssistant)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*Assistant)
		}
	}

	return y
}

func (x *RspListSharedAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListSharedAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*Assistant)
		}
	}

	return y
}

func (x *RspListSharedAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListSharedAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*Assistant)
		}
	}

	return y
}

func (x *ReqValidateQAInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqValidateQAInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqValidateQAInBulk)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*QA)
		}
	}
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqValidateQAInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*QA)
		}
	}
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqValidateQAInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqValidateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*QA)
		}
	}
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *RspValidateQAInBulk) MaskInLog() any {
	if x == nil {
		return (*RspValidateQAInBulk)(nil)
	}

	y := proto.Clone(x).(*RspValidateQAInBulk)
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Errors[k] = vv.MaskInLog().(*RspValidateQAInBulk_Item)
		}
	}

	return y
}

func (x *RspValidateQAInBulk) MaskInRpc() any {
	if x == nil {
		return (*RspValidateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Errors[k] = vv.MaskInRpc().(*RspValidateQAInBulk_Item)
		}
	}

	return y
}

func (x *RspValidateQAInBulk) MaskInBff() any {
	if x == nil {
		return (*RspValidateQAInBulk)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Errors[k] = vv.MaskInBff().(*RspValidateQAInBulk_Item)
		}
	}

	return y
}

func (x *ReqValidateTextFileInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqValidateTextFileInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqValidateTextFileInBulk)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*TextFile)
		}
	}
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqValidateTextFileInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqValidateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*TextFile)
		}
	}
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqValidateTextFileInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqValidateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*TextFile)
		}
	}
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *RspValidateTextFileInBulk) MaskInLog() any {
	if x == nil {
		return (*RspValidateTextFileInBulk)(nil)
	}

	y := proto.Clone(x).(*RspValidateTextFileInBulk)
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Errors[k] = vv.MaskInLog().(*RspValidateTextFileInBulk_Item)
		}
	}

	return y
}

func (x *RspValidateTextFileInBulk) MaskInRpc() any {
	if x == nil {
		return (*RspValidateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Errors[k] = vv.MaskInRpc().(*RspValidateTextFileInBulk_Item)
		}
	}

	return y
}

func (x *RspValidateTextFileInBulk) MaskInBff() any {
	if x == nil {
		return (*RspValidateTextFileInBulk)(nil)
	}

	y := x
	for k, v := range y.Errors {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Errors[k] = vv.MaskInBff().(*RspValidateTextFileInBulk_Item)
		}
	}

	return y
}

func (x *ReqCloneDocInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqCloneDocInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqCloneDocInBulk)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ReqCloneDocInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqCloneDocInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ReqCloneDocInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqCloneDocInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ReqCreateSystemDocCopy) MaskInLog() any {
	if x == nil {
		return (*ReqCreateSystemDocCopy)(nil)
	}

	y := proto.Clone(x).(*ReqCreateSystemDocCopy)
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqCreateSystemDocCopy) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateSystemDocCopy)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqCreateSystemDocCopy) MaskInBff() any {
	if x == nil {
		return (*ReqCreateSystemDocCopy)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqEnableSystemDoc) MaskInLog() any {
	if x == nil {
		return (*ReqEnableSystemDoc)(nil)
	}

	y := proto.Clone(x).(*ReqEnableSystemDoc)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqEnableSystemDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqEnableSystemDoc)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqEnableSystemDoc) MaskInBff() any {
	if x == nil {
		return (*ReqEnableSystemDoc)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqDisableSystemDoc) MaskInLog() any {
	if x == nil {
		return (*ReqDisableSystemDoc)(nil)
	}

	y := proto.Clone(x).(*ReqDisableSystemDoc)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqDisableSystemDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqDisableSystemDoc)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqDisableSystemDoc) MaskInBff() any {
	if x == nil {
		return (*ReqDisableSystemDoc)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqDeleteSystemDoc) MaskInLog() any {
	if x == nil {
		return (*ReqDeleteSystemDoc)(nil)
	}

	y := proto.Clone(x).(*ReqDeleteSystemDoc)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqDeleteSystemDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqDeleteSystemDoc)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqDeleteSystemDoc) MaskInBff() any {
	if x == nil {
		return (*ReqDeleteSystemDoc)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertUserFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertUserFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertUserFeedback)
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.References[k] = vv.MaskInLog().(*TypedReference)
		}
	}
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertUserFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertUserFeedback)(nil)
	}

	y := x
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.References[k] = vv.MaskInRpc().(*TypedReference)
		}
	}
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertUserFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertUserFeedback)(nil)
	}

	y := x
	for k, v := range y.References {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.References[k] = vv.MaskInBff().(*TypedReference)
		}
	}
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertOpFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertOpFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertOpFeedback)
	if v, ok := any(y.OpComment).(interface{ MaskInLog() any }); ok {
		y.OpComment = v.MaskInLog().(*FeedbackComment)
	}
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertOpFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertOpFeedback)(nil)
	}

	y := x
	if v, ok := any(y.OpComment).(interface{ MaskInRpc() any }); ok {
		y.OpComment = v.MaskInRpc().(*FeedbackComment)
	}
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertOpFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertOpFeedback)(nil)
	}

	y := x
	if v, ok := any(y.OpComment).(interface{ MaskInBff() any }); ok {
		y.OpComment = v.MaskInBff().(*FeedbackComment)
	}
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertMgmtFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertMgmtFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertMgmtFeedback)
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInLog() any }); ok {
		y.MgmtFeedback = v.MaskInLog().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInLog() any }); ok {
		y.MgmtComment = v.MaskInLog().(*FeedbackComment)
	}
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertMgmtFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertMgmtFeedback)(nil)
	}

	y := x
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInRpc() any }); ok {
		y.MgmtFeedback = v.MaskInRpc().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInRpc() any }); ok {
		y.MgmtComment = v.MaskInRpc().(*FeedbackComment)
	}
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqUpsertMgmtFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertMgmtFeedback)(nil)
	}

	y := x
	if v, ok := any(y.MgmtFeedback).(interface{ MaskInBff() any }); ok {
		y.MgmtFeedback = v.MaskInBff().(*FeedbackComment)
	}
	if v, ok := any(y.MgmtComment).(interface{ MaskInBff() any }); ok {
		y.MgmtComment = v.MaskInBff().(*FeedbackComment)
	}
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbacks)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetFeedbacks_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInLog() any }); ok {
		y.Relation = v.MaskInLog().(*ReqGetFeedbacks_Relation)
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetFeedbacks_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInRpc() any }); ok {
		y.Relation = v.MaskInRpc().(*ReqGetFeedbacks_Relation)
	}

	return y
}

func (x *ReqGetFeedbacks) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbacks)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetFeedbacks_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInBff() any }); ok {
		y.Relation = v.MaskInBff().(*ReqGetFeedbacks_Relation)
	}

	return y
}

func (x *ReqGetFeedbacks_Filter) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbacks_Filter)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbacks_Filter)
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetFeedbacks_Filter) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbacks_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetFeedbacks_Filter) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbacks_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetFeedbacks) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbacks)
	for k, v := range y.Feedbacks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Feedbacks[k] = vv.MaskInLog().(*FullFeedback)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := x
	for k, v := range y.Feedbacks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Feedbacks[k] = vv.MaskInRpc().(*FullFeedback)
		}
	}

	return y
}

func (x *RspGetFeedbacks) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbacks)(nil)
	}

	y := x
	for k, v := range y.Feedbacks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Feedbacks[k] = vv.MaskInBff().(*FullFeedback)
		}
	}

	return y
}

func (x *ReqAcceptFeedback) MaskInLog() any {
	if x == nil {
		return (*ReqAcceptFeedback)(nil)
	}

	y := proto.Clone(x).(*ReqAcceptFeedback)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqAcceptFeedback) MaskInRpc() any {
	if x == nil {
		return (*ReqAcceptFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqAcceptFeedback) MaskInBff() any {
	if x == nil {
		return (*ReqAcceptFeedback)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbackLogs)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetFeedbackLogs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInLog() any }); ok {
		y.Relation = v.MaskInLog().(*ReqGetFeedbackLogs_Relation)
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetFeedbackLogs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInRpc() any }); ok {
		y.Relation = v.MaskInRpc().(*ReqGetFeedbackLogs_Relation)
	}

	return y
}

func (x *ReqGetFeedbackLogs) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbackLogs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetFeedbackLogs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInBff() any }); ok {
		y.Relation = v.MaskInBff().(*ReqGetFeedbackLogs_Relation)
	}

	return y
}

func (x *ReqGetFeedbackLogs_Filter) MaskInLog() any {
	if x == nil {
		return (*ReqGetFeedbackLogs_Filter)(nil)
	}

	y := proto.Clone(x).(*ReqGetFeedbackLogs_Filter)
	if v, ok := any(y.CreateIdentity).(interface{ MaskInLog() any }); ok {
		y.CreateIdentity = v.MaskInLog().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetFeedbackLogs_Filter) MaskInRpc() any {
	if x == nil {
		return (*ReqGetFeedbackLogs_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInRpc() any }); ok {
		y.CreateIdentity = v.MaskInRpc().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetFeedbackLogs_Filter) MaskInBff() any {
	if x == nil {
		return (*ReqGetFeedbackLogs_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateIdentity).(interface{ MaskInBff() any }); ok {
		y.CreateIdentity = v.MaskInBff().(*base.Identity)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInLog() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := proto.Clone(x).(*RspGetFeedbackLogs)
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*FullFeedbackLog)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInRpc() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*FullFeedbackLog)
		}
	}

	return y
}

func (x *RspGetFeedbackLogs) MaskInBff() any {
	if x == nil {
		return (*RspGetFeedbackLogs)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*FullFeedbackLog)
		}
	}

	return y
}

func (x *RspGetDocs) MaskInLog() any {
	if x == nil {
		return (*RspGetDocs)(nil)
	}

	y := proto.Clone(x).(*RspGetDocs)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspGetDocs) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocs)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspGetDocs) MaskInBff() any {
	if x == nil {
		return (*RspGetDocs)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ReqListChat) MaskInLog() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := proto.Clone(x).(*ReqListChat)
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInLog() any }); ok {
		y.CreateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInLog() any }); ok {
		y.UpdateDateRange = v.MaskInLog().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInLog() any }); ok {
		y.OrderByLabel = v.MaskInLog().(*OrderByLabel)
	}

	return y
}

func (x *ReqListChat) MaskInRpc() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInRpc() any }); ok {
		y.CreateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInRpc() any }); ok {
		y.UpdateDateRange = v.MaskInRpc().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInRpc() any }); ok {
		y.OrderByLabel = v.MaskInRpc().(*OrderByLabel)
	}

	return y
}

func (x *ReqListChat) MaskInBff() any {
	if x == nil {
		return (*ReqListChat)(nil)
	}

	y := x
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqListChat_Filter)
	}
	if v, ok := any(y.CreateDateRange).(interface{ MaskInBff() any }); ok {
		y.CreateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	if v, ok := any(y.UpdateDateRange).(interface{ MaskInBff() any }); ok {
		y.UpdateDateRange = v.MaskInBff().(*base.TimeRange)
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*LabelFilter)
		}
	}
	if v, ok := any(y.OrderByLabel).(interface{ MaskInBff() any }); ok {
		y.OrderByLabel = v.MaskInBff().(*OrderByLabel)
	}

	return y
}

func (x *RspListChat) MaskInLog() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := proto.Clone(x).(*RspListChat)
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chats[k] = vv.MaskInLog().(*Chat)
		}
	}

	return y
}

func (x *RspListChat) MaskInRpc() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chats[k] = vv.MaskInRpc().(*Chat)
		}
	}

	return y
}

func (x *RspListChat) MaskInBff() any {
	if x == nil {
		return (*RspListChat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chats[k] = vv.MaskInBff().(*Chat)
		}
	}

	return y
}

func (x *RspGetChatDetail) MaskInLog() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := proto.Clone(x).(*RspGetChatDetail)
	if v, ok := any(y.ChatDetail).(interface{ MaskInLog() any }); ok {
		y.ChatDetail = v.MaskInLog().(*ChatDetail)
	}

	return y
}

func (x *RspGetChatDetail) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.ChatDetail).(interface{ MaskInRpc() any }); ok {
		y.ChatDetail = v.MaskInRpc().(*ChatDetail)
	}

	return y
}

func (x *RspGetChatDetail) MaskInBff() any {
	if x == nil {
		return (*RspGetChatDetail)(nil)
	}

	y := x
	if v, ok := any(y.ChatDetail).(interface{ MaskInBff() any }); ok {
		y.ChatDetail = v.MaskInBff().(*ChatDetail)
	}

	return y
}

func (x *RspDescribeMessageDocs) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessageDocs)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessageDocs)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageDocs) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessageDocs)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageDocs) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessageDocs)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ReqDescribeMessageLog) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeMessageLog)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeMessageLog)
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}

	return y
}

func (x *ReqDescribeMessageLog) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeMessageLog)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}

	return y
}

func (x *ReqDescribeMessageLog) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeMessageLog)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}

	return y
}

func (x *RspDescribeMessageLog) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessageLog)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessageLog)
	for k, v := range y.MessageLogs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.MessageLogs[k] = vv.MaskInLog().(*ChatMessageLog)
		}
	}

	return y
}

func (x *RspDescribeMessageLog) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessageLog)(nil)
	}

	y := x
	for k, v := range y.MessageLogs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.MessageLogs[k] = vv.MaskInRpc().(*ChatMessageLog)
		}
	}

	return y
}

func (x *RspDescribeMessageLog) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessageLog)(nil)
	}

	y := x
	for k, v := range y.MessageLogs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.MessageLogs[k] = vv.MaskInBff().(*ChatMessageLog)
		}
	}

	return y
}

func (x *RspDescribeSuggestLog) MaskInLog() any {
	if x == nil {
		return (*RspDescribeSuggestLog)(nil)
	}

	y := proto.Clone(x).(*RspDescribeSuggestLog)
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*ChatSuggestLog)
		}
	}

	return y
}

func (x *RspDescribeSuggestLog) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeSuggestLog)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*ChatSuggestLog)
		}
	}

	return y
}

func (x *RspDescribeSuggestLog) MaskInBff() any {
	if x == nil {
		return (*RspDescribeSuggestLog)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*ChatSuggestLog)
		}
	}

	return y
}

func (x *RspListDocByRef) MaskInLog() any {
	if x == nil {
		return (*RspListDocByRef)(nil)
	}

	y := proto.Clone(x).(*RspListDocByRef)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*RspListDocByRef_Doc)
		}
	}

	return y
}

func (x *RspListDocByRef) MaskInRpc() any {
	if x == nil {
		return (*RspListDocByRef)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*RspListDocByRef_Doc)
		}
	}

	return y
}

func (x *RspListDocByRef) MaskInBff() any {
	if x == nil {
		return (*RspListDocByRef)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*RspListDocByRef_Doc)
		}
	}

	return y
}

func (x *RspListDocByRef_Doc) MaskInLog() any {
	if x == nil {
		return (*RspListDocByRef_Doc)(nil)
	}

	y := proto.Clone(x).(*RspListDocByRef_Doc)
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *RspListDocByRef_Doc) MaskInRpc() any {
	if x == nil {
		return (*RspListDocByRef_Doc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *RspListDocByRef_Doc) MaskInBff() any {
	if x == nil {
		return (*RspListDocByRef_Doc)(nil)
	}

	y := x
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ReqListCustomLabel) MaskInLog() any {
	if x == nil {
		return (*ReqListCustomLabel)(nil)
	}

	y := proto.Clone(x).(*ReqListCustomLabel)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqListCustomLabel) MaskInRpc() any {
	if x == nil {
		return (*ReqListCustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqListCustomLabel) MaskInBff() any {
	if x == nil {
		return (*ReqListCustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListAssistantCanShareDoc)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListAssistantCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListAssistantCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*RspListAssistantCanShareDoc_SharedAssistant)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListTeamCanShareDoc)
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Teams[k] = vv.MaskInLog().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Teams[k] = vv.MaskInRpc().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListTeamCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListTeamCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Teams {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Teams[k] = vv.MaskInBff().(*RspListTeamCanShareDoc_Teams)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInLog() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := proto.Clone(x).(*RspListUserCanShareDoc)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInRpc() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *RspListUserCanShareDoc) MaskInBff() any {
	if x == nil {
		return (*RspListUserCanShareDoc)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*RspListUserCanShareDoc_Users)
		}
	}

	return y
}

func (x *ReqListSharedTeam) MaskInLog() any {
	if x == nil {
		return (*ReqListSharedTeam)(nil)
	}

	y := proto.Clone(x).(*ReqListSharedTeam)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqListSharedTeam) MaskInRpc() any {
	if x == nil {
		return (*ReqListSharedTeam)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqListSharedTeam) MaskInBff() any {
	if x == nil {
		return (*ReqListSharedTeam)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *ReqListSharedUser) MaskInLog() any {
	if x == nil {
		return (*ReqListSharedUser)(nil)
	}

	y := proto.Clone(x).(*ReqListSharedUser)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqListSharedUser) MaskInRpc() any {
	if x == nil {
		return (*ReqListSharedUser)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqListSharedUser) MaskInBff() any {
	if x == nil {
		return (*ReqListSharedUser)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocShareConfigReceiverAssistant)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*DocReceiveConfigBySender)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*DocReceiveConfigBySender)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*DocReceiveConfigBySender)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverUserTeam) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverUserTeam)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocShareConfigReceiverUserTeam)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*DocReceiveConfigBySender)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverUserTeam) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*DocReceiveConfigBySender)
		}
	}

	return y
}

func (x *ReqCreateDocShareConfigReceiverUserTeam) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*DocReceiveConfigBySender)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverAssistant)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverAssistant)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverAssistant_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInLog() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := proto.Clone(x).(*RspListDocShareConfigReceiverUserTeam)
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserShares[k] = vv.MaskInLog().(*RspListDocShareConfigReceiverUserTeam_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInRpc() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserShares[k] = vv.MaskInRpc().(*RspListDocShareConfigReceiverUserTeam_UserShare)
		}
	}

	return y
}

func (x *RspListDocShareConfigReceiverUserTeam) MaskInBff() any {
	if x == nil {
		return (*RspListDocShareConfigReceiverUserTeam)(nil)
	}

	y := x
	for k, v := range y.UserShares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserShares[k] = vv.MaskInBff().(*RspListDocShareConfigReceiverUserTeam_UserShare)
		}
	}

	return y
}

func (x *ReqCreateDocShare) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocShare)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocShare)
	if v, ok := any(y.Operator).(interface{ MaskInLog() any }); ok {
		y.Operator = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ReqCreateDocShare) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocShare)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInRpc() any }); ok {
		y.Operator = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ReqCreateDocShare) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocShare)(nil)
	}

	y := x
	if v, ok := any(y.Operator).(interface{ MaskInBff() any }); ok {
		y.Operator = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *RspListExternalSourceUser) MaskInLog() any {
	if x == nil {
		return (*RspListExternalSourceUser)(nil)
	}

	y := proto.Clone(x).(*RspListExternalSourceUser)
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Users[k] = vv.MaskInLog().(*ExternalSourceUser)
		}
	}

	return y
}

func (x *RspListExternalSourceUser) MaskInRpc() any {
	if x == nil {
		return (*RspListExternalSourceUser)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Users[k] = vv.MaskInRpc().(*ExternalSourceUser)
		}
	}

	return y
}

func (x *RspListExternalSourceUser) MaskInBff() any {
	if x == nil {
		return (*RspListExternalSourceUser)(nil)
	}

	y := x
	for k, v := range y.Users {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Users[k] = vv.MaskInBff().(*ExternalSourceUser)
		}
	}

	return y
}

func (x *RspDescribeTencentDocList) MaskInLog() any {
	if x == nil {
		return (*RspDescribeTencentDocList)(nil)
	}

	y := proto.Clone(x).(*RspDescribeTencentDocList)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*TencentDoc)
		}
	}

	return y
}

func (x *RspDescribeTencentDocList) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeTencentDocList)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*TencentDoc)
		}
	}

	return y
}

func (x *RspDescribeTencentDocList) MaskInBff() any {
	if x == nil {
		return (*RspDescribeTencentDocList)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*TencentDoc)
		}
	}

	return y
}

func (x *RspReimportTencentDoc) MaskInLog() any {
	if x == nil {
		return (*RspReimportTencentDoc)(nil)
	}

	y := proto.Clone(x).(*RspReimportTencentDoc)
	for k, v := range y.Failed {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Failed[k] = vv.MaskInLog().(*RspReimportTencentDoc_FailInfo)
		}
	}

	return y
}

func (x *RspReimportTencentDoc) MaskInRpc() any {
	if x == nil {
		return (*RspReimportTencentDoc)(nil)
	}

	y := x
	for k, v := range y.Failed {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Failed[k] = vv.MaskInRpc().(*RspReimportTencentDoc_FailInfo)
		}
	}

	return y
}

func (x *RspReimportTencentDoc) MaskInBff() any {
	if x == nil {
		return (*RspReimportTencentDoc)(nil)
	}

	y := x
	for k, v := range y.Failed {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Failed[k] = vv.MaskInBff().(*RspReimportTencentDoc_FailInfo)
		}
	}

	return y
}

func (x *RspReimportTencentDoc_FailInfo) MaskInLog() any {
	if x == nil {
		return (*RspReimportTencentDoc_FailInfo)(nil)
	}

	y := proto.Clone(x).(*RspReimportTencentDoc_FailInfo)
	if v, ok := any(y.User).(interface{ MaskInLog() any }); ok {
		y.User = v.MaskInLog().(*ExternalSourceUser)
	}

	return y
}

func (x *RspReimportTencentDoc_FailInfo) MaskInRpc() any {
	if x == nil {
		return (*RspReimportTencentDoc_FailInfo)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInRpc() any }); ok {
		y.User = v.MaskInRpc().(*ExternalSourceUser)
	}

	return y
}

func (x *RspReimportTencentDoc_FailInfo) MaskInBff() any {
	if x == nil {
		return (*RspReimportTencentDoc_FailInfo)(nil)
	}

	y := x
	if v, ok := any(y.User).(interface{ MaskInBff() any }); ok {
		y.User = v.MaskInBff().(*ExternalSourceUser)
	}

	return y
}

func (x *ReqImportTencentDocWebClip) MaskInLog() any {
	if x == nil {
		return (*ReqImportTencentDocWebClip)(nil)
	}

	y := proto.Clone(x).(*ReqImportTencentDocWebClip)
	if v, ok := any(y.AfterTime).(interface{ MaskInLog() any }); ok {
		y.AfterTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqImportTencentDocWebClip) MaskInRpc() any {
	if x == nil {
		return (*ReqImportTencentDocWebClip)(nil)
	}

	y := x
	if v, ok := any(y.AfterTime).(interface{ MaskInRpc() any }); ok {
		y.AfterTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqImportTencentDocWebClip) MaskInBff() any {
	if x == nil {
		return (*ReqImportTencentDocWebClip)(nil)
	}

	y := x
	if v, ok := any(y.AfterTime).(interface{ MaskInBff() any }); ok {
		y.AfterTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspImportTencentDocWebClip) MaskInLog() any {
	if x == nil {
		return (*RspImportTencentDocWebClip)(nil)
	}

	y := proto.Clone(x).(*RspImportTencentDocWebClip)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*TencentDoc)
		}
	}

	return y
}

func (x *RspImportTencentDocWebClip) MaskInRpc() any {
	if x == nil {
		return (*RspImportTencentDocWebClip)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*TencentDoc)
		}
	}

	return y
}

func (x *RspImportTencentDocWebClip) MaskInBff() any {
	if x == nil {
		return (*RspImportTencentDocWebClip)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*TencentDoc)
		}
	}

	return y
}

func (x *RspDescribeDocTab) MaskInLog() any {
	if x == nil {
		return (*RspDescribeDocTab)(nil)
	}

	y := proto.Clone(x).(*RspDescribeDocTab)
	for k, v := range y.Tabs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tabs[k] = vv.MaskInLog().(*RspDescribeDocTab_DocTab)
		}
	}

	return y
}

func (x *RspDescribeDocTab) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeDocTab)(nil)
	}

	y := x
	for k, v := range y.Tabs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tabs[k] = vv.MaskInRpc().(*RspDescribeDocTab_DocTab)
		}
	}

	return y
}

func (x *RspDescribeDocTab) MaskInBff() any {
	if x == nil {
		return (*RspDescribeDocTab)(nil)
	}

	y := x
	for k, v := range y.Tabs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tabs[k] = vv.MaskInBff().(*RspDescribeDocTab_DocTab)
		}
	}

	return y
}

func (x *ReqDescribeNebulaTaskList) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeNebulaTaskList)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeNebulaTaskList)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqDescribeNebulaTaskList) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeNebulaTaskList)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqDescribeNebulaTaskList) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeNebulaTaskList)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspDescribeNebulaTaskList) MaskInLog() any {
	if x == nil {
		return (*RspDescribeNebulaTaskList)(nil)
	}

	y := proto.Clone(x).(*RspDescribeNebulaTaskList)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*RspDescribeNebulaTaskList_Task)
		}
	}

	return y
}

func (x *RspDescribeNebulaTaskList) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeNebulaTaskList)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*RspDescribeNebulaTaskList_Task)
		}
	}

	return y
}

func (x *RspDescribeNebulaTaskList) MaskInBff() any {
	if x == nil {
		return (*RspDescribeNebulaTaskList)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*RspDescribeNebulaTaskList_Task)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInLog() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := proto.Clone(x).(*RspListCustomLabel)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInRpc() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}

	return y
}

func (x *RspListCustomLabel) MaskInBff() any {
	if x == nil {
		return (*RspListCustomLabel)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpsertCustomLabels) MaskInLog() any {
	if x == nil {
		return (*ReqUpsertCustomLabels)(nil)
	}

	y := proto.Clone(x).(*ReqUpsertCustomLabels)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpsertCustomLabels) MaskInRpc() any {
	if x == nil {
		return (*ReqUpsertCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpsertCustomLabels) MaskInBff() any {
	if x == nil {
		return (*ReqUpsertCustomLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqListAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqListAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqListAssistant)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Admins[k] = vv.MaskInLog().(*AssistantAdmin)
		}
	}
	if v, ok := any(y.Order).(interface{ MaskInLog() any }); ok {
		y.Order = v.MaskInLog().(*base.OrderBy)
	}

	return y
}

func (x *ReqListAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqListAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Admins[k] = vv.MaskInRpc().(*AssistantAdmin)
		}
	}
	if v, ok := any(y.Order).(interface{ MaskInRpc() any }); ok {
		y.Order = v.MaskInRpc().(*base.OrderBy)
	}

	return y
}

func (x *ReqListAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqListAssistant)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Admins[k] = vv.MaskInBff().(*AssistantAdmin)
		}
	}
	if v, ok := any(y.Order).(interface{ MaskInBff() any }); ok {
		y.Order = v.MaskInBff().(*base.OrderBy)
	}

	return y
}

func (x *RspListAssistant) MaskInLog() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := proto.Clone(x).(*RspListAssistant)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*Assistant)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*Assistant)
		}
	}

	return y
}

func (x *RspListAssistant) MaskInBff() any {
	if x == nil {
		return (*RspListAssistant)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*Assistant)
		}
	}

	return y
}

func (x *RspGetAssistant) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistant)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistant)
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}

	return y
}

func (x *RspGetAssistant) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistant)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}

	return y
}

func (x *RspGetAssistant) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistant)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}

	return y
}

func (x *RspGetAssistantInfoMap) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantInfoMap)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantInfoMap)
	for k, v := range y.InfoMap {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.InfoMap[k] = vv.MaskInLog().(*RspGetAssistantInfoMap_Info)
		}
	}

	return y
}

func (x *RspGetAssistantInfoMap) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantInfoMap)(nil)
	}

	y := x
	for k, v := range y.InfoMap {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.InfoMap[k] = vv.MaskInRpc().(*RspGetAssistantInfoMap_Info)
		}
	}

	return y
}

func (x *RspGetAssistantInfoMap) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantInfoMap)(nil)
	}

	y := x
	for k, v := range y.InfoMap {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.InfoMap[k] = vv.MaskInBff().(*RspGetAssistantInfoMap_Info)
		}
	}

	return y
}

func (x *RspGetAssistantInfoMap_Info) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantInfoMap_Info)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantInfoMap_Info)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *RspGetAssistantInfoMap_Info) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantInfoMap_Info)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *RspGetAssistantInfoMap_Info) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantInfoMap_Info)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqUpdateCustomChatLabels) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateCustomChatLabels)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateCustomChatLabels)
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateCustomChatLabels) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateCustomChatLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateCustomChatLabels) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateCustomChatLabels)(nil)
	}

	y := x
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqListChatLiveAgent) MaskInLog() any {
	if x == nil {
		return (*ReqListChatLiveAgent)(nil)
	}

	y := proto.Clone(x).(*ReqListChatLiveAgent)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqListChatLiveAgent) MaskInRpc() any {
	if x == nil {
		return (*ReqListChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqListChatLiveAgent) MaskInBff() any {
	if x == nil {
		return (*ReqListChatLiveAgent)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInLog() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := proto.Clone(x).(*RspListChatLiveAgent)
	for k, v := range y.ChatLiveAgents {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ChatLiveAgents[k] = vv.MaskInLog().(*ChatLiveAgent)
		}
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInRpc() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := x
	for k, v := range y.ChatLiveAgents {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ChatLiveAgents[k] = vv.MaskInRpc().(*ChatLiveAgent)
		}
	}

	return y
}

func (x *RspListChatLiveAgent) MaskInBff() any {
	if x == nil {
		return (*RspListChatLiveAgent)(nil)
	}

	y := x
	for k, v := range y.ChatLiveAgents {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ChatLiveAgents[k] = vv.MaskInBff().(*ChatLiveAgent)
		}
	}

	return y
}

func (x *ReqGetAnswerWechat) MaskInLog() any {
	if x == nil {
		return (*ReqGetAnswerWechat)(nil)
	}

	y := proto.Clone(x).(*ReqGetAnswerWechat)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}
	if v, ok := any(y.AnswerStartTime).(interface{ MaskInLog() any }); ok {
		y.AnswerStartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.AnswerEndTime).(interface{ MaskInLog() any }); ok {
		y.AnswerEndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInLog() any }); ok {
		y.CollectionSnapshot = v.MaskInLog().(*MessageCollectionSnapshot)
	}

	return y
}

func (x *ReqGetAnswerWechat) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAnswerWechat)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}
	if v, ok := any(y.AnswerStartTime).(interface{ MaskInRpc() any }); ok {
		y.AnswerStartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.AnswerEndTime).(interface{ MaskInRpc() any }); ok {
		y.AnswerEndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInRpc() any }); ok {
		y.CollectionSnapshot = v.MaskInRpc().(*MessageCollectionSnapshot)
	}

	return y
}

func (x *ReqGetAnswerWechat) MaskInBff() any {
	if x == nil {
		return (*ReqGetAnswerWechat)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}
	if v, ok := any(y.AnswerStartTime).(interface{ MaskInBff() any }); ok {
		y.AnswerStartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.AnswerEndTime).(interface{ MaskInBff() any }); ok {
		y.AnswerEndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInBff() any }); ok {
		y.CollectionSnapshot = v.MaskInBff().(*MessageCollectionSnapshot)
	}

	return y
}

func (x *RspGetAnswerWechat) MaskInLog() any {
	if x == nil {
		return (*RspGetAnswerWechat)(nil)
	}

	y := proto.Clone(x).(*RspGetAnswerWechat)
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Answers[k] = vv.MaskInLog().(*ChatMessage)
		}
	}
	for k, v := range y.AnswerSharding {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AnswerSharding[k] = vv.MaskInLog().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *RspGetAnswerWechat) MaskInRpc() any {
	if x == nil {
		return (*RspGetAnswerWechat)(nil)
	}

	y := x
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Answers[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}
	for k, v := range y.AnswerSharding {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AnswerSharding[k] = vv.MaskInRpc().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *RspGetAnswerWechat) MaskInBff() any {
	if x == nil {
		return (*RspGetAnswerWechat)(nil)
	}

	y := x
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Answers[k] = vv.MaskInBff().(*ChatMessage)
		}
	}
	for k, v := range y.AnswerSharding {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AnswerSharding[k] = vv.MaskInBff().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageStateWechat) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateChatMessageStateWechat)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateChatMessageStateWechat)
	for k, v := range y.Infos {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Infos[k] = vv.MaskInLog().(*ReqUpdateChatMessageStateWechat_Info)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageStateWechat) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateChatMessageStateWechat)(nil)
	}

	y := x
	for k, v := range y.Infos {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Infos[k] = vv.MaskInRpc().(*ReqUpdateChatMessageStateWechat_Info)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageStateWechat) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateChatMessageStateWechat)(nil)
	}

	y := x
	for k, v := range y.Infos {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Infos[k] = vv.MaskInBff().(*ReqUpdateChatMessageStateWechat_Info)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageStateWechat_Info) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateChatMessageStateWechat_Info)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateChatMessageStateWechat_Info)
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageStateWechat_Info) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateChatMessageStateWechat_Info)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageStateWechat_Info) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateChatMessageStateWechat_Info)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *RspGetChatWechat) MaskInLog() any {
	if x == nil {
		return (*RspGetChatWechat)(nil)
	}

	y := proto.Clone(x).(*RspGetChatWechat)
	for k, v := range y.ChatMap {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ChatMap[k] = vv.MaskInLog().(*ChatWeChat)
		}
	}

	return y
}

func (x *RspGetChatWechat) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatWechat)(nil)
	}

	y := x
	for k, v := range y.ChatMap {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ChatMap[k] = vv.MaskInRpc().(*ChatWeChat)
		}
	}

	return y
}

func (x *RspGetChatWechat) MaskInBff() any {
	if x == nil {
		return (*RspGetChatWechat)(nil)
	}

	y := x
	for k, v := range y.ChatMap {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ChatMap[k] = vv.MaskInBff().(*ChatWeChat)
		}
	}

	return y
}

func (x *ReqCreateChatWechat) MaskInLog() any {
	if x == nil {
		return (*ReqCreateChatWechat)(nil)
	}

	y := proto.Clone(x).(*ReqCreateChatWechat)
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chats[k] = vv.MaskInLog().(*ChatWeChat)
		}
	}

	return y
}

func (x *ReqCreateChatWechat) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateChatWechat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chats[k] = vv.MaskInRpc().(*ChatWeChat)
		}
	}

	return y
}

func (x *ReqCreateChatWechat) MaskInBff() any {
	if x == nil {
		return (*ReqCreateChatWechat)(nil)
	}

	y := x
	for k, v := range y.Chats {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chats[k] = vv.MaskInBff().(*ChatWeChat)
		}
	}

	return y
}

func (x *ReqCreateSendRecord) MaskInLog() any {
	if x == nil {
		return (*ReqCreateSendRecord)(nil)
	}

	y := proto.Clone(x).(*ReqCreateSendRecord)
	for k, v := range y.Pieces {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Pieces[k] = vv.MaskInLog().(*ReqCreateSendRecord_Piece)
		}
	}
	if v, ok := any(y.WhatsAppRecord).(interface{ MaskInLog() any }); ok {
		y.WhatsAppRecord = v.MaskInLog().(*AiSendResultRecord)
	}

	return y
}

func (x *ReqCreateSendRecord) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateSendRecord)(nil)
	}

	y := x
	for k, v := range y.Pieces {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Pieces[k] = vv.MaskInRpc().(*ReqCreateSendRecord_Piece)
		}
	}
	if v, ok := any(y.WhatsAppRecord).(interface{ MaskInRpc() any }); ok {
		y.WhatsAppRecord = v.MaskInRpc().(*AiSendResultRecord)
	}

	return y
}

func (x *ReqCreateSendRecord) MaskInBff() any {
	if x == nil {
		return (*ReqCreateSendRecord)(nil)
	}

	y := x
	for k, v := range y.Pieces {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Pieces[k] = vv.MaskInBff().(*ReqCreateSendRecord_Piece)
		}
	}
	if v, ok := any(y.WhatsAppRecord).(interface{ MaskInBff() any }); ok {
		y.WhatsAppRecord = v.MaskInBff().(*AiSendResultRecord)
	}

	return y
}

func (x *RspCreateSendRecord) MaskInLog() any {
	if x == nil {
		return (*RspCreateSendRecord)(nil)
	}

	y := proto.Clone(x).(*RspCreateSendRecord)
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*RspCreateSendRecord_RecordInfo)
		}
	}

	return y
}

func (x *RspCreateSendRecord) MaskInRpc() any {
	if x == nil {
		return (*RspCreateSendRecord)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*RspCreateSendRecord_RecordInfo)
		}
	}

	return y
}

func (x *RspCreateSendRecord) MaskInBff() any {
	if x == nil {
		return (*RspCreateSendRecord)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*RspCreateSendRecord_RecordInfo)
		}
	}

	return y
}

func (x *ReqUpdateSendRecord) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateSendRecord)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateSendRecord)
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *ReqUpdateSendRecord) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateSendRecord)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *ReqUpdateSendRecord) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateSendRecord)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*AiSendResultRecord)
		}
	}

	return y
}

func (x *ReqDescribeUserChatRecords) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeUserChatRecords)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeUserChatRecords)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	if v, ok := any(y.SendRange).(interface{ MaskInLog() any }); ok {
		y.SendRange = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqDescribeUserChatRecords) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeUserChatRecords)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	if v, ok := any(y.SendRange).(interface{ MaskInRpc() any }); ok {
		y.SendRange = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqDescribeUserChatRecords) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeUserChatRecords)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	if v, ok := any(y.SendRange).(interface{ MaskInBff() any }); ok {
		y.SendRange = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspDescribeUserChatRecords) MaskInLog() any {
	if x == nil {
		return (*RspDescribeUserChatRecords)(nil)
	}

	y := proto.Clone(x).(*RspDescribeUserChatRecords)
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*ChatSendRecordInfo)
		}
	}

	return y
}

func (x *RspDescribeUserChatRecords) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeUserChatRecords)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*ChatSendRecordInfo)
		}
	}

	return y
}

func (x *RspDescribeUserChatRecords) MaskInBff() any {
	if x == nil {
		return (*RspDescribeUserChatRecords)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*ChatSendRecordInfo)
		}
	}

	return y
}

func (x *ReqDescribeAssistantMessage) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeAssistantMessage)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeAssistantMessage)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqDescribeAssistantMessage) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeAssistantMessage)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqDescribeAssistantMessage) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeAssistantMessage)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspDescribeAssistantMessage) MaskInLog() any {
	if x == nil {
		return (*RspDescribeAssistantMessage)(nil)
	}

	y := proto.Clone(x).(*RspDescribeAssistantMessage)
	for k, v := range y.Message {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Message[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *RspDescribeAssistantMessage) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeAssistantMessage)(nil)
	}

	y := x
	for k, v := range y.Message {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Message[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *RspDescribeAssistantMessage) MaskInBff() any {
	if x == nil {
		return (*RspDescribeAssistantMessage)(nil)
	}

	y := x
	for k, v := range y.Message {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Message[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *ReqInsertAssistantMessageRecord) MaskInLog() any {
	if x == nil {
		return (*ReqInsertAssistantMessageRecord)(nil)
	}

	y := proto.Clone(x).(*ReqInsertAssistantMessageRecord)
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Records[k] = vv.MaskInLog().(*AiChatSendRecord)
		}
	}

	return y
}

func (x *ReqInsertAssistantMessageRecord) MaskInRpc() any {
	if x == nil {
		return (*ReqInsertAssistantMessageRecord)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Records[k] = vv.MaskInRpc().(*AiChatSendRecord)
		}
	}

	return y
}

func (x *ReqInsertAssistantMessageRecord) MaskInBff() any {
	if x == nil {
		return (*ReqInsertAssistantMessageRecord)(nil)
	}

	y := x
	for k, v := range y.Records {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Records[k] = vv.MaskInBff().(*AiChatSendRecord)
		}
	}

	return y
}

func (x *ReqCreateMessageSuggestQuestion) MaskInLog() any {
	if x == nil {
		return (*ReqCreateMessageSuggestQuestion)(nil)
	}

	y := proto.Clone(x).(*ReqCreateMessageSuggestQuestion)
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}

	return y
}

func (x *ReqCreateMessageSuggestQuestion) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateMessageSuggestQuestion)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}

	return y
}

func (x *ReqCreateMessageSuggestQuestion) MaskInBff() any {
	if x == nil {
		return (*ReqCreateMessageSuggestQuestion)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}

	return y
}

func (x *RspCreateExportTask) MaskInLog() any {
	if x == nil {
		return (*RspCreateExportTask)(nil)
	}

	y := proto.Clone(x).(*RspCreateExportTask)
	if v, ok := any(y.Task).(interface{ MaskInLog() any }); ok {
		y.Task = v.MaskInLog().(*ExportTask)
	}

	return y
}

func (x *RspCreateExportTask) MaskInRpc() any {
	if x == nil {
		return (*RspCreateExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Task).(interface{ MaskInRpc() any }); ok {
		y.Task = v.MaskInRpc().(*ExportTask)
	}

	return y
}

func (x *RspCreateExportTask) MaskInBff() any {
	if x == nil {
		return (*RspCreateExportTask)(nil)
	}

	y := x
	if v, ok := any(y.Task).(interface{ MaskInBff() any }); ok {
		y.Task = v.MaskInBff().(*ExportTask)
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInLog() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := proto.Clone(x).(*RspDescribeExportTasks)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ExportTask)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ExportTask)
		}
	}

	return y
}

func (x *RspDescribeExportTasks) MaskInBff() any {
	if x == nil {
		return (*RspDescribeExportTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ExportTask)
		}
	}

	return y
}

func (x *ReqConvertCustomLabel) MaskInLog() any {
	if x == nil {
		return (*ReqConvertCustomLabel)(nil)
	}

	y := proto.Clone(x).(*ReqConvertCustomLabel)
	if v, ok := any(y.Target).(interface{ MaskInLog() any }); ok {
		y.Target = v.MaskInLog().(*CustomLabel)
	}

	return y
}

func (x *ReqConvertCustomLabel) MaskInRpc() any {
	if x == nil {
		return (*ReqConvertCustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Target).(interface{ MaskInRpc() any }); ok {
		y.Target = v.MaskInRpc().(*CustomLabel)
	}

	return y
}

func (x *ReqConvertCustomLabel) MaskInBff() any {
	if x == nil {
		return (*ReqConvertCustomLabel)(nil)
	}

	y := x
	if v, ok := any(y.Target).(interface{ MaskInBff() any }); ok {
		y.Target = v.MaskInBff().(*CustomLabel)
	}

	return y
}

func (x *RspGetCustomLabelValueTopN) MaskInLog() any {
	if x == nil {
		return (*RspGetCustomLabelValueTopN)(nil)
	}

	y := proto.Clone(x).(*RspGetCustomLabelValueTopN)
	for k, v := range y.Values {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Values[k] = vv.MaskInLog().(*LabelValue)
		}
	}

	return y
}

func (x *RspGetCustomLabelValueTopN) MaskInRpc() any {
	if x == nil {
		return (*RspGetCustomLabelValueTopN)(nil)
	}

	y := x
	for k, v := range y.Values {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Values[k] = vv.MaskInRpc().(*LabelValue)
		}
	}

	return y
}

func (x *RspGetCustomLabelValueTopN) MaskInBff() any {
	if x == nil {
		return (*RspGetCustomLabelValueTopN)(nil)
	}

	y := x
	for k, v := range y.Values {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Values[k] = vv.MaskInBff().(*LabelValue)
		}
	}

	return y
}

func (x *ReqDescribeExportChatMessages) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeExportChatMessages)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeExportChatMessages)
	for k, v := range y.ExportChats {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExportChats[k] = vv.MaskInLog().(*ReqDescribeExportChatMessages_ExportChat)
		}
	}

	return y
}

func (x *ReqDescribeExportChatMessages) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeExportChatMessages)(nil)
	}

	y := x
	for k, v := range y.ExportChats {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExportChats[k] = vv.MaskInRpc().(*ReqDescribeExportChatMessages_ExportChat)
		}
	}

	return y
}

func (x *ReqDescribeExportChatMessages) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeExportChatMessages)(nil)
	}

	y := x
	for k, v := range y.ExportChats {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExportChats[k] = vv.MaskInBff().(*ReqDescribeExportChatMessages_ExportChat)
		}
	}

	return y
}

func (x *RspDescribeExportChatMessages) MaskInLog() any {
	if x == nil {
		return (*RspDescribeExportChatMessages)(nil)
	}

	y := proto.Clone(x).(*RspDescribeExportChatMessages)
	for k, v := range y.ExportMessages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ExportMessages[k] = vv.MaskInLog().(*ExportMessage)
		}
	}

	return y
}

func (x *RspDescribeExportChatMessages) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeExportChatMessages)(nil)
	}

	y := x
	for k, v := range y.ExportMessages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ExportMessages[k] = vv.MaskInRpc().(*ExportMessage)
		}
	}

	return y
}

func (x *RspDescribeExportChatMessages) MaskInBff() any {
	if x == nil {
		return (*RspDescribeExportChatMessages)(nil)
	}

	y := x
	for k, v := range y.ExportMessages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ExportMessages[k] = vv.MaskInBff().(*ExportMessage)
		}
	}

	return y
}

func (x *ReqGetAssistants) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistants)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistants)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetAssistants_Filter)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.OrderBy[k] = vv.MaskInLog().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInLog() any }); ok {
		y.Relation = v.MaskInLog().(*ReqGetAssistants_Relation)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistants) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistants)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetAssistants_Filter)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.OrderBy[k] = vv.MaskInRpc().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInRpc() any }); ok {
		y.Relation = v.MaskInRpc().(*ReqGetAssistants_Relation)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistants) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistants)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetAssistants_Filter)
	}
	for k, v := range y.OrderBy {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.OrderBy[k] = vv.MaskInBff().(*base.OrderBy)
		}
	}
	if v, ok := any(y.Relation).(interface{ MaskInBff() any }); ok {
		y.Relation = v.MaskInBff().(*ReqGetAssistants_Relation)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistants_Filter) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistants_Filter)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistants_Filter)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetAssistants_Filter) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistants_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetAssistants_Filter) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistants_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetAssistants) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistants)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistants)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*FullAssistant)
		}
	}

	return y
}

func (x *RspGetAssistants) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*FullAssistant)
		}
	}

	return y
}

func (x *RspGetAssistants) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*FullAssistant)
		}
	}

	return y
}

func (x *ReqBatchCreateAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqBatchCreateAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqBatchCreateAssistant)
	for k, v := range y.Configs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Configs[k] = vv.MaskInLog().(*AssistantConfig)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqBatchCreateAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchCreateAssistant)(nil)
	}

	y := x
	for k, v := range y.Configs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Configs[k] = vv.MaskInRpc().(*AssistantConfig)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqBatchCreateAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqBatchCreateAssistant)(nil)
	}

	y := x
	for k, v := range y.Configs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Configs[k] = vv.MaskInBff().(*AssistantConfig)
		}
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqBatchUpdateAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqBatchUpdateAssistant)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*ReqBatchUpdateAssistant_Item)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqBatchUpdateAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*ReqBatchUpdateAssistant_Item)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqBatchUpdateAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*ReqBatchUpdateAssistant_Item)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *ReqBatchUpdateAssistant_Item) MaskInLog() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant_Item)(nil)
	}

	y := proto.Clone(x).(*ReqBatchUpdateAssistant_Item)
	if v, ok := any(y.Config).(interface{ MaskInLog() any }); ok {
		y.Config = v.MaskInLog().(*AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.OldUserLabelConfig).(interface{ MaskInLog() any }); ok {
		y.OldUserLabelConfig = v.MaskInLog().(*AssistantUserLabelConfig)
	}

	return y
}

func (x *ReqBatchUpdateAssistant_Item) MaskInRpc() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant_Item)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInRpc() any }); ok {
		y.Config = v.MaskInRpc().(*AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.OldUserLabelConfig).(interface{ MaskInRpc() any }); ok {
		y.OldUserLabelConfig = v.MaskInRpc().(*AssistantUserLabelConfig)
	}

	return y
}

func (x *ReqBatchUpdateAssistant_Item) MaskInBff() any {
	if x == nil {
		return (*ReqBatchUpdateAssistant_Item)(nil)
	}

	y := x
	if v, ok := any(y.Config).(interface{ MaskInBff() any }); ok {
		y.Config = v.MaskInBff().(*AssistantConfig)
	}
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	if v, ok := any(y.OldUserLabelConfig).(interface{ MaskInBff() any }); ok {
		y.OldUserLabelConfig = v.MaskInBff().(*AssistantUserLabelConfig)
	}

	return y
}

func (x *ReqDeleteAssistant) MaskInLog() any {
	if x == nil {
		return (*ReqDeleteAssistant)(nil)
	}

	y := proto.Clone(x).(*ReqDeleteAssistant)
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*base.Identity)
	}

	switch v := y.Condition.(type) {
	case *ReqDeleteAssistant_ByAssistantId:
		if vv, ok := any(v.ByAssistantId).(interface{ MaskInLog() any }); ok {
			v.ByAssistantId = vv.MaskInLog().(*ReqDeleteAssistant_AssistantId)
		}
	}

	return y
}

func (x *ReqDeleteAssistant) MaskInRpc() any {
	if x == nil {
		return (*ReqDeleteAssistant)(nil)
	}

	y := x
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*base.Identity)
	}

	switch v := y.Condition.(type) {
	case *ReqDeleteAssistant_ByAssistantId:
		if vv, ok := any(v.ByAssistantId).(interface{ MaskInRpc() any }); ok {
			v.ByAssistantId = vv.MaskInRpc().(*ReqDeleteAssistant_AssistantId)
		}
	}

	return y
}

func (x *ReqDeleteAssistant) MaskInBff() any {
	if x == nil {
		return (*ReqDeleteAssistant)(nil)
	}

	y := x
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*base.Identity)
	}

	switch v := y.Condition.(type) {
	case *ReqDeleteAssistant_ByAssistantId:
		if vv, ok := any(v.ByAssistantId).(interface{ MaskInBff() any }); ok {
			v.ByAssistantId = vv.MaskInBff().(*ReqDeleteAssistant_AssistantId)
		}
	}

	return y
}

func (x *ReqGetAssistantLogs) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistantLogs)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistantLogs)
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*ReqGetAssistantLogs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistantLogs) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistantLogs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*ReqGetAssistantLogs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistantLogs) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistantLogs)(nil)
	}

	y := x
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*ReqGetAssistantLogs_Filter)
	}
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistantLogs_Filter) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistantLogs_Filter)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistantLogs_Filter)
	if v, ok := any(y.CreateDate).(interface{ MaskInLog() any }); ok {
		y.CreateDate = v.MaskInLog().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetAssistantLogs_Filter) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistantLogs_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInRpc() any }); ok {
		y.CreateDate = v.MaskInRpc().(*base.TimeRange)
	}

	return y
}

func (x *ReqGetAssistantLogs_Filter) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistantLogs_Filter)(nil)
	}

	y := x
	if v, ok := any(y.CreateDate).(interface{ MaskInBff() any }); ok {
		y.CreateDate = v.MaskInBff().(*base.TimeRange)
	}

	return y
}

func (x *RspGetAssistantLogs) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantLogs)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantLogs)
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Logs[k] = vv.MaskInLog().(*AssistantLog)
		}
	}

	return y
}

func (x *RspGetAssistantLogs) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantLogs)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Logs[k] = vv.MaskInRpc().(*AssistantLog)
		}
	}

	return y
}

func (x *RspGetAssistantLogs) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantLogs)(nil)
	}

	y := x
	for k, v := range y.Logs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Logs[k] = vv.MaskInBff().(*AssistantLog)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantOptions)
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.InteractiveCode[k] = vv.MaskInLog().(*InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.VisibleChain[k] = vv.MaskInLog().(*VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInLog().(*EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.ChatModelV2[k] = vv.MaskInLog().(*ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInLog().(*SearchEngineOption)
		}
	}
	for k, v := range y.QuickActions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.QuickActions[k] = vv.MaskInLog().(*QuickAction)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := x
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.InteractiveCode[k] = vv.MaskInRpc().(*InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.VisibleChain[k] = vv.MaskInRpc().(*VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInRpc().(*EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.ChatModelV2[k] = vv.MaskInRpc().(*ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInRpc().(*SearchEngineOption)
		}
	}
	for k, v := range y.QuickActions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.QuickActions[k] = vv.MaskInRpc().(*QuickAction)
		}
	}

	return y
}

func (x *RspGetAssistantOptions) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantOptions)(nil)
	}

	y := x
	for k, v := range y.InteractiveCode {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.InteractiveCode[k] = vv.MaskInBff().(*InteractiveCodeOption)
		}
	}
	for k, v := range y.VisibleChain {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.VisibleChain[k] = vv.MaskInBff().(*VisibleChainOption)
		}
	}
	for k, v := range y.EmbeddingModel {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.EmbeddingModel[k] = vv.MaskInBff().(*EmbeddingModelOption)
		}
	}
	for k, v := range y.ChatModelV2 {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.ChatModelV2[k] = vv.MaskInBff().(*ChatModelOption)
		}
	}
	for k, v := range y.SearchEngineV2 {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.SearchEngineV2[k] = vv.MaskInBff().(*SearchEngineOption)
		}
	}
	for k, v := range y.QuickActions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.QuickActions[k] = vv.MaskInBff().(*QuickAction)
		}
	}

	return y
}

func (x *ReqCheckAssistantAllowlist) MaskInLog() any {
	if x == nil {
		return (*ReqCheckAssistantAllowlist)(nil)
	}

	y := proto.Clone(x).(*ReqCheckAssistantAllowlist)
	switch v := y.TypePara.(type) {
	case *ReqCheckAssistantAllowlist_PhoneTypePara:
		if vv, ok := any(v.PhoneTypePara).(interface{ MaskInLog() any }); ok {
			v.PhoneTypePara = vv.MaskInLog().(*ReqCheckAssistantAllowlist_PhonePara)
		}
	}

	return y
}

func (x *ReqCheckAssistantAllowlist) MaskInRpc() any {
	if x == nil {
		return (*ReqCheckAssistantAllowlist)(nil)
	}

	y := x
	switch v := y.TypePara.(type) {
	case *ReqCheckAssistantAllowlist_PhoneTypePara:
		if vv, ok := any(v.PhoneTypePara).(interface{ MaskInRpc() any }); ok {
			v.PhoneTypePara = vv.MaskInRpc().(*ReqCheckAssistantAllowlist_PhonePara)
		}
	}

	return y
}

func (x *ReqCheckAssistantAllowlist) MaskInBff() any {
	if x == nil {
		return (*ReqCheckAssistantAllowlist)(nil)
	}

	y := x
	switch v := y.TypePara.(type) {
	case *ReqCheckAssistantAllowlist_PhoneTypePara:
		if vv, ok := any(v.PhoneTypePara).(interface{ MaskInBff() any }); ok {
			v.PhoneTypePara = vv.MaskInBff().(*ReqCheckAssistantAllowlist_PhonePara)
		}
	}

	return y
}

func (x *ReqGetDocChunks) MaskInLog() any {
	if x == nil {
		return (*ReqGetDocChunks)(nil)
	}

	y := proto.Clone(x).(*ReqGetDocChunks)
	if v, ok := any(y.Admin).(interface{ MaskInLog() any }); ok {
		y.Admin = v.MaskInLog().(*base.Identity)
	}

	return y
}

func (x *ReqGetDocChunks) MaskInRpc() any {
	if x == nil {
		return (*ReqGetDocChunks)(nil)
	}

	y := x
	if v, ok := any(y.Admin).(interface{ MaskInRpc() any }); ok {
		y.Admin = v.MaskInRpc().(*base.Identity)
	}

	return y
}

func (x *ReqGetDocChunks) MaskInBff() any {
	if x == nil {
		return (*ReqGetDocChunks)(nil)
	}

	y := x
	if v, ok := any(y.Admin).(interface{ MaskInBff() any }); ok {
		y.Admin = v.MaskInBff().(*base.Identity)
	}

	return y
}

func (x *RspGetDocChunks) MaskInLog() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := proto.Clone(x).(*RspGetDocChunks)
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.AssistantChunks[k] = vv.MaskInLog().(*AssistantChunks)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.AssistantChunks[k] = vv.MaskInRpc().(*AssistantChunks)
		}
	}

	return y
}

func (x *RspGetDocChunks) MaskInBff() any {
	if x == nil {
		return (*RspGetDocChunks)(nil)
	}

	y := x
	for k, v := range y.AssistantChunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.AssistantChunks[k] = vv.MaskInBff().(*AssistantChunks)
		}
	}

	return y
}

func (x *ReqChunkDoc) MaskInLog() any {
	if x == nil {
		return (*ReqChunkDoc)(nil)
	}

	y := proto.Clone(x).(*ReqChunkDoc)
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*base.Identity)
	}

	switch v := y.ChunkPara.(type) {
	case *ReqChunkDoc_AutoPara:
		if vv, ok := any(v.AutoPara).(interface{ MaskInLog() any }); ok {
			v.AutoPara = vv.MaskInLog().(*AutoChunkPara)
		}
	case *ReqChunkDoc_ManualPara:
		if vv, ok := any(v.ManualPara).(interface{ MaskInLog() any }); ok {
			v.ManualPara = vv.MaskInLog().(*ManualChunkPara)
		}
	}

	return y
}

func (x *ReqChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*base.Identity)
	}

	switch v := y.ChunkPara.(type) {
	case *ReqChunkDoc_AutoPara:
		if vv, ok := any(v.AutoPara).(interface{ MaskInRpc() any }); ok {
			v.AutoPara = vv.MaskInRpc().(*AutoChunkPara)
		}
	case *ReqChunkDoc_ManualPara:
		if vv, ok := any(v.ManualPara).(interface{ MaskInRpc() any }); ok {
			v.ManualPara = vv.MaskInRpc().(*ManualChunkPara)
		}
	}

	return y
}

func (x *ReqChunkDoc) MaskInBff() any {
	if x == nil {
		return (*ReqChunkDoc)(nil)
	}

	y := x
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*base.Identity)
	}

	switch v := y.ChunkPara.(type) {
	case *ReqChunkDoc_AutoPara:
		if vv, ok := any(v.AutoPara).(interface{ MaskInBff() any }); ok {
			v.AutoPara = vv.MaskInBff().(*AutoChunkPara)
		}
	case *ReqChunkDoc_ManualPara:
		if vv, ok := any(v.ManualPara).(interface{ MaskInBff() any }); ok {
			v.ManualPara = vv.MaskInBff().(*ManualChunkPara)
		}
	}

	return y
}

func (x *RspChunkDoc) MaskInLog() any {
	if x == nil {
		return (*RspChunkDoc)(nil)
	}

	y := proto.Clone(x).(*RspChunkDoc)
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Chunks[k] = vv.MaskInLog().(*ChunkItem)
		}
	}

	return y
}

func (x *RspChunkDoc) MaskInRpc() any {
	if x == nil {
		return (*RspChunkDoc)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Chunks[k] = vv.MaskInRpc().(*ChunkItem)
		}
	}

	return y
}

func (x *RspChunkDoc) MaskInBff() any {
	if x == nil {
		return (*RspChunkDoc)(nil)
	}

	y := x
	for k, v := range y.Chunks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Chunks[k] = vv.MaskInBff().(*ChunkItem)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInLog() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := proto.Clone(x).(*RspGetChunkDocTasks)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*DocChunkTask)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInRpc() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*DocChunkTask)
		}
	}

	return y
}

func (x *RspGetChunkDocTasks) MaskInBff() any {
	if x == nil {
		return (*RspGetChunkDocTasks)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*DocChunkTask)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInLog() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := proto.Clone(x).(*RspGetDocEmbeddingModels)
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInLog().(*EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInRpc() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := x
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInRpc().(*EmbeddingModelCount)
		}
	}

	return y
}

func (x *RspGetDocEmbeddingModels) MaskInBff() any {
	if x == nil {
		return (*RspGetDocEmbeddingModels)(nil)
	}

	y := x
	for k, v := range y.EmbeddingModels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.EmbeddingModels[k] = vv.MaskInBff().(*EmbeddingModelCount)
		}
	}

	return y
}

func (x *ReqResendMessageSync) MaskInLog() any {
	if x == nil {
		return (*ReqResendMessageSync)(nil)
	}

	y := proto.Clone(x).(*ReqResendMessageSync)
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}

	return y
}

func (x *ReqResendMessageSync) MaskInRpc() any {
	if x == nil {
		return (*ReqResendMessageSync)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}

	return y
}

func (x *ReqResendMessageSync) MaskInBff() any {
	if x == nil {
		return (*ReqResendMessageSync)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}

	return y
}

func (x *RspResendMessageSync) MaskInLog() any {
	if x == nil {
		return (*RspResendMessageSync)(nil)
	}

	y := proto.Clone(x).(*RspResendMessageSync)
	for k, v := range y.Message {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Message[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *RspResendMessageSync) MaskInRpc() any {
	if x == nil {
		return (*RspResendMessageSync)(nil)
	}

	y := x
	for k, v := range y.Message {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Message[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *RspResendMessageSync) MaskInBff() any {
	if x == nil {
		return (*RspResendMessageSync)(nil)
	}

	y := x
	for k, v := range y.Message {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Message[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *ReqUpdateDocAttrInBulk) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateDocAttrInBulk)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateDocAttrInBulk)
	if v, ok := any(y.Mask).(interface{ MaskInLog() any }); ok {
		y.Mask = v.MaskInLog().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Contributor[k] = vv.MaskInLog().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInLog() any }); ok {
		y.UpdateBy = v.MaskInLog().(*Operator)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Reference[k] = vv.MaskInLog().(*DocReference)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Labels[k] = vv.MaskInLog().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateDocAttrInBulk) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateDocAttrInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInRpc() any }); ok {
		y.Mask = v.MaskInRpc().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Contributor[k] = vv.MaskInRpc().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInRpc() any }); ok {
		y.UpdateBy = v.MaskInRpc().(*Operator)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Reference[k] = vv.MaskInRpc().(*DocReference)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Labels[k] = vv.MaskInRpc().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqUpdateDocAttrInBulk) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateDocAttrInBulk)(nil)
	}

	y := x
	if v, ok := any(y.Mask).(interface{ MaskInBff() any }); ok {
		y.Mask = v.MaskInBff().(*fieldmaskpb.FieldMask)
	}
	for k, v := range y.Contributor {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Contributor[k] = vv.MaskInBff().(*Contributor)
		}
	}
	if v, ok := any(y.UpdateBy).(interface{ MaskInBff() any }); ok {
		y.UpdateBy = v.MaskInBff().(*Operator)
	}
	for k, v := range y.Reference {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Reference[k] = vv.MaskInBff().(*DocReference)
		}
	}
	for k, v := range y.Labels {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Labels[k] = vv.MaskInBff().(*CustomLabel)
		}
	}

	return y
}

func (x *ReqDescribeMessageMatchQa) MaskInLog() any {
	if x == nil {
		return (*ReqDescribeMessageMatchQa)(nil)
	}

	y := proto.Clone(x).(*ReqDescribeMessageMatchQa)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInLog() any }); ok {
		y.Filter = v.MaskInLog().(*MessageMatchQaFilter)
	}

	return y
}

func (x *ReqDescribeMessageMatchQa) MaskInRpc() any {
	if x == nil {
		return (*ReqDescribeMessageMatchQa)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInRpc() any }); ok {
		y.Filter = v.MaskInRpc().(*MessageMatchQaFilter)
	}

	return y
}

func (x *ReqDescribeMessageMatchQa) MaskInBff() any {
	if x == nil {
		return (*ReqDescribeMessageMatchQa)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}
	if v, ok := any(y.Filter).(interface{ MaskInBff() any }); ok {
		y.Filter = v.MaskInBff().(*MessageMatchQaFilter)
	}

	return y
}

func (x *RspDescribeMessageMatchQa) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessageMatchQa)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessageMatchQa)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageMatchQa) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessageMatchQa)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageMatchQa) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessageMatchQa)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ReqCreateQaMatchMessage) MaskInLog() any {
	if x == nil {
		return (*ReqCreateQaMatchMessage)(nil)
	}

	y := proto.Clone(x).(*ReqCreateQaMatchMessage)
	if v, ok := any(y.Question).(interface{ MaskInLog() any }); ok {
		y.Question = v.MaskInLog().(*ChatMessage)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInLog() any }); ok {
		y.CollectionSnapshot = v.MaskInLog().(*MessageCollectionSnapshot)
	}
	if v, ok := any(y.Task).(interface{ MaskInLog() any }); ok {
		y.Task = v.MaskInLog().(*ChatMessageTask)
	}

	return y
}

func (x *ReqCreateQaMatchMessage) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateQaMatchMessage)(nil)
	}

	y := x
	if v, ok := any(y.Question).(interface{ MaskInRpc() any }); ok {
		y.Question = v.MaskInRpc().(*ChatMessage)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInRpc() any }); ok {
		y.CollectionSnapshot = v.MaskInRpc().(*MessageCollectionSnapshot)
	}
	if v, ok := any(y.Task).(interface{ MaskInRpc() any }); ok {
		y.Task = v.MaskInRpc().(*ChatMessageTask)
	}

	return y
}

func (x *ReqCreateQaMatchMessage) MaskInBff() any {
	if x == nil {
		return (*ReqCreateQaMatchMessage)(nil)
	}

	y := x
	if v, ok := any(y.Question).(interface{ MaskInBff() any }); ok {
		y.Question = v.MaskInBff().(*ChatMessage)
	}
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.CollectionSnapshot).(interface{ MaskInBff() any }); ok {
		y.CollectionSnapshot = v.MaskInBff().(*MessageCollectionSnapshot)
	}
	if v, ok := any(y.Task).(interface{ MaskInBff() any }); ok {
		y.Task = v.MaskInBff().(*ChatMessageTask)
	}

	return y
}

func (x *RspCreateQaMatchMessage) MaskInLog() any {
	if x == nil {
		return (*RspCreateQaMatchMessage)(nil)
	}

	y := proto.Clone(x).(*RspCreateQaMatchMessage)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *RspCreateQaMatchMessage) MaskInRpc() any {
	if x == nil {
		return (*RspCreateQaMatchMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *RspCreateQaMatchMessage) MaskInBff() any {
	if x == nil {
		return (*RspCreateQaMatchMessage)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *ReqGetRecentlyUsedAssistants) MaskInLog() any {
	if x == nil {
		return (*ReqGetRecentlyUsedAssistants)(nil)
	}

	y := proto.Clone(x).(*ReqGetRecentlyUsedAssistants)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetRecentlyUsedAssistants) MaskInRpc() any {
	if x == nil {
		return (*ReqGetRecentlyUsedAssistants)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetRecentlyUsedAssistants) MaskInBff() any {
	if x == nil {
		return (*ReqGetRecentlyUsedAssistants)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspGetRecentlyUsedAssistants) MaskInLog() any {
	if x == nil {
		return (*RspGetRecentlyUsedAssistants)(nil)
	}

	y := proto.Clone(x).(*RspGetRecentlyUsedAssistants)
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Assistants[k] = vv.MaskInLog().(*FullAssistant)
		}
	}

	return y
}

func (x *RspGetRecentlyUsedAssistants) MaskInRpc() any {
	if x == nil {
		return (*RspGetRecentlyUsedAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Assistants[k] = vv.MaskInRpc().(*FullAssistant)
		}
	}

	return y
}

func (x *RspGetRecentlyUsedAssistants) MaskInBff() any {
	if x == nil {
		return (*RspGetRecentlyUsedAssistants)(nil)
	}

	y := x
	for k, v := range y.Assistants {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Assistants[k] = vv.MaskInBff().(*FullAssistant)
		}
	}

	return y
}

func (x *ReqGetAssistantAdmin) MaskInLog() any {
	if x == nil {
		return (*ReqGetAssistantAdmin)(nil)
	}

	y := proto.Clone(x).(*ReqGetAssistantAdmin)
	if v, ok := any(y.Page).(interface{ MaskInLog() any }); ok {
		y.Page = v.MaskInLog().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistantAdmin) MaskInRpc() any {
	if x == nil {
		return (*ReqGetAssistantAdmin)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInRpc() any }); ok {
		y.Page = v.MaskInRpc().(*base.Paginator)
	}

	return y
}

func (x *ReqGetAssistantAdmin) MaskInBff() any {
	if x == nil {
		return (*ReqGetAssistantAdmin)(nil)
	}

	y := x
	if v, ok := any(y.Page).(interface{ MaskInBff() any }); ok {
		y.Page = v.MaskInBff().(*base.Paginator)
	}

	return y
}

func (x *RspGetAssistantAdmin) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantAdmin)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantAdmin)
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Admins[k] = vv.MaskInLog().(*RspGetAssistantAdmin_Info)
		}
	}

	return y
}

func (x *RspGetAssistantAdmin) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantAdmin)(nil)
	}

	y := x
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Admins[k] = vv.MaskInRpc().(*RspGetAssistantAdmin_Info)
		}
	}

	return y
}

func (x *RspGetAssistantAdmin) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantAdmin)(nil)
	}

	y := x
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Admins[k] = vv.MaskInBff().(*RspGetAssistantAdmin_Info)
		}
	}

	return y
}

func (x *RspGetAssistantAdmin_Info) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantAdmin_Info)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantAdmin_Info)
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Admins[k] = vv.MaskInLog().(*base.Identity)
		}
	}

	return y
}

func (x *RspGetAssistantAdmin_Info) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantAdmin_Info)(nil)
	}

	y := x
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Admins[k] = vv.MaskInRpc().(*base.Identity)
		}
	}

	return y
}

func (x *RspGetAssistantAdmin_Info) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantAdmin_Info)(nil)
	}

	y := x
	for k, v := range y.Admins {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Admins[k] = vv.MaskInBff().(*base.Identity)
		}
	}

	return y
}

func (x *RspGetAssistantChatUser) MaskInLog() any {
	if x == nil {
		return (*RspGetAssistantChatUser)(nil)
	}

	y := proto.Clone(x).(*RspGetAssistantChatUser)
	for k, v := range y.UserInfo {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.UserInfo[k] = vv.MaskInLog().(*RspGetAssistantChatUser_Info)
		}
	}

	return y
}

func (x *RspGetAssistantChatUser) MaskInRpc() any {
	if x == nil {
		return (*RspGetAssistantChatUser)(nil)
	}

	y := x
	for k, v := range y.UserInfo {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.UserInfo[k] = vv.MaskInRpc().(*RspGetAssistantChatUser_Info)
		}
	}

	return y
}

func (x *RspGetAssistantChatUser) MaskInBff() any {
	if x == nil {
		return (*RspGetAssistantChatUser)(nil)
	}

	y := x
	for k, v := range y.UserInfo {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.UserInfo[k] = vv.MaskInBff().(*RspGetAssistantChatUser_Info)
		}
	}

	return y
}

func (x *ReqGetTextFileTip) MaskInLog() any {
	if x == nil {
		return (*ReqGetTextFileTip)(nil)
	}

	y := proto.Clone(x).(*ReqGetTextFileTip)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqGetTextFileTip) MaskInRpc() any {
	if x == nil {
		return (*ReqGetTextFileTip)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqGetTextFileTip) MaskInBff() any {
	if x == nil {
		return (*ReqGetTextFileTip)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *RspGetTextFileTip) MaskInLog() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := proto.Clone(x).(*RspGetTextFileTip)
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.TableOverSize[k] = vv.MaskInLog().(*TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInRpc() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := x
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.TableOverSize[k] = vv.MaskInRpc().(*TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *RspGetTextFileTip) MaskInBff() any {
	if x == nil {
		return (*RspGetTextFileTip)(nil)
	}

	y := x
	for k, v := range y.TableOverSize {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.TableOverSize[k] = vv.MaskInBff().(*TextFileTipTableOverSize)
		}
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInLog() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := proto.Clone(x).(*ReqCreateDocQuery)
	if v, ok := any(y.Doc).(interface{ MaskInLog() any }); ok {
		y.Doc = v.MaskInLog().(*ReqListTextFile)
	}
	if v, ok := any(y.Qa).(interface{ MaskInLog() any }); ok {
		y.Qa = v.MaskInLog().(*ReqListQA)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInLog() any }); ok {
		y.CreateBy = v.MaskInLog().(*Operator)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInRpc() any }); ok {
		y.Doc = v.MaskInRpc().(*ReqListTextFile)
	}
	if v, ok := any(y.Qa).(interface{ MaskInRpc() any }); ok {
		y.Qa = v.MaskInRpc().(*ReqListQA)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInRpc() any }); ok {
		y.CreateBy = v.MaskInRpc().(*Operator)
	}

	return y
}

func (x *ReqCreateDocQuery) MaskInBff() any {
	if x == nil {
		return (*ReqCreateDocQuery)(nil)
	}

	y := x
	if v, ok := any(y.Doc).(interface{ MaskInBff() any }); ok {
		y.Doc = v.MaskInBff().(*ReqListTextFile)
	}
	if v, ok := any(y.Qa).(interface{ MaskInBff() any }); ok {
		y.Qa = v.MaskInBff().(*ReqListQA)
	}
	if v, ok := any(y.CreateBy).(interface{ MaskInBff() any }); ok {
		y.CreateBy = v.MaskInBff().(*Operator)
	}

	return y
}

func (x *ReqGetQaTip) MaskInLog() any {
	if x == nil {
		return (*ReqGetQaTip)(nil)
	}

	y := proto.Clone(x).(*ReqGetQaTip)
	if v, ok := any(y.Contributor).(interface{ MaskInLog() any }); ok {
		y.Contributor = v.MaskInLog().(*Contributor)
	}

	return y
}

func (x *ReqGetQaTip) MaskInRpc() any {
	if x == nil {
		return (*ReqGetQaTip)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInRpc() any }); ok {
		y.Contributor = v.MaskInRpc().(*Contributor)
	}

	return y
}

func (x *ReqGetQaTip) MaskInBff() any {
	if x == nil {
		return (*ReqGetQaTip)(nil)
	}

	y := x
	if v, ok := any(y.Contributor).(interface{ MaskInBff() any }); ok {
		y.Contributor = v.MaskInBff().(*Contributor)
	}

	return y
}

func (x *ReqParseChatDoc) MaskInLog() any {
	if x == nil {
		return (*ReqParseChatDoc)(nil)
	}

	y := proto.Clone(x).(*ReqParseChatDoc)
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*ChatMessageFile)
		}
	}

	return y
}

func (x *ReqParseChatDoc) MaskInRpc() any {
	if x == nil {
		return (*ReqParseChatDoc)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*ChatMessageFile)
		}
	}

	return y
}

func (x *ReqParseChatDoc) MaskInBff() any {
	if x == nil {
		return (*ReqParseChatDoc)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*ChatMessageFile)
		}
	}

	return y
}

func (x *RspDescribeChatQuestionAnswersByPage) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChatQuestionAnswersByPage)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChatQuestionAnswersByPage)
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Questions[k] = vv.MaskInLog().(*ChatMessage)
		}
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Answers[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *RspDescribeChatQuestionAnswersByPage) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChatQuestionAnswersByPage)(nil)
	}

	y := x
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Questions[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Answers[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *RspDescribeChatQuestionAnswersByPage) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChatQuestionAnswersByPage)(nil)
	}

	y := x
	for k, v := range y.Questions {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Questions[k] = vv.MaskInBff().(*ChatMessage)
		}
	}
	for k, v := range y.Answers {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Answers[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *ReqCreateChatTaskMessage) MaskInLog() any {
	if x == nil {
		return (*ReqCreateChatTaskMessage)(nil)
	}

	y := proto.Clone(x).(*ReqCreateChatTaskMessage)
	if v, ok := any(y.AssistantDetail).(interface{ MaskInLog() any }); ok {
		y.AssistantDetail = v.MaskInLog().(*AssistantDetail)
	}
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ChatAgentTask)
		}
	}

	return y
}

func (x *ReqCreateChatTaskMessage) MaskInRpc() any {
	if x == nil {
		return (*ReqCreateChatTaskMessage)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInRpc() any }); ok {
		y.AssistantDetail = v.MaskInRpc().(*AssistantDetail)
	}
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ChatAgentTask)
		}
	}

	return y
}

func (x *ReqCreateChatTaskMessage) MaskInBff() any {
	if x == nil {
		return (*ReqCreateChatTaskMessage)(nil)
	}

	y := x
	if v, ok := any(y.AssistantDetail).(interface{ MaskInBff() any }); ok {
		y.AssistantDetail = v.MaskInBff().(*AssistantDetail)
	}
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ChatAgentTask)
		}
	}

	return y
}

func (x *RspCreateChatTaskMessage) MaskInLog() any {
	if x == nil {
		return (*RspCreateChatTaskMessage)(nil)
	}

	y := proto.Clone(x).(*RspCreateChatTaskMessage)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *RspCreateChatTaskMessage) MaskInRpc() any {
	if x == nil {
		return (*RspCreateChatTaskMessage)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *RspCreateChatTaskMessage) MaskInBff() any {
	if x == nil {
		return (*RspCreateChatTaskMessage)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *RspDescribeChatAgentTask) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChatAgentTask)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChatAgentTask)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ChatAgentTask)
		}
	}

	return y
}

func (x *RspDescribeChatAgentTask) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChatAgentTask)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ChatAgentTask)
		}
	}

	return y
}

func (x *RspDescribeChatAgentTask) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChatAgentTask)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ChatAgentTask)
		}
	}

	return y
}

func (x *ReqFixSearchCollectionItems) MaskInLog() any {
	if x == nil {
		return (*ReqFixSearchCollectionItems)(nil)
	}

	y := proto.Clone(x).(*ReqFixSearchCollectionItems)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqFixSearchCollectionItems) MaskInRpc() any {
	if x == nil {
		return (*ReqFixSearchCollectionItems)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqFixSearchCollectionItems) MaskInBff() any {
	if x == nil {
		return (*ReqFixSearchCollectionItems)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspFixSearchCollectionItems) MaskInLog() any {
	if x == nil {
		return (*RspFixSearchCollectionItems)(nil)
	}

	y := proto.Clone(x).(*RspFixSearchCollectionItems)
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspFixSearchCollectionItems) MaskInRpc() any {
	if x == nil {
		return (*RspFixSearchCollectionItems)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspFixSearchCollectionItems) MaskInBff() any {
	if x == nil {
		return (*RspFixSearchCollectionItems)(nil)
	}

	y := x
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageCollections) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateChatMessageCollections)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateChatMessageCollections)
	if v, ok := any(y.StartTime).(interface{ MaskInLog() any }); ok {
		y.StartTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInLog() any }); ok {
		y.EndTime = v.MaskInLog().(*timestamppb.Timestamp)
	}
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Items[k] = vv.MaskInLog().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageCollections) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateChatMessageCollections)(nil)
	}

	y := x
	if v, ok := any(y.StartTime).(interface{ MaskInRpc() any }); ok {
		y.StartTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInRpc() any }); ok {
		y.EndTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Items[k] = vv.MaskInRpc().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *ReqUpdateChatMessageCollections) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateChatMessageCollections)(nil)
	}

	y := x
	if v, ok := any(y.StartTime).(interface{ MaskInBff() any }); ok {
		y.StartTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.EndTime).(interface{ MaskInBff() any }); ok {
		y.EndTime = v.MaskInBff().(*timestamppb.Timestamp)
	}
	for k, v := range y.Items {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Items[k] = vv.MaskInBff().(*SearchCollectionItem)
		}
	}

	return y
}

func (x *RspDescribeChatAgentTaskByPreTask) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChatAgentTaskByPreTask)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChatAgentTaskByPreTask)
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Tasks[k] = vv.MaskInLog().(*ChatAgentTask)
		}
	}

	return y
}

func (x *RspDescribeChatAgentTaskByPreTask) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChatAgentTaskByPreTask)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Tasks[k] = vv.MaskInRpc().(*ChatAgentTask)
		}
	}

	return y
}

func (x *RspDescribeChatAgentTaskByPreTask) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChatAgentTaskByPreTask)(nil)
	}

	y := x
	for k, v := range y.Tasks {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Tasks[k] = vv.MaskInBff().(*ChatAgentTask)
		}
	}

	return y
}

func (x *RspDescribeChatMessageFileState) MaskInLog() any {
	if x == nil {
		return (*RspDescribeChatMessageFileState)(nil)
	}

	y := proto.Clone(x).(*RspDescribeChatMessageFileState)
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Files[k] = vv.MaskInLog().(*ChatMessageFile)
		}
	}

	return y
}

func (x *RspDescribeChatMessageFileState) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeChatMessageFileState)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Files[k] = vv.MaskInRpc().(*ChatMessageFile)
		}
	}

	return y
}

func (x *RspDescribeChatMessageFileState) MaskInBff() any {
	if x == nil {
		return (*RspDescribeChatMessageFileState)(nil)
	}

	y := x
	for k, v := range y.Files {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Files[k] = vv.MaskInBff().(*ChatMessageFile)
		}
	}

	return y
}

func (x *RspStopQuestionReply) MaskInLog() any {
	if x == nil {
		return (*RspStopQuestionReply)(nil)
	}

	y := proto.Clone(x).(*RspStopQuestionReply)
	if v, ok := any(y.Message).(interface{ MaskInLog() any }); ok {
		y.Message = v.MaskInLog().(*ChatMessage)
	}

	return y
}

func (x *RspStopQuestionReply) MaskInRpc() any {
	if x == nil {
		return (*RspStopQuestionReply)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInRpc() any }); ok {
		y.Message = v.MaskInRpc().(*ChatMessage)
	}

	return y
}

func (x *RspStopQuestionReply) MaskInBff() any {
	if x == nil {
		return (*RspStopQuestionReply)(nil)
	}

	y := x
	if v, ok := any(y.Message).(interface{ MaskInBff() any }); ok {
		y.Message = v.MaskInBff().(*ChatMessage)
	}

	return y
}

func (x *RspGetChatShareMessages) MaskInLog() any {
	if x == nil {
		return (*RspGetChatShareMessages)(nil)
	}

	y := proto.Clone(x).(*RspGetChatShareMessages)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *RspGetChatShareMessages) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatShareMessages)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *RspGetChatShareMessages) MaskInBff() any {
	if x == nil {
		return (*RspGetChatShareMessages)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *RspGetChatShareRecord) MaskInLog() any {
	if x == nil {
		return (*RspGetChatShareRecord)(nil)
	}

	y := proto.Clone(x).(*RspGetChatShareRecord)
	if v, ok := any(y.ShareDate).(interface{ MaskInLog() any }); ok {
		y.ShareDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInLog() any }); ok {
		y.ExpireDate = v.MaskInLog().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInLog() any }); ok {
		y.LastAccessTime = v.MaskInLog().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspGetChatShareRecord) MaskInRpc() any {
	if x == nil {
		return (*RspGetChatShareRecord)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInRpc() any }); ok {
		y.ShareDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInRpc() any }); ok {
		y.ExpireDate = v.MaskInRpc().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInRpc() any }); ok {
		y.LastAccessTime = v.MaskInRpc().(*timestamppb.Timestamp)
	}

	return y
}

func (x *RspGetChatShareRecord) MaskInBff() any {
	if x == nil {
		return (*RspGetChatShareRecord)(nil)
	}

	y := x
	if v, ok := any(y.ShareDate).(interface{ MaskInBff() any }); ok {
		y.ShareDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.ExpireDate).(interface{ MaskInBff() any }); ok {
		y.ExpireDate = v.MaskInBff().(*timestamppb.Timestamp)
	}
	if v, ok := any(y.LastAccessTime).(interface{ MaskInBff() any }); ok {
		y.LastAccessTime = v.MaskInBff().(*timestamppb.Timestamp)
	}

	return y
}

func (x *ReqContinueChatFromShare) MaskInLog() any {
	if x == nil {
		return (*ReqContinueChatFromShare)(nil)
	}

	y := proto.Clone(x).(*ReqContinueChatFromShare)
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Messages[k] = vv.MaskInLog().(*ChatMessage)
		}
	}

	return y
}

func (x *ReqContinueChatFromShare) MaskInRpc() any {
	if x == nil {
		return (*ReqContinueChatFromShare)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Messages[k] = vv.MaskInRpc().(*ChatMessage)
		}
	}

	return y
}

func (x *ReqContinueChatFromShare) MaskInBff() any {
	if x == nil {
		return (*ReqContinueChatFromShare)(nil)
	}

	y := x
	for k, v := range y.Messages {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Messages[k] = vv.MaskInBff().(*ChatMessage)
		}
	}

	return y
}

func (x *RspListChatShares) MaskInLog() any {
	if x == nil {
		return (*RspListChatShares)(nil)
	}

	y := proto.Clone(x).(*RspListChatShares)
	for k, v := range y.Shares {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Shares[k] = vv.MaskInLog().(*ChatShare)
		}
	}

	return y
}

func (x *RspListChatShares) MaskInRpc() any {
	if x == nil {
		return (*RspListChatShares)(nil)
	}

	y := x
	for k, v := range y.Shares {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Shares[k] = vv.MaskInRpc().(*ChatShare)
		}
	}

	return y
}

func (x *RspListChatShares) MaskInBff() any {
	if x == nil {
		return (*RspListChatShares)(nil)
	}

	y := x
	for k, v := range y.Shares {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Shares[k] = vv.MaskInBff().(*ChatShare)
		}
	}

	return y
}

func (x *RspListChatShareAccesses) MaskInLog() any {
	if x == nil {
		return (*RspListChatShareAccesses)(nil)
	}

	y := proto.Clone(x).(*RspListChatShareAccesses)
	for k, v := range y.Accesses {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Accesses[k] = vv.MaskInLog().(*ChatShareAccess)
		}
	}

	return y
}

func (x *RspListChatShareAccesses) MaskInRpc() any {
	if x == nil {
		return (*RspListChatShareAccesses)(nil)
	}

	y := x
	for k, v := range y.Accesses {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Accesses[k] = vv.MaskInRpc().(*ChatShareAccess)
		}
	}

	return y
}

func (x *RspListChatShareAccesses) MaskInBff() any {
	if x == nil {
		return (*RspListChatShareAccesses)(nil)
	}

	y := x
	for k, v := range y.Accesses {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Accesses[k] = vv.MaskInBff().(*ChatShareAccess)
		}
	}

	return y
}

func (x *ReqUpdateMessageDocs) MaskInLog() any {
	if x == nil {
		return (*ReqUpdateMessageDocs)(nil)
	}

	y := proto.Clone(x).(*ReqUpdateMessageDocs)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ReqUpdateMessageDocs) MaskInRpc() any {
	if x == nil {
		return (*ReqUpdateMessageDocs)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ReqUpdateMessageDocs) MaskInBff() any {
	if x == nil {
		return (*ReqUpdateMessageDocs)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageDocSnapshot) MaskInLog() any {
	if x == nil {
		return (*RspDescribeMessageDocSnapshot)(nil)
	}

	y := proto.Clone(x).(*RspDescribeMessageDocSnapshot)
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInLog() any }); ok {
			y.Docs[k] = vv.MaskInLog().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageDocSnapshot) MaskInRpc() any {
	if x == nil {
		return (*RspDescribeMessageDocSnapshot)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInRpc() any }); ok {
			y.Docs[k] = vv.MaskInRpc().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *RspDescribeMessageDocSnapshot) MaskInBff() any {
	if x == nil {
		return (*RspDescribeMessageDocSnapshot)(nil)
	}

	y := x
	for k, v := range y.Docs {
		if vv, ok := any(v).(interface{ MaskInBff() any }); ok {
			y.Docs[k] = vv.MaskInBff().(*ChatMessageDoc)
		}
	}

	return y
}

func (x *ReqCreateChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqPublishChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqSendMessageSync) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSendMessageSync) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSendMessagesSync) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqSendMessageWithoutSaveSync) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSendMessageWithoutSaveSync) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeMessageByQuestionId) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateTextFileInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDeleteDocInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqReparseTextFiles) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqOnOffDocInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspOnOffDocInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.PreRepeatCollections {
		if sanitizer, ok := any(x.PreRepeatCollections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.RepeatCollections {
		if sanitizer, ok := any(x.RepeatCollections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ExceedQaContainsMatchLimit {
		if sanitizer, ok := any(x.ExceedQaContainsMatchLimit[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateTextFileInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedAssistant {
		if sanitizer, ok := any(x.SharedAssistant[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UpdateBy {
		if sanitizer, ok := any(x.UpdateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Search).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.TenantCond).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.SharedState).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.SharedReceivers).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CreateBy {
		if sanitizer, ok := any(x.CreateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.TipFilter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListTextFile_TenantCond) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListTextFile_TipFilter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WarningGroup).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ListDocSharedFileter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListTextFile) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ContributorFilter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *OperatorFilter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.UpdateBy {
		if sanitizer, ok := any(x.UpdateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Search).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.TenantCond).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.SharedState).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.SharedReceivers).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.CreateBy {
		if sanitizer, ok := any(x.CreateBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.TipFilter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListQA_TenantCond) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListQA_TipFilter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.WarningGroup).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQA) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Item).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateQAInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SharedAssistant {
		if sanitizer, ok := any(x.SharedAssistant[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateChatMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CollectionSnapshot).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateQA) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.States {
		if sanitizer, ok := any(x.States[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateQAInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeUserChats) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chat {
		if sanitizer, ok := any(x.Chat[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspSearchCollectionOneShot) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.SearchItems {
		if sanitizer, ok := any(x.SearchItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SearchTextItems {
		if sanitizer, ok := any(x.SearchTextItems[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspSearchCollection) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListCollection) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Collections {
		if sanitizer, ok := any(x.Collections[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListContributor) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Or).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListContributor_OrGroup) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListContributor) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ContributorMap {
		if sanitizer, ok := any(x.ContributorMap[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListContributorSlice) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributors {
		if sanitizer, ok := any(x.Contributors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListUpdateBy) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Or).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListUpdateBy_OrGroup) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListUpdateBy) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Operators {
		if sanitizer, ok := any(x.Operators[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListSharedAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListSharedAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqValidateQAInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspValidateQAInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Errors {
		if sanitizer, ok := any(x.Errors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqValidateTextFileInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspValidateTextFileInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Errors {
		if sanitizer, ok := any(x.Errors[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCloneDocInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateSystemDocCopy) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqEnableSystemDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDisableSystemDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDeleteSystemDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpsertUserFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.References {
		if sanitizer, ok := any(x.References[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpsertOpFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.OpComment).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpsertMgmtFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.MgmtFeedback).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.MgmtComment).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetFeedbacks) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Relation).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetFeedbacks_Filter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetFeedbacks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Feedbacks {
		if sanitizer, ok := any(x.Feedbacks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqAcceptFeedback) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetFeedbackLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Relation).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetFeedbackLogs_Filter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateIdentity).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetFeedbackLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListChat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.UpdateDateRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.OrderByLabel).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListChat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chats {
		if sanitizer, ok := any(x.Chats[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetChatDetail) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ChatDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeMessageDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeMessageLog) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeMessageLog) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.MessageLogs {
		if sanitizer, ok := any(x.MessageLogs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeSuggestLog) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocByRef) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocByRef_Doc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListCustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListAssistantCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListTeamCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Teams {
		if sanitizer, ok := any(x.Teams[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListUserCanShareDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListSharedTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqListSharedUser) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqCreateDocShareConfigReceiverAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateDocShareConfigReceiverUserTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListDocShareConfigReceiverUserTeam) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserShares {
		if sanitizer, ok := any(x.UserShares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateDocShare) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Operator).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListExternalSourceUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Users {
		if sanitizer, ok := any(x.Users[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeTencentDocList) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspReimportTencentDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Failed {
		if sanitizer, ok := any(x.Failed[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspReimportTencentDoc_FailInfo) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.User).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqImportTencentDocWebClip) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AfterTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspImportTencentDocWebClip) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeDocTab) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tabs {
		if sanitizer, ok := any(x.Tabs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeNebulaTaskList) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeNebulaTaskList) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListCustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpsertCustomLabels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Admins {
		if sanitizer, ok := any(x.Admins[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Order).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAssistantInfoMap) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.InfoMap {
		if sanitizer, ok := any(x.InfoMap[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantInfoMap_Info) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqUpdateCustomChatLabels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqListChatLiveAgent) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspListChatLiveAgent) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ChatLiveAgents {
		if sanitizer, ok := any(x.ChatLiveAgents[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetAnswerWechat) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.AnswerStartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.AnswerEndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CollectionSnapshot).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAnswerWechat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Answers {
		if sanitizer, ok := any(x.Answers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.AnswerSharding {
		if sanitizer, ok := any(x.AnswerSharding[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateChatMessageStateWechat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Infos {
		if sanitizer, ok := any(x.Infos[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateChatMessageStateWechat_Info) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetChatWechat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ChatMap {
		if sanitizer, ok := any(x.ChatMap[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateChatWechat) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chats {
		if sanitizer, ok := any(x.Chats[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateSendRecord) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Pieces {
		if sanitizer, ok := any(x.Pieces[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.WhatsAppRecord).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateSendRecord) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateSendRecord) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeUserChatRecords) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.SendRange).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeUserChatRecords) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeAssistantMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeAssistantMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Message {
		if sanitizer, ok := any(x.Message[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqInsertAssistantMessageRecord) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Records {
		if sanitizer, ok := any(x.Records[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateMessageSuggestQuestion) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateExportTask) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Task).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeExportTasks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqConvertCustomLabel) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Target).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetCustomLabelValueTopN) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Values {
		if sanitizer, ok := any(x.Values[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeExportChatMessages) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ExportChats {
		if sanitizer, ok := any(x.ExportChats[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeExportChatMessages) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.ExportMessages {
		if sanitizer, ok := any(x.ExportMessages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.OrderBy {
		if sanitizer, ok := any(x.OrderBy[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.Relation).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetAssistants_Filter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqBatchCreateAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Configs {
		if sanitizer, ok := any(x.Configs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqBatchUpdateAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqBatchUpdateAssistant_Item) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Config).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.OldUserLabelConfig).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqDeleteAssistant) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	switch oneof := x.Condition.(type) {
	case *ReqDeleteAssistant_ByAssistantId:
		if sanitizer, ok := any(oneof.ByAssistantId).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.Condition = oneof
	}
}

func (x *ReqGetAssistantLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetAssistantLogs_Filter) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAssistantLogs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Logs {
		if sanitizer, ok := any(x.Logs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantOptions) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.InteractiveCode {
		if sanitizer, ok := any(x.InteractiveCode[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.VisibleChain {
		if sanitizer, ok := any(x.VisibleChain[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.EmbeddingModel {
		if sanitizer, ok := any(x.EmbeddingModel[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.ChatModelV2 {
		if sanitizer, ok := any(x.ChatModelV2[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.SearchEngineV2 {
		if sanitizer, ok := any(x.SearchEngineV2[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.QuickActions {
		if sanitizer, ok := any(x.QuickActions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCheckAssistantAllowlist) SanitizeXSS() {
	if x == nil {
		return
	}

	switch oneof := x.TypePara.(type) {
	case *ReqCheckAssistantAllowlist_PhoneTypePara:
		if sanitizer, ok := any(oneof.PhoneTypePara).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.TypePara = oneof
	}
}

func (x *ReqGetDocChunks) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Admin).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetDocChunks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.AssistantChunks {
		if sanitizer, ok := any(x.AssistantChunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	switch oneof := x.ChunkPara.(type) {
	case *ReqChunkDoc_AutoPara:
		if sanitizer, ok := any(oneof.AutoPara).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.ChunkPara = oneof
	case *ReqChunkDoc_ManualPara:
		if sanitizer, ok := any(oneof.ManualPara).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
		x.ChunkPara = oneof
	}
}

func (x *RspChunkDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Chunks {
		if sanitizer, ok := any(x.Chunks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetChunkDocTasks) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetDocEmbeddingModels) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.EmbeddingModels {
		if sanitizer, ok := any(x.EmbeddingModels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqResendMessageSync) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspResendMessageSync) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Message {
		if sanitizer, ok := any(x.Message[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateDocAttrInBulk) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Mask).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Contributor {
		if sanitizer, ok := any(x.Contributor[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.UpdateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Reference {
		if sanitizer, ok := any(x.Reference[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Labels {
		if sanitizer, ok := any(x.Labels[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqDescribeMessageMatchQa) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Filter).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspDescribeMessageMatchQa) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateQaMatchMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Question).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CollectionSnapshot).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Task).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspCreateQaMatchMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetRecentlyUsedAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetRecentlyUsedAssistants) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Assistants {
		if sanitizer, ok := any(x.Assistants[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetAssistantAdmin) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Page).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetAssistantAdmin) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Admins {
		if sanitizer, ok := any(x.Admins[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantAdmin_Info) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Admins {
		if sanitizer, ok := any(x.Admins[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetAssistantChatUser) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.UserInfo {
		if sanitizer, ok := any(x.UserInfo[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqGetTextFileTip) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetTextFileTip) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.TableOverSize {
		if sanitizer, ok := any(x.TableOverSize[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateDocQuery) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Doc).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Qa).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.CreateBy).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqGetQaTip) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Contributor).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqParseChatDoc) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeChatQuestionAnswersByPage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Questions {
		if sanitizer, ok := any(x.Questions[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
	for k := range x.Answers {
		if sanitizer, ok := any(x.Answers[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqCreateChatTaskMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.AssistantDetail).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspCreateChatTaskMessage) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeChatAgentTask) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqFixSearchCollectionItems) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspFixSearchCollectionItems) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateChatMessageCollections) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.StartTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.EndTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	for k := range x.Items {
		if sanitizer, ok := any(x.Items[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeChatAgentTaskByPreTask) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Tasks {
		if sanitizer, ok := any(x.Tasks[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeChatMessageFileState) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Files {
		if sanitizer, ok := any(x.Files[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspStopQuestionReply) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.Message).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *RspGetChatShareMessages) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspGetChatShareRecord) SanitizeXSS() {
	if x == nil {
		return
	}

	if sanitizer, ok := any(x.ShareDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.ExpireDate).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
	if sanitizer, ok := any(x.LastAccessTime).(interface{ SanitizeXSS() }); ok {
		sanitizer.SanitizeXSS()
	}
}

func (x *ReqContinueChatFromShare) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Messages {
		if sanitizer, ok := any(x.Messages[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListChatShares) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Shares {
		if sanitizer, ok := any(x.Shares[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspListChatShareAccesses) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Accesses {
		if sanitizer, ok := any(x.Accesses[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *ReqUpdateMessageDocs) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}

func (x *RspDescribeMessageDocSnapshot) SanitizeXSS() {
	if x == nil {
		return
	}

	for k := range x.Docs {
		if sanitizer, ok := any(x.Docs[k]).(interface{ SanitizeXSS() }); ok {
			sanitizer.SanitizeXSS()
		}
	}
}
