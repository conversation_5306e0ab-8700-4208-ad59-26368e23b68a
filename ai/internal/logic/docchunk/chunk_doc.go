package docchunk

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"
	"unicode/utf8"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xcrypto"
	"e.coding.net/tencent-ssv/tanlive/gokits/xstrings"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/client"
	tablechunk "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	basepb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ChunkDocLogic 分段逻辑
type ChunkDocLogic struct {
	chunkLogic
}

// GetDoc 查询文档
func (l *ChunkDocLogic) GetDoc(ctx context.Context, docID uint64, newText string) (*model.TDoc, bool, error) {
	// 如果有NewText就不查询IndexText了
	withText := len(newText) == 0
	doc, err := l.queryDoc(ctx, docID, withText)
	if err != nil {
		return nil, false, err
	}

	// 如果NewText与旧版的md5一致，强制将NewText置为空，表示不更新IndexText
	if len(newText) > 0 {
		doc.IndexText = newText
		if newMd5 := util.CalculateMD5String(newText); newMd5 == doc.IndexTextMd5 {
			newText = ""
		} else {
			doc.IndexTextMd5 = newMd5
		}
	}

	return doc, len(newText) > 0, nil
}

// AutoChunk 自动分段
func (l *ChunkDocLogic) AutoChunk(ctx context.Context,
	doc *model.TDoc, para *aipb.AutoChunkPara, createBy *basepb.Identity) ([]*aipb.ChunkItem, error) {
	if para.DryRun {
		return l.dryRunAutoChunk(ctx, doc, para, createBy)
	}
	return nil, xerrors.ValidationError("auto chunk supports preview only")
}

func (l *ChunkDocLogic) dryRunAutoChunk(ctx context.Context,
	doc *model.TDoc, para *aipb.AutoChunkPara, createBy *basepb.Identity) ([]*aipb.ChunkItem, error) {
	assistantID := para.AssistantId[0]
	assistant, err := l.queryAssistant(ctx, assistantID, doc.ID, createBy)
	if err != nil {
		return nil, err
	}
	collection := assistant.AssistantCollections[0].Collection

	minChunkSize, maxChunkSize, overlapSize := model.CalcDocChunkPara(collection.Lang, para.ChunkConfig)
	rsp, err := client.GetRagClient().CreateCollection(ctx, &rag.ReqCreateCollection{
		FullText:         doc.IndexText,
		FileName:         doc.RagFilename,
		CollectionName:   collection.RagName,
		VdbType:          collection.VdbType,
		Lang:             collection.Lang,
		ReturnIds:        true,
		ReturnChunksOnly: true,
		ChunkMinSize:     minChunkSize,
		ChunkSize:        maxChunkSize,
		ChunkOverlapSize: overlapSize,
	})
	if err != nil {
		return nil, err
	}

	chunks := rsp.Data.Chunks
	if len(chunks) == 0 || len(chunks[0]) < 2 {
		return nil, fmt.Errorf("auto chunk result: no chunks")
	}

	return l.matchPreviewChunksPosition(doc, chunks[0][1:]), nil
}

// ManualChunk 手动分段
func (l *ChunkDocLogic) ManualChunk(ctx context.Context, doc *model.TDoc,
	newText bool, para *aipb.ManualChunkPara, createBy *basepb.Identity) (*model.TDocChunkTask, error) {
	if err := l.EnsureDocHasNoRunningTask(ctx, doc.ID); err != nil {
		return nil, err
	}

	assistantIDs, assistantChunksSet, err := l.mergeAssistantChunks(ctx, doc, newText, para.AssistantChunks)
	if err != nil {
		return nil, err
	}

	assistantDocs, err := model.NewQuery[model.TAssistantDoc](ctx).
		GetBy("doc_id = ? AND assistant_id IN (?)", doc.ID, assistantIDs)
	if err != nil {
		return nil, fmt.Errorf("query assistant docs: %w", err)
	}
	assistantDocsMap := make(map[uint64]*model.TAssistantDoc)
	for _, ad := range assistantDocs {
		assistantDocsMap[ad.AssistantID] = ad
	}

	var (
		errs                 = make([]error, len(assistantIDs))
		assistantSubtasksSet = make([][]*model.TDocChunkSubtask, len(assistantIDs))
		docChunksSet         = make([]*model.TDocChunkDetail, 0, len(assistantChunksSet))
		index                int
		subtaskCount         int
	)

	g := xsync.NewGroup(ctx, xsync.GroupGoOption(boot.TraceGo(ctx)))
	for _, assistantChunks := range assistantChunksSet {
		for _, assistantID := range assistantChunks.AssistantIds {
			i, assistantID, chunks := index, assistantID, assistantChunks.Chunks
			g.Go(func(ctx context.Context) error {
				ad := assistantDocsMap[assistantID]
				if ad == nil {
					ad = &model.TAssistantDoc{
						DocID:       doc.ID,
						AssistantID: assistantID,
					}
					assistantDocsMap[assistantID] = ad
				}

				// 仅启用的助手创建子任务，未启用的仅需更新数据库里的分段详情
				if ad.State == aipb.DocState_DOC_STATE_ENABLED {
					assistantSubtasksSet[i], errs[i] = l.newManualChunkSubtasks(ctx, doc, newText, assistantID, chunks, createBy)
				}

				docChunks := &model.TDocChunkDetail{
					Sum:   assistantChunks.Sum,
					Items: assistantChunks.Chunks,
				}

				ad.ChunkDetail = docChunks
				docChunksSet = append(docChunksSet, docChunks)
				subtaskCount += len(assistantSubtasksSet[i])

				return nil
			})
			index += 1
		}
	}
	g.Wait()

	for _, err := range errs {
		if err != nil {
			return nil, err
		}
	}

	subtasks := make([]*model.TDocChunkSubtask, 0, subtaskCount)
	for _, assistantSubtasks := range assistantSubtasksSet {
		subtasks = append(subtasks, assistantSubtasks...)
	}

	var task *model.TDocChunkTask
	if len(subtasks) > 0 {
		task = &model.TDocChunkTask{
			DocID: doc.ID,
			State: aipb.DocChunkTaskState_DOC_CHUNK_TASK_STATE_RUNNING,
			Type:  aipb.DocChunkTaskType_DOC_CHUNK_TASK_TYPE_MANUAL,
			Para: &model.TDocChunkTaskPara{
				Assistants: assistantIDs,
				ManualPara: para,
			},
			SubtaskCount: subtaskCount,
			Subtasks:     subtasks,
			CreateBy:     createBy,
			CreateDate:   time.Now(),
		}
	}

	var docFields []string
	if newText {
		doc.UpdateBy = createBy.IdentityId
		doc.UpdateByUser = createBy.ExtraId
		doc.UpdateByType = createBy.IdentityType
		doc.UpdateDate = time.Now()
		docFields = []string{
			"update_by", "update_by_user", "update_by_type", "update_date", "index_text", "index_text_md5",
		}

		if doc.State == aipb.DocState_DOC_STATE_PARES_FAILED {
			doc.State = aipb.DocState_DOC_STATE_DISABLED
			docFields = append(docFields, "state")
		}
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		// 指定批量插入子任务数量
		tx.CreateBatchSize = 200

		// 更新文档
		if len(docFields) > 0 {
			if err := tx.Select(docFields).Save(doc).Error; err != nil {
				return fmt.Errorf("update doc: %w", err)
			}
		}

		// 更新助手文档分段详情ID
		if err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "sum"}},
			DoUpdates: clause.AssignmentColumns([]string{"items"}),
		}).Create(docChunksSet).Error; err != nil {
			return fmt.Errorf("upsert doc_chunk_detail: %w", err)
		}

		for _, ad := range assistantDocsMap {
			if err := l.updateChunkDetailID(tx, ad, ad.ChunkDetail); err != nil {
				return err
			}
		}

		// 创建任务
		if task != nil {
			if err := tx.Create(task).Error; err != nil {
				return fmt.Errorf("create doc chunk task: %w", err)
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	// 执行表格提取
	if newText {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			err = tablechunk.NewDocTableLogic(model.NewConnection(ctx)).StoreOversizedTablesAllAssistant(ctx, doc.ID)
			if err != nil {
				log.WithContext(ctx).Errorw("manual chunk process doc tables all assistant failed", "doc_id", doc.ID, "error", err)
				return err
			}
			return nil
		}, boot.TraceGo(ctx))
	}

	// 触发子任务
	if task != nil {
		for _, subtask := range task.Subtasks {
			if subtask.State != aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_ACTIVATED {
				continue
			}

			subtaskID := subtask.ID
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				if err := new(ChunkTaskLogic).RunSubtask(ctx, subtaskID); err != nil {
					log.WithContext(ctx).Errorw("run subtask failed", "subtask_id", subtaskID, "error", err)
				}
				return nil
			}, boot.TraceGo(ctx))
		}
	}

	return task, nil
}

func (l *ChunkDocLogic) mergeAssistantChunks(ctx context.Context, doc *model.TDoc, newText bool, assistantChunks []*aipb.AssistantChunks) ([]uint64, []*model.AssistantChunks, error) {
	assistantSet := make(map[uint64]bool)
	assistantIdSet := make([]uint64, 0)
	sumSet := make(map[string]*aipb.AssistantChunks, len(assistantChunks))

	for _, item := range assistantChunks {
		// 助手ID不允许重复
		assistantIDs := make([]uint64, 0, len(item.AssistantId))
		for _, assistantID := range item.AssistantId {
			if assistantSet[assistantID] {
				return nil, nil, xerrors.ValidationError("duplicate assistant_id")
			}
			assistantSet[assistantID] = true
			assistantIDs = append(assistantIDs, assistantID)
			assistantIdSet = append(assistantIdSet, assistantID)
		}
		item.AssistantId = assistantIDs

		chunks := sortAndUniqueChunks(item.Chunks)
		sum := calcJsonSum(chunks)
		if sumSet[sum] != nil {
			sumSet[sum].AssistantId = append(sumSet[sum].AssistantId, item.AssistantId...)
		} else {
			sumSet[sum] = item
		}
	}

	// 如果更新原文，需要确保所有助手的分段都被更新
	if newText {
		var allAssistantIDs []uint64
		if err := model.NewConnection(ctx).Model(&model.TAssistantDoc{}).
			Where("doc_id = ? AND chunk_detail_id > 0", doc.ID).
			Distinct().Pluck("assistant_id", &allAssistantIDs).Error; err != nil {
			return nil, nil, fmt.Errorf("query all assistant ids: %w", err)
		}
		if len(allAssistantIDs) != len(assistantIdSet) {
			return nil, nil, xerrors.ValidationError("not full assistants")
		}
	}

	merged := make([]*model.AssistantChunks, 0, len(sumSet))
	mergedSet := make(map[string]*model.AssistantChunks, len(sumSet))

	for _, item := range sumSet {
		chunks := toChunkItems(doc.RagFilename, doc.IndexText, item.Chunks)
		sum := calcJsonSum(chunks)

		if existed := mergedSet[sum]; existed != nil {
			existed.AssistantIds = append(existed.AssistantIds, item.AssistantId...)
		} else {
			ac := &model.AssistantChunks{
				AssistantIds: item.AssistantId,
				Chunks:       chunks,
				Sum:          sum,
			}
			merged = append(merged, ac)
			mergedSet[sum] = ac
		}
	}

	return assistantIdSet, merged, nil
}

func (l *ChunkDocLogic) newManualChunkSubtasks(ctx context.Context, doc *model.TDoc, newText bool,
	assistantID uint64, chunkItems []*model.ChunkItem, admin *basepb.Identity) ([]*model.TDocChunkSubtask, error) {
	// 更新原文时会应用到所有助手，因此无需校验和admin的关系
	if newText {
		admin = nil
	}

	// 查询助手，确保和文档的关系、和管理员的关系正确
	assistant, err := l.queryAssistant(ctx, assistantID, doc.ID, admin)
	if err != nil {
		return nil, err
	}
	if len(assistant.AssistantCollections) == 0 || assistant.AssistantCollections[0].Collection == nil {
		return nil, fmt.Errorf("assistant [%d] has no collection", assistantID)
	}
	collection := assistant.AssistantCollections[0].Collection

	// 查询真实分段信息
	_, oldChunkIDs, err := l.fetchDocChunks(ctx, doc, collection)
	if err != nil {
		return nil, err
	}
	oldChunkIDMap := make(map[string]bool)
	for _, chunkID := range oldChunkIDs {
		oldChunkIDMap[chunkID] = true
	}

	var (
		subtasks      []*model.TDocChunkSubtask
		deleteChunks  []string
		creates       = make(map[string]bool)
		newChunkIDMap = make(map[string]bool)
	)

	// 新建分段
	for _, chunkItem := range chunkItems {
		newChunkIDMap[chunkItem.ChunkId] = true
		if !oldChunkIDMap[chunkItem.ChunkId] {
			if creates[chunkItem.ChunkId] {
				continue
			}
			subtasks = append(subtasks, l.newCreateChunkSubtask(doc, assistant, collection, chunkItem))
			creates[chunkItem.ChunkId] = true
		}
	}

	// 删除分段
	for _, chunkID := range oldChunkIDs {
		if !newChunkIDMap[chunkID] {
			deleteChunks = append(deleteChunks, chunkID)
		}
	}
	if len(deleteChunks) > 0 {
		subtasks = append(subtasks, l.newDeleteChunkSubtask(doc, assistant, collection, deleteChunks))
	}

	return subtasks, nil
}

func (l *ChunkDocLogic) newCreateChunkSubtask(doc *model.TDoc, assistant *model.TAssistant, collection *model.TCollection, chunk *model.ChunkItem) *model.TDocChunkSubtask {
	para, _ := json.Marshal(&rag.ReqEditCollection{
		CollectionName: collection.RagName,
		Text:           chunk.Content,
		DocName:        doc.RagFilename,
		Threshold:      2, // 大于1时不分片
		VdbType:        collection.VdbType,
		Lang:           collection.Lang,
		EsIns:          collection.EsIns,
	})

	return &model.TDocChunkSubtask{
		DocID:        doc.ID,
		AssistantID:  assistant.ID,
		CollectionID: collection.ID,
		Type:         aipb.DocChunkSubtaskType_DOC_CHUNK_SUBTASK_TYPE_CREATE_CHUNK,
		State:        aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_ACTIVATED,
		Para:         para,
		CreateDate:   time.Now(),
	}
}

func (l *ChunkDocLogic) newDeleteChunkSubtask(doc *model.TDoc, assistant *model.TAssistant, collection *model.TCollection, deleteChunks []string) *model.TDocChunkSubtask {
	para, _ := json.Marshal(&rag.ReqDeleteVecById{
		CollectionName: collection.RagName,
		EsIns:          collection.EsIns,
		VecIds:         deleteChunks,
	})

	return &model.TDocChunkSubtask{
		DocID:        doc.ID,
		AssistantID:  assistant.ID,
		CollectionID: collection.ID,
		Type:         aipb.DocChunkSubtaskType_DOC_CHUNK_SUBTASK_TYPE_DELETE_CHUNK,
		State:        aipb.DocChunkSubtaskState_DOC_CHUNK_SUBTASK_STATE_ACTIVATED,
		Para:         para,
		CreateDate:   time.Now(),
	}
}

// EnsureDocHasNoRunningTask 确保文档没有运行中的任务
func (l *ChunkDocLogic) EnsureDocHasNoRunningTask(ctx context.Context, docID uint64) error {
	cnt, err := model.NewQuery[model.TDocChunkTask](ctx).Limit(1).
		CountBy("doc_id = ? and state = ?", docID, aipb.DocChunkTaskState_DOC_CHUNK_TASK_STATE_RUNNING)
	if err != nil {
		return fmt.Errorf("count running chunk task: %w", err)
	}

	if cnt > 0 {
		return xerrors.NewCode(errorspb.AiError_AiDocHasRunningChunkTask)
	}
	return nil
}

func toChunkItems(filename, text string, chunks []*aipb.ChunkItem) []*model.ChunkItem {
	chunks = sortAndUniqueChunks(chunks)
	items := make([]*model.ChunkItem, 0, len(chunks))

	var (
		textCopy   = text
		size       int
		runeIndex  int // rune索引
		charIndex  int // char索引
		chunkIndex int // chunk索引
	)

	// 存放滑动中的chunk
	slides := linkedList[*model.ChunkItem]{}

	for {
		if _, size = utf8.DecodeRuneInString(textCopy); size == 0 {
			break
		}

		for ; chunkIndex < len(chunks); chunkIndex++ {
			if runeStart := int(chunks[chunkIndex].Start); runeIndex == runeStart {
				slides.append(&model.ChunkItem{
					RuneStart: runeIndex,
					RuneLen:   int(chunks[chunkIndex].Len),
					CharStart: charIndex,
				})
			} else if runeIndex < runeStart {
				// 因为chunks是有序的，索引未到达最近一个chunk的开始位置可直接退出
				break
			}
		}

		// 判断遍历链表中元素的rune长度，如果已达到item的长度，表示已匹配完内容
		slides.foreach(func(n *linkedListNode[*model.ChunkItem]) {
			// 借用CharLen字段表示rune滑动长度
			n.value.CharLen += 1

			if n.value.CharLen == n.value.RuneLen {
				n.value.CharLen = charIndex + size - n.value.CharStart
				n.value.Content = text[n.value.CharStart : charIndex+size]
				n.value.ChunkId = rag.NewChunkId(filename, n.value.Content)

				items = append(items, n.value)
				slides.remove(n)
			}
		})

		runeIndex += 1
		charIndex += size
		textCopy = textCopy[size:]
	}

	// 如果slides中还有未处理的chunks，说明这部分chunks的RuneStart+RuneLen超出了整体text的长度，
	// 因此需要将这部分chunks的文本截取到text末尾
	if slides.length > 0 {
		restChunks := make([]*model.ChunkItem, 0, slides.length)
		slides.foreach(func(n *linkedListNode[*model.ChunkItem]) {
			n.value.RuneLen = n.value.CharLen
			n.value.CharLen = len(text) - n.value.CharStart
			n.value.Content = text[n.value.CharStart:]
			n.value.ChunkId = rag.NewChunkId(filename, n.value.Content)

			restChunks = append(restChunks, n.value)
		})
		items = append(items, uniqueSortedChunks(restChunks)...)
	}

	return items
}

func uniqueSortedChunks(chunks []*model.ChunkItem) []*model.ChunkItem {
	var prev *model.ChunkItem
	set := make([]*model.ChunkItem, 0, len(chunks))
	for _, chunk := range chunks {
		// 如果数据相同就替换，确保数据是以最后一个为准
		if prev != nil && prev.CharStart == chunk.CharStart && prev.CharLen == chunk.CharLen {
			set[len(set)-1] = chunk
		} else {
			set = append(set, chunk)
		}
		prev = chunk
	}
	return set
}

func sortAndUniqueChunks(chunks []*aipb.ChunkItem) []*aipb.ChunkItem {
	sort.Slice(chunks, func(i, j int) bool {
		if chunks[i].Start == chunks[j].Start {
			return chunks[i].Len <= chunks[j].Len
		}
		return chunks[i].Start < chunks[j].Start
	})

	var prev *aipb.ChunkItem
	set := make([]*aipb.ChunkItem, 0, len(chunks))
	for _, chunk := range chunks {
		// content置空，确保计算sum无偏差
		chunk.Content = ""

		// 如果数据相同就替换，确保数据是以最后一个为准
		if prev != nil && prev.Start == chunk.Start && prev.Len == chunk.Len {
			set[len(set)-1] = chunk
		} else {
			set = append(set, chunk)
		}
		prev = chunk
	}

	return set
}

func calcJsonSum[T any](items T) string {
	s, _ := json.Marshal(items)
	return xcrypto.Md5(xstrings.FromBytes(s))
}

// CreateDocChunks 创建文档时的分段
// 由于es的shard同步存在延迟，且分段数量越大延迟越明显。如果查询到的分段数量小于分段总数时，间隔一段时间后多重试几次
func (l *ChunkDocLogic) CreateDocChunks(ctx context.Context, doc *model.TDoc, collection *model.TCollection, assistant *model.TAssistant, chunkCount, maxTries int, delay time.Duration) error {
	var (
		err    error
		chunks []*rag.ChunkInfo
		tried  int
	)

	log.WithContext(ctx).Infow("StartCreateDocChunks",
		"filename", doc.RagFilename, "collection", collection.RagName,
		"chunk_count", chunkCount, "max_tries", maxTries, "delay", delay)

	for {
		chunks, _, err = l.fetchDocChunks(ctx, doc, collection)
		if err != nil {
			return err
		}
		chunks = l.uniqueDocChunks(chunks)

		log.WithContext(ctx).Infow("GetCollection",
			"filename", doc.RagFilename, "collection", collection.RagName,
			"total_chunks", len(chunks))

		if tried++; tried < maxTries && len(chunks) < chunkCount {
			log.WithContext(ctx).Infow("RetryCreateDocChunks",
				"filename", doc.RagFilename, "collection", collection.RagName,
				"tried", tried, "sleep", delay.String(),
				"expect_chunks", chunkCount, "real_chunks", len(chunks))
			if delay > 0 {
				time.Sleep(delay)
			}
			continue
		}

		if err = l.updateDocChunks(ctx, chunks, doc, collection, assistant); err != nil {
			return err
		}

		break
	}

	return nil
}

// UpdateDocChunks 更新文档的分段信息
func (l *ChunkDocLogic) UpdateDocChunks(ctx context.Context, doc *model.TDoc, collection *model.TCollection, assistant *model.TAssistant) error {
	chunks, _, err := l.fetchDocChunks(ctx, doc, collection)
	if err != nil {
		return err
	}

	if len(chunks) == 0 {
		return nil
	}

	return l.updateDocChunks(ctx, chunks, doc, collection, assistant)
}

func (l *ChunkDocLogic) updateDocChunks(ctx context.Context, chunks []*rag.ChunkInfo, doc *model.TDoc, collection *model.TCollection, assistant *model.TAssistant) error {
	text := doc.IndexText

	items := make([]*model.ChunkItem, 0, len(chunks))
	var unmatched []string
	for _, chunk := range chunks {
		runeStart, runeLen, charStart, charLen, found := calcSubstringPosition(text, chunk.Content)
		if !found {
			unmatched = append(unmatched, chunk.Content)
			continue
		}

		items = append(items, &model.ChunkItem{
			ChunkId:   chunk.Id,
			RuneStart: runeStart,
			RuneLen:   runeLen,
			CharStart: charStart,
			CharLen:   charLen,
			Content:   chunk.Content,
		})
	}

	log.WithContext(ctx).Infow("FilteredDocChunks",
		"filename", doc.RagFilename,
		"collection", collection.RagName,
		"total_chunks", len(items),
		"unmatched_count", len(unmatched),
		"unmatched_items", unmatched,
	)

	if len(items) == 0 {
		return nil
	}

	items = l.sortChunksModel(items)
	sum := calcJsonSum(items)

	existedChunkDetail, err := model.NewQuery[model.TDocChunkDetail](ctx).Select("id", "sum").FindBy("sum = ?", sum)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("find existed chunk detail: %w", err)
		}
	}

	var chunkDetail *model.TDocChunkDetail
	if existedChunkDetail != nil {
		chunkDetail = existedChunkDetail
	} else {
		chunkDetail = &model.TDocChunkDetail{
			Sum:   sum,
			Items: items,
		}
		if err = model.NewConnection(ctx).Create(chunkDetail).Error; err != nil {
			return fmt.Errorf("upsert doc chunk detail: %w", err)
		}
	}

	ad := &model.TAssistantDoc{
		DocID:       doc.ID,
		AssistantID: assistant.ID,
	}
	if err := l.updateChunkDetailID(model.NewConnection(ctx), ad, chunkDetail); err != nil {
		return err
	}

	return nil
}

func (l *ChunkDocLogic) updateChunkDetailID(tx *gorm.DB, ad *model.TAssistantDoc, cd *model.TDocChunkDetail) error {
	const updateChunkDetailIdSql = "UPDATE `t_assistant_doc` SET `chunk_detail_id` = " +
		"(SELECT `id` FROM `t_doc_chunk_detail` WHERE `sum` = ?) " +
		"WHERE `doc_id` = ? AND `assistant_id` = ?"

	if err := tx.Exec(updateChunkDetailIdSql,
		cd.Sum, ad.DocID, ad.AssistantID).Error; err != nil {
		return fmt.Errorf("update chunk_detail_id: %w", err)
	}

	return nil
}
