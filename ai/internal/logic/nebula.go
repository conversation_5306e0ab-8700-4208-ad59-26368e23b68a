package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xredis"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/gorm"
)

// CreateNebulaTask 创建nebula任务
func CreateNebulaTask(ctx context.Context, req *aipb.ReqCreateNebulaTask, uuid string) error {
	var err error

	log.WithContext(ctx).Infow("CreateNebulaTask data", "uuid", uuid, "req", req)

	// 创建任务表
	task := &model.TNebulaTask{
		UUID:       uuid,
		UserID:     req.UserId,
		TeamID:     req.TeamId,
		Lang:       req.<PERSON>,
		FilterText: req.FilterText,
		AutoCreate: uint32(req.AutoCreate),
	}

	defer func() {
		if err != nil {
			xredis.Default.Set(ctx, "nebula_pod_calcing", "", 0) // 清理计算动作

			log.WithContext(ctx).Errorw("CreateNebulaTask error", "uuid", uuid, "err", err)
			_, _ = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(&model.TNebulaTask{
				State:  model.NebulaTaskStateFailed,
				ErrMsg: err.Error()}, "id = ?", task.ID)
		}
	}()
	// 对助手id和查询助手id进行排序并转换为JSON字符串
	assistantIds, err := uint64ArrayToJSON(req.AssistantId)
	if err != nil {
		return err
	}
	task.AssistantIDs = assistantIds

	queryAssistantIds, err := uint64ArrayToJSON(req.QueryAssistantId)
	if err != nil {
		return err
	}
	task.QueryAssistantIDs = queryAssistantIds

	// 如果有参数一致的且状态进行中的，则创建任务
	runningTask, err := findRunningTask(ctx, req)
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		err = errors.New("findRunningTask err: " + err.Error())
		return err
	}

	if runningTask {
		log.WithContext(ctx).Infow("CreateNebulaTask running, no create task", "req", req)
		return nil
	}

	// 通过助手id查询collectionIds
	collectionIds, err := findCollectionIdsByAssistantId(ctx, req)
	if err != nil {
		return err
	}

	// 转换创建任务请求，重置参数
	taskData, _ := transCreateTaskReqReset(ctx, req)
	task.AdminType = uint32(req.AdminType)

	err = model.NewQuery[model.TNebulaTask](ctx).Create(task)
	if err != nil {
		return err
	}

	// ==================== 新增并发控制逻辑 ====================
	maxRunningTasks := config.GetUint32Or("nebula.max_running_tasks", 4)
	maxRetries := config.GetIntOr("nebula.max_retries", 1440)
	for i := 0; i < maxRetries; i++ {

		threeHoursAgo := time.Now().Add(-3 * time.Hour)

		runningCount, err := model.NewQuery[model.TNebulaTask](ctx).
			Where("state = ?", model.NebulaTaskStateCreated).Where("create_date >= ?", threeHoursAgo).Count()

		if err != nil {
			return errors.New("get running task count error: " + err.Error())
		}

		if runningCount < maxRunningTasks {
			break
		}

		if i < maxRetries-1 {
			log.WithContext(ctx).Infow("running tasks >=4, waiting", "uuid", uuid,
				"count", runningCount, "retry", i)
			select {
			case <-time.After(60 * time.Second):
				continue
			case <-ctx.Done():
				return ctx.Err()
			}
		} else {
			return errors.New("running tasks >=4, wait timeout after  retries")
		}
	}
	// ==================== 并发控制结束 ====================

	client := NewClient

	var knowDataFile string
	var knowDataFileSize int
	if len(req.AssistantId) > 0 {
		// 1、请求collection数据，已经启用+启用knowledge的数据
		collectionPath := fmt.Sprintf("./data/data/%s/%s.json", time.Now().Format("2006-01-02"), uuid)

		request := GenerateJSONFromKnowledgeLRequest{
			CollectionID: collectionIds,
			OutputPath:   collectionPath,
			SplitParams: map[string]interface{}{
				"lang": req.Lang,
			},
		}

		if req.SelectAll {
			request.ContributorType = req.AdminType
			request.ContributorId = taskData.ContributorId
		}

		knowledges, err1 := client().GenerateJSONFromKnowledge(request)
		if err1 != nil {
			err = errors.New("GenerateJSONFromKnowledge err: " + err.Error())
			return err
		}

		_, err = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(&model.TNebulaTask{CollectionPath: collectionPath}, "id = ?", task.ID)
		if err != nil {
			err = errors.New("UpdateBy err: " + err.Error())
			return err
		}

		if knowledges.DataCount > 0 {
			// 2、知识库文本生成向量，支持重试
			stepRsp1, err1 := retryAddVecToCol(ctx, client(), uuid, collectionPath, req)
			if err1 != nil {
				err = errors.New("retryAddVecToCol err: " + err.Error())
				return err
			}

			jsonStr, err1 := json.Marshal(stepRsp1)
			if err1 != nil {
				err = errors.New("json.Marshal err: " + err.Error())
				return err
			}

			_, err = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(&model.TNebulaTask{
				ResultCollectionToVec: string(jsonStr),
				VecCollectionPath:     stepRsp1.OutputFile,
				VecFileSize:           uint32(stepRsp1.FileSizeMb),
			}, "id = ?", task.ID)
			if err != nil {
				err = errors.New("UpdateBy err: " + err.Error())
				return err
			}

			knowDataFileSize = stepRsp1.FileSizeMb
			knowDataFile = stepRsp1.OutputFile

		} else {
			log.WithContext(ctx).Infow("GenerateJSONFromKnowledge no data", "uuid", uuid)
		}

	}

	var queryDataFile string
	if len(req.QueryAssistantId) > 0 {
		// 3、处理用户查询数据，生成用户查询数据
		userdataPath := fmt.Sprintf("./data/data/%s/%s-%s.json", time.Now().Format("2006-01-02"), uuid, "querydata")
		userQAData, err1 := client().GenerateUserQAData(GenerateUserQADataRequest{
			AssistantnID: req.QueryAssistantId,
			OutputPath:   userdataPath,
		})
		if err1 != nil {
			err = errors.New("GenerateUserQAData err: " + err.Error())
			return err
		}

		// 没有用户问答数据
		if userQAData.DataCount > 0 {

			// 4、知识库文本生成向量，支持重试
			stepRsp2, err1 := retryAddVecToCol(ctx, client(), uuid, userdataPath, req)
			if err1 != nil {
				err = errors.New("retryAddVecToCol err: " + err.Error())
				return err
			}

			jsonStr, err1 := json.Marshal(stepRsp2)
			if err1 != nil {
				err = errors.New("json.Marshal err: " + err.Error())
				return err
			}

			_, err = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(&model.TNebulaTask{
				ResultQueryToVec: string(jsonStr),
				VecQueryPath:     stepRsp2.OutputFile,
				VecQueryFileSize: uint32(stepRsp2.FileSizeMb),
			}, "id = ?", task.ID)
			if err != nil {
				err = errors.New("UpdateBy err: " + err.Error())
				return err
			}

			queryDataFile = stepRsp2.OutputFile

		} else {
			log.WithContext(ctx).Infow("GenerateUserQAData no data", "uuid", uuid)
		}

	}

	updateTime := time.Now()

	// 如果knowDataFile和queryDataFile都为空，则不需要预计算
	if knowDataFile == "" && queryDataFile == "" {
		_, err = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(
			&model.TNebulaTask{
				ErrMsg:      "knowDataFile = empty && queryDataFile = empty",
				CalcuResult: "[]",
				EndDate:     &updateTime,
				UpdateDate:  time.Now(),
				State:       model.NebulaTaskStateOK,
			}, "id = ?", task.ID)
		if err != nil {
			err = errors.New("UpdateBy err: " + err.Error())
			return err
		}
		return nil
	}

	calcUmapErr := calcUmap(ctx, knowDataFile, queryDataFile, updateTime, req, task, knowDataFileSize, uuid)
	if calcUmapErr != nil {
		err = errors.New("calcUmap err: " + calcUmapErr.Error())
		return err
	}
	return nil
}

// calcUmap 计算Umap
func calcUmap(ctx context.Context, knowDataFile, queryDataFile string, updateTime time.Time,
	req *aipb.ReqCreateNebulaTask, task *model.TNebulaTask, knowDataFileSize int, uuid string) error {

	client := NewClient()

	splitSize := config.GetIntOr("nebula.split_size", 2000)
	namespace := config.GetStringOr("nebula.namespace", "tanlive-pre")
	deployment := config.GetStringOr("nebula.deployment", "tanlive-rag-calc-vis")

	// 处理普通文件情况(<=1000MB)
	if knowDataFileSize <= 1000 {
		return processUMAPTask(ctx, false, client, knowDataFile, queryDataFile,
			req, task, splitSize, uuid)
	}

	// 处理大文件情况(>1000MB)
	podSpec := xredis.Default.Get(ctx, "nebula_pod_spec").Val()
	calcingPod := xredis.Default.Get(ctx, "nebula_pod_calcing").Val()

	startFileSize := config.GetIntOr("nebula.start_file_size", 1000)
	// 情况1: 1000-4000MB且需要升级规格
	if knowDataFileSize > startFileSize && knowDataFileSize < 4000 && (podSpec != "large" && podSpec != "xlarge" && podSpec != "xxlarge") {
		return handlePodUpgrade(ctx, client, namespace, deployment, "large",
			knowDataFile, queryDataFile, req, task, splitSize, updateTime, uuid, calcingPod)
	}

	// 情况2: >4000MB且需要升级规格
	if knowDataFileSize >= 4000 && knowDataFileSize <= 8000 && (podSpec != "xlarge" && podSpec != "xxlarge") {
		return handlePodUpgrade(ctx, client, namespace, deployment, "xlarge",
			knowDataFile, queryDataFile, req, task, splitSize, updateTime, uuid, calcingPod)
	}

	// 情况3: >8000MB~且需要升级规格
	if knowDataFileSize >= 4000 && podSpec != "xxlarge" {
		return handlePodUpgrade(ctx, client, namespace, deployment, "xxlarge",
			knowDataFile, queryDataFile, req, task, splitSize, updateTime, uuid, calcingPod)
	}

	// 如果已经是大规格，直接处理任务
	return processUMAPTask(ctx, config.GetBoolOr("nebula.open_umap", false), client, knowDataFile, queryDataFile,
		req, task, splitSize, uuid)
}

// 处理UMAP任务的通用函数
func processUMAPTask(ctx context.Context, newCalc bool, client *Client,
	knowDataFile, queryDataFile string, req *aipb.ReqCreateNebulaTask,
	task *model.TNebulaTask, splitSize int, uuid string) error {

	var stepRsp3 *UMAPResultData
	var err error

	calcReq := PrecomputeUMAPRequest{
		InputFile:       knowDataFile,
		InputQueryFile:  queryDataFile,
		Lang:            req.SimplifyLang,
		SplitSize:       0,
		EnableCosUpload: false}

	if newCalc {

		defer xredis.Default.Set(ctx, "nebula_pod_calcing", "", 0) // 确保清理

		calcingPod := xredis.Default.Get(ctx, "nebula_pod_calcing").Val()
		// 如果有任务正在计算，等待
		if len(calcingPod) > 0 {
			for {
				log.WithContext(ctx).Infow("processUMAPTask calcUmap Waiting for calcingPod", "uuid", uuid)
				time.Sleep(time.Second * 30)

				calcingPod = xredis.Default.Get(ctx, "nebula_pod_calcing").Val()
				if len(calcingPod) == 0 {
					break
				}
			}
		}

		// 设置任务进行中标志
		xredis.Default.Set(ctx, "nebula_pod_calcing", "running", time.Hour)

		// 使用新的计算服务
		stepRsp3, err = client.PrecomputeNewUMAP(calcReq)
		if err != nil {
			return fmt.Errorf("PrecomputeNewUMAP err: %w", err)
		}

	} else {
		stepRsp3, err = client.PrecomputeUMAP(calcReq)
		if err != nil {
			return fmt.Errorf("PrecomputeUMAP err: %w", err)
		}
	}

	return updateTaskResult(ctx, req, client, task, stepRsp3, splitSize, newCalc)
}

// 处理Pod升级和任务执行的通用函数
func handlePodUpgrade(ctx context.Context, client *Client, namespace, deployment, targetSpec string,
	knowDataFile, queryDataFile string, req *aipb.ReqCreateNebulaTask, task *model.TNebulaTask,
	splitSize int, updateTime time.Time, uuid string, calcingPod string) error {

	// 根据目标规格设置资源
	var cpu, memory string
	switch targetSpec {
	case "large":
		cpu = "20000"
		memory = config.GetStringOr("nebula.large_memory", "48000")
	case "xlarge":
		cpu = "28000"
		memory = config.GetStringOr("nebula.xlarge_memory", "68000")
	case "xxlarge":
		cpu = "48000"
		memory = config.GetStringOr("nebula.xxlarge_memory", "96000")
	}

	time.Sleep(time.Second * 5)
	log.WithContext(ctx).Infow("handlePodUpgrade", "uuid", uuid, "targetSpec", targetSpec, "cpu", cpu, "memory", memory)

	// 更新Pod资源规格，添加重试机制
	const maxRetries = 4
	const retryInterval = 15 * time.Second
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		_, err := client.UpdateResources(UpdateResourcesRequest{
			Namespace:      namespace,
			Deployment:     deployment,
			LimitsCPU:      cpu,
			LimitsMemory:   memory,
			RequestsCPU:    cpu,
			RequestsMemory: memory,
		})

		if err == nil {
			break
		}

		lastErr = err
		if i < maxRetries-1 {
			log.WithContext(ctx).Errorw("UpdateResources failed, will retry",
				"attempt", i+1, "maxRetries", maxRetries, "error", err)
			time.Sleep(retryInterval)
		}
	}

	if lastErr != nil {
		return fmt.Errorf("failed to update resources after %d attempts: %w", maxRetries, lastErr)
	}

	// 等待Pod就绪
	if err := waitForPodsReady(ctx, client, namespace, deployment, uuid); err != nil {
		return err
	}

	// 更新Pod规格标记
	xredis.Default.Set(ctx, "nebula_pod_spec", targetSpec, 0)

	// 执行UMAP任务
	return processUMAPTask(ctx, true, client, knowDataFile, queryDataFile,
		req, task, splitSize, uuid)
}

// 等待Pod就绪的通用函数
func waitForPodsReady(ctx context.Context, client *Client, namespace, deployment, uuid string) error {
	for {
		pods, err := client.CheckPods(CheckPodsRequest{
			Namespace:   namespace,
			PodPrefix:   deployment,
			IntervalSec: 10,
		})
		if err != nil {
			return err
		}

		if pods.AllPodsRunning {
			return nil
		}

		log.WithContext(ctx).Infow("calcUmap Waiting for pod upgrade waitForPodsReady", "uuid", uuid)
		time.Sleep(time.Second * 30)
	}
}

// 更新任务结果的通用函数
func updateTaskResult(ctx context.Context, req *aipb.ReqCreateNebulaTask, client *Client, task *model.TNebulaTask,
	stepRsp3 *UMAPResultData, splitSize int, newCalc bool) error {

	resultPrecompute, err := json.Marshal(stepRsp3)
	if err != nil {
		return fmt.Errorf("json.Marshal err: %w", err)
	}

	clusteringReq := AddClusteringToProjectionRequest{
		InputFile:       stepRsp3.OutPutfile,
		SplitSize:       splitSize,
		EnableCosUpload: true,
	}

	// Only set clustering parameters if they have values
	if req.ClusteringMethod != "" {
		clusteringReq.ClusteringMethod = req.ClusteringMethod
	}
	if len(req.NClustersRange) > 0 {
		clusteringReq.NclustersRange = req.NClustersRange
	}
	if len(req.EpsRange) > 0 {
		clusteringReq.EpsRange = req.EpsRange
	}
	if len(req.MinSamplesRange) > 0 {
		clusteringReq.MinSamplesRange = req.MinSamplesRange
	}

	projection := &AddClusteringToProjectionResponse{}
	// 聚类计算
	if newCalc {
		defer xredis.Default.Set(ctx, "nebula_pod_calcing", "", 0)

		xredis.Default.Set(ctx, "nebula_pod_calcing", "running", time.Hour)

		projection, err = client.AddNewClusteringToProjection(clusteringReq)
		if err != nil {
			return fmt.Errorf("AddNewClusteringToProjection err: %w", err)
		}

	} else {
		projection, err = client.AddClusteringToProjection(clusteringReq)
		if err != nil {
			return fmt.Errorf("AddClusteringToProjection err: %w", err)
		}
	}

	resultCluster, err := json.Marshal(projection)
	if err != nil {
		return fmt.Errorf("json.Marshal err: %w", err)
	}

	jsonData, err := json.Marshal(projection.CosPaths)
	if err != nil {
		return fmt.Errorf("json.Marshal err: %w", err)
	}

	updateTime := time.Now()

	_, err = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(
		&model.TNebulaTask{
			ResultPrecompute: string(resultPrecompute),
			ResultCluster:    string(resultCluster),
			CentersCosURL:    projection.CentersCosURL,
			ConnectionCosURL: projection.ConnectionCosURL,
			ModelPathCluster: projection.ModelPath,
			PklPath:          stepRsp3.ModelPath,
			CalcuResult:      string(jsonData),
			EndDate:          &updateTime,
			UpdateDate:       time.Now(),
			SplitSize:        uint32(splitSize),
			State:            model.NebulaTaskStateOK,
		}, "id = ?", task.ID)
	if err != nil {
		return fmt.Errorf("UpdateBy err: %w", err)
	}

	return nil
}

// retryAddVecToCol 封装AddVecToCol的重试逻辑
func retryAddVecToCol(ctx context.Context, client *Client, uuid, inputFile string, req *aipb.ReqCreateNebulaTask) (*AddVecToColResponse, error) {
	maxRetries := config.GetIntOr("nebula.vec_max_retries", 5)
	retryInterval := 10 * time.Second
	var lastErr error
	var stepRsp *AddVecToColResponse
	var err error

	for i := 0; i < maxRetries; i++ {
		stepRsp, err = client.AddVecToCol(AddVecToColRequest{InputFile: inputFile, Lang: req.Lang, Load: req.LoadEmbedding})

		if err == nil {
			if stepRsp.FailCount == 0 {
				return stepRsp, nil // 成功，返回结果
			}
			// FailCount > 0 但 err == nil，最后一次重试则放行
			if i == maxRetries-1 {
				log.WithContext(ctx).Warnw("AddVecToCol completed with failCount after max retries",
					"uuid", uuid,
					"failCount", stepRsp.FailCount,
					"inputFile", inputFile,
					"retryCount", i+1)
				return stepRsp, nil
			}
		} else {
			lastErr = err
			// 不是最后一次重试则记录日志并等待
			if i < maxRetries-1 {
				log.WithContext(ctx).Errorw("AddVecToCol failed, will retry",
					"uuid", uuid,
					"error", err.Error(),
					"inputFile", inputFile,
					"retryCount", i+1)
				time.Sleep(retryInterval)
				continue
			}
			// 最后一次重试仍然有错误
			return nil, fmt.Errorf("AddVecToCol failed after %d retries, last error: %v", maxRetries, lastErr)
		}

		// 不是最后一次重试且有FailCount，则等待后继续
		log.WithContext(ctx).Infow("AddVecToCol completed with failCount, will retry",
			"uuid", uuid,
			"failCount", stepRsp.FailCount,
			"inputFile", inputFile,
			"retryCount", i+1)

		time.Sleep(retryInterval)
	}

	return stepRsp, fmt.Errorf("AddVecToCol failed after %d retries, last error: %v", maxRetries, lastErr)

}

// DescribeNebulaProjection 查看星云投影
func DescribeNebulaProjection(ctx context.Context, req *aipb.ReqDescribeNebulaProjection) ([]float64, error) {

	nebula, err := model.NewQuery[model.TNebulaTask](ctx).Where("uuid = ?", req.Uuid).Find()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return []float64{}, err
	}

	if nebula == nil {
		return []float64{}, nil
	}

	client := NewClient

	projection, err := client().QueryProjection(QueryProjectionRequest{
		Query: req.Query, ModelPath: nebula.PklPath, Lang: nebula.Lang})
	if err != nil {
		return []float64{}, err
	}

	if projection != nil && len(projection.Queries) > 0 {
		return projection.Queries[0].Projection, nil
	}
	return []float64{}, nil

}

// findRunningTask 查询运行中一致参数的任务，则不创建任务
func findRunningTask(ctx context.Context, req *aipb.ReqCreateNebulaTask) (bool, error) {

	assistantId, _ := uint64ArrayToJSON(req.AssistantId)
	queryAssistantId, _ := uint64ArrayToJSON(req.QueryAssistantId)

	query := model.NewQuery[model.TNebulaTask](ctx).
		Where("lang = ?", req.Lang).
		Where("assistant_ids = ?", assistantId).
		Where("query_assistant_ids = ?", queryAssistantId).
		Where("state = ?", model.TaskStateProcessing)

	if req.AsTeam {
		query = query.Where("team_id = ?  and admin_type = ? ", req.TeamId, model.AdminTypeTeam)
	} else {
		query = query.Where("user_id = ?  and admin_type = ? ", req.UserId, model.AdminTypeUser)
	}

	task, err := query.Find()
	if err != nil {
		return false, err
	}

	//  检查是否有任务在最近1分钟内创建
	tenMinutesAgo := time.Now().Add(-1 * time.Minute)
	if task.CreateDate.After(tenMinutesAgo) {
		return true, nil
	}

	return false, nil

}

// findCollectionIdsByAssistantId 根据id查找集合ids
func findCollectionIdsByAssistantId(ctx context.Context, req *aipb.ReqCreateNebulaTask) ([]uint64, error) {
	c, err := model.NewQuery[model.TAssistantCollection](ctx).Where("assistant_id in ?", req.AssistantId).Get()
	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		return []uint64{}, err
	}

	var collectionIds []uint64
	for _, v := range c {
		if v.CollectionID > 0 {
			collectionIds = append(collectionIds, v.CollectionID)
		}
	}

	return collectionIds, nil
}

// FindCalcNebulaPCdata 根据lang、assistant_id、query_assistant_id 查找计算nebula任务
func FindCalcNebulaPCdata(ctx context.Context, req *aipb.ReqDescribeNebulaTask, rsp *aipb.RspDescribeNebulaTask) error {

	assistantId, _ := uint64ArrayToJSON(req.AssistantId)
	queryAssistantId, _ := uint64ArrayToJSON(req.QueryAssistantId)

	query := model.NewQuery[model.TNebulaTask](ctx).
		Where("lang = ? and assistant_ids = ? and query_assistant_ids = ? and state = ?", req.Lang,
			assistantId, queryAssistantId, model.NebulaTaskStateOK)

	if req.AsTeam {
		query = query.Where("team_id = ?  and admin_type = ? ", req.TeamId, model.AdminTypeTeam)
	} else {
		query = query.Where("user_id = ?  and admin_type = ? ", req.UserId, model.AdminTypeUser)
	}

	nebula, err := query.OrderBy("id", true).
		Find()

	if !errors.Is(err, gorm.ErrRecordNotFound) && err != nil {
		log.WithContext(ctx).Errorw("FindCalcNebulaPCdata error", "err", err)
		return err
	}

	if nebula == nil {
		log.WithContext(ctx).Infow("FindCalcNebulaPCdata no data found", "req", req)
		return nil
	}

	_, err = model.NewQuery[model.TNebulaTask](ctx).UpdateBy(map[string]any{"is_read": model.Readed},
		"uuid = ?", nebula.UUID)

	rsp.Uuid = nebula.UUID
	rsp.State = nebula.State
	rsp.CreateDate = nebula.CreateDate.Format(time.RFC3339)
	rsp.FilterText = nebula.FilterText

	if len(nebula.CalcuResult) > 0 {
		var calcuResult []string
		err = json.Unmarshal([]byte(nebula.CalcuResult), &calcuResult)
		if err != nil {
			return err
		}

		rsp.EndDate = nebula.EndDate.Format(time.RFC3339)
		rsp.CalcuResult = calcuResult
		rsp.ClusterList = CosUrlToCdnUrl(nebula.CentersCosURL)
		rsp.ConnectInfo = CosUrlToCdnUrl(nebula.ConnectionCosURL)
	}

	return nil

}

// DescribeNebulaData 获取知识星云数据
func DescribeNebulaData(ctx context.Context, req *aipb.ReqDescribeNebulaData) ([]string, error) {
	client := NewClient()

	var contents []string
	if len(req.ContentHash) <= 20 {
		for _, v := range req.ContentHash {
			response, err := client.GetContent(GetContentRequest{
				MD5: v,
			})
			if err != nil {
				return []string{}, err
			}

			contents = append(contents, response.Content)
		}
	}

	return contents, nil
}

// findCollectionIdByName 根据名称查找集合id
func findCollectionIdByName(ctx context.Context, name string) (uint64, error) {
	c, err := model.NewQuery[model.TCollection](ctx).Where("rag_name = ?", name).Find()

	if err != nil {
		return 0, err
	}

	if c != nil {
		return c.ID, nil
	}
	return 0, errors.New("collection-name not found")
}

// findCollectionNameById 根据id查找集合名称
func findCollectionNameById(ctx context.Context, id uint64) (string, error) {
	c, err := model.NewQuery[model.TCollection](ctx).Where("id = ?", id).Find()

	if err != nil {
		return "", err
	}

	if c != nil {
		return c.RagName, nil
	}
	return "", errors.New("collection-id not found")
}

type CreateTaskReqReset struct {
	ContributorId uint64
}

// transCreateTaskReqReset 转换创建任务请求，重置参数
func transCreateTaskReqReset(ctx context.Context, req *aipb.ReqCreateNebulaTask) (CreateTaskReqReset, error) {
	// 1.预计算bge-m3转成 bgem3

	req.SimplifyLang = req.Lang
	if req.Lang == "bge-m3" {
		req.SimplifyLang = "bgem3"
	}

	reset := CreateTaskReqReset{}

	req.AdminType = model.AdminTypeUser
	reset.ContributorId = req.UserId
	if req.AsTeam {
		req.AdminType = model.AdminTypeTeam
		reset.ContributorId = req.TeamId
	}

	return reset, nil
}

// transformFilePath 将原始文件路径转换为新的查询数据文件路径 标识：-querydata
func transformFilePath(originalPath string) string {
	// 获取文件名（不带扩展名）
	filename := filepath.Base(originalPath)
	ext := filepath.Ext(filename)
	filenameWithoutExt := strings.TrimSuffix(filename, ext)

	// 获取目录路径
	dir := filepath.Dir(originalPath)

	// 构建新路径
	newFilename := filenameWithoutExt + "-querydata.json"
	newPath := filepath.Join(dir, newFilename)

	// 确保路径使用正斜杠（如果需要）
	newPath = filepath.ToSlash(newPath)

	return newPath
}

// uint64ArrayToJSON 将uint64数组转换为JSON字符串
func uint64ArrayToJSON(arr []uint64) (string, error) {
	if len(arr) == 0 {
		return "", nil
	}
	// 创建一个副本以避免修改原始数组
	sortedArr := make([]uint64, len(arr))
	copy(sortedArr, arr)

	// 对副本进行正序排序
	sort.Slice(sortedArr, func(i, j int) bool {
		return sortedArr[i] < sortedArr[j]
	})

	jsonBytes, err := json.Marshal(sortedArr) // 这里改为使用 sortedArr
	if err != nil {
		return "", fmt.Errorf("failed to marshal array to JSON: %v", err)
	}

	return string(jsonBytes), nil
}

// uint64ToIntArray 将uint64数组转换为int数组
func uint64ToIntArray(uints []uint64) []int {
	ints := make([]int, len(uints))
	for i, v := range uints {
		ints[i] = int(v)
	}
	return ints
}

// CosUrlToCdnUrl 将COS URL转换为CDN URL
func CosUrlToCdnUrl(url string) string {
	cosUrl := config.GetString("nebula.cos_url")
	cdnUrl := config.GetString("nebula.cdn_url")
	return strings.ReplaceAll(url, cosUrl, cdnUrl)
}
