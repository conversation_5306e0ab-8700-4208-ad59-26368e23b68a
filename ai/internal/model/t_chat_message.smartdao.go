// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessage [...]
type TChatMessage struct {
	ID                 uint64         `gorm:"primaryKey;column:id" json:"id"`                                 // 自增id
	ChatID             uint64         `gorm:"column:chat_id" json:"chatId"`                                   // 对话id
	QuestionID         uint64         `gorm:"column:question_id" json:"questionId"`                           // 问题id
	Content            datatypes.JSON `gorm:"column:content" json:"content"`                                  // 对话内容
	ReviewID           uint64         `gorm:"column:review_id;default:0" json:"reviewId"`                     // 审核ID
	RatingScale        int32          `gorm:"column:rating_scale;default:0" json:"ratingScale"`               // 评价等级：1满意，2一般，3不满意
	State              int32          `gorm:"column:state;default:1" json:"state"`                            // 状态 1未发送 2已发送 3 默认消息已发送 4努力思考中 5整理答案中
	CreateDate         time.Time      `gorm:"column:create_date;default:CURRENT_TIMESTAMP;not null" json:"createDate"` // 创建时间
	UpdateDate         time.Time      `gorm:"column:update_date;default:CURRENT_TIMESTAMP;not null" json:"updateDate"` // 更新时间
	CreateBy           uint64         `gorm:"column:create_by;default:0" json:"createBy"`                     // 创建人
	UpdateBy           uint64         `gorm:"column:update_by;default:0" json:"updateBy"`                     // 更新人
	Type               int32          `gorm:"column:type;default:0" json:"type"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at" json:"deletedAt"`                         // 删除时间
	RejectReason       string         `gorm:"column:reject_reason" json:"rejectReason"`                   // 驳回原因
	ThirdRecordUUID    string         `gorm:"column:third_record_uuid;default:''" json:"thirdRecordUuid"` // 第三方记录
	ThirdUserUUID      string         `gorm:"column:third_user_uuid;default:''" json:"thirdUserUuid"`     // 第三方用户记录id
	NextCursor         string         `gorm:"column:next_cursor;default:''" json:"nextCursor"`            // 下一个问题游标记录值
	AssistantID        uint64         `gorm:"column:assistant_id" json:"assistantId"`                     // 助手id
	Lang               string         `gorm:"column:lang;default:''" json:"lang"`                         // 语言
	LiveAgentName      string         `gorm:"column:live_agent_name;default:''" json:"liveAgentName"`     // 人工坐席
	CollectionSnapshot string         `gorm:"column:collection_snapshot" json:"collectionSnapshot"`       // 语言
	AskType            int32          `gorm:"column:ask_type;default:1" json:"askType"`                   // 问答枚举 QUESTION_ASK_TYPE  1 正常问答 2 重新回答 3 继续回答 4 预设问答
	EndTime            *time.Time     `gorm:"column:end_time" json:"endTime"`                             // 结束时间
	StartTime          *time.Time     `gorm:"column:start_time" json:"startTime"`                         // 开始时间
	MessageLog         []*TChatLog     `gorm:"foreignKey:message_id" json:"messageLog"`
	ShowType	       int32  		   `gorm:"column:show_type;default:1" json:"show_type"`	// 展示状态
	Docs      		   []TChatMessageDoc `gorm:"foreignKey:message_id" json:"docs"`
	MatchPattern 	   aipb.DocMatchPattern `gorm:"column:match_pattern;default:0" json:"matchPattern"` // 匹配模式
	Suggests  		   []TChatSuggest     `gorm:"foreignKey:message_id" json:"suggests"`	// 建议问题
	Think			   *TChatMessageThink  `gorm:"foreignKey:message_id" json:"think"`	// 思考过程
	PromptType		   string `gorm:"column:prompt_type" json:"promptType"`

	Chat     *TChat     `gorm:"foreignKey:id;references:chat_id"` // 所属聊天
	Feedback *TFeedback `gorm:"foreignKey:answer_id"`             // 教学反馈
	LastOperation      *TChatOperation `gorm:"foreignKey:message_id" json:"last_operation"` // 最后一次操作记录
	HashId		   string `gorm:"column:hash_id" json:"hashId"`
	Collections  *TChatMessageCollection `gorm:"foreignKey:message_id" json:"collections"`
	Task         *TChatMessageTask       `gorm:"foreignKey:message_id" json:"task"`
	Files 		 []*TChatMessageFile `gorm:"foreignKey:message_id" json:"files"` // 消息文件
	DocSnapshot  *TChatMessageDocSnapshot `gorm:"foreignKey:message_id" json:"doc_snapshot"` // doc快照
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessage) TableName() string {
	return "t_chat_message"
}

// TChatMessageColumns get sql column name.获取数据库列名
var TChatMessageColumns = struct {
	ID                 string
	ChatID             string
	QuestionID         string
	Content            string
	ReviewID           string
	RatingScale        string
	State              string
	CreateDate         string
	UpdateDate         string
	CreateBy           string
	UpdateBy           string
	Type               string
	DeletedAt          string
	RejectReason       string
	DocNames           string
	ThirdRecordUUID    string
	ThirdUserUUID      string
	NextCursor         string
	AssistantID        string
	Lang               string
	LiveAgentName      string
	CollectionSnapshot string
	ModelType          string
	AskType            string
	EndTime            string
	StartTime          string
	SuggestQuestion    string
	ShowType string
}{
	ID:                 "id",
	ChatID:             "chat_id",
	QuestionID:         "question_id",
	Content:            "content",
	ReviewID:           "review_id",
	RatingScale:        "rating_scale",
	State:              "state",
	CreateDate:         "create_date",
	UpdateDate:         "update_date",
	CreateBy:           "create_by",
	UpdateBy:           "update_by",
	Type:               "type",
	DeletedAt:          "deleted_at",
	RejectReason:       "reject_reason",
	DocNames:           "doc_names",
	ThirdRecordUUID:    "third_record_uuid",
	ThirdUserUUID:      "third_user_uuid",
	NextCursor:         "next_cursor",
	AssistantID:        "assistant_id",
	Lang:               "lang",
	LiveAgentName:      "live_agent_name",
	CollectionSnapshot: "collection_snapshot",
	ModelType:          "model_type",
	AskType:            "ask_type",
	EndTime:            "end_time",
	StartTime:          "start_time",
	SuggestQuestion:    "suggest_question",
	ShowType: "show_type",
}
