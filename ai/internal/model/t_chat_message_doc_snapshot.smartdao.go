// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TChatMessageDocSnapshot [...]
type TChatMessageDocSnapshot struct {
	MessageID  uint64    `gorm:"column:message_id" json:"messageId"` // 消息id
	Content    string    `gorm:"column:content" json:"content"`
	CreateDate time.Time `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate"`
}

// TableName get sql table name.获取数据库表名
func (m *TChatMessageDocSnapshot) TableName() string {
	return "t_chat_message_doc_snapshot"
}

// TChatMessageDocSnapshotColumns get sql column name.获取数据库列名
var TChatMessageDocSnapshotColumns = struct {
	MessageID  string
	Content    string
	CreateDate string
}{
	MessageID:  "message_id",
	Content:    "content",
	CreateDate: "create_date",
}
