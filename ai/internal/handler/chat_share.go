package handler

import (
	"context"
	"encoding/json"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/go-micro/v3/xerrors"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	chatlogic "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/chat"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	aipb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	errorspb "e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/errors"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

// CreateChatShare 创建聊天分享
func (a *Ai) CreateChatShare(ctx context.Context, req *aipb.ReqCreateChatShare, rsp *aipb.RspCreateChatShare) error {
	if req.ChatId == 0 || len(req.MessageIds) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	// 生成唯一分享ID
	shareID := util.GenerateUUID()

	// 将消息ID转换为JSON格式
	messageIDsJSON, err := json.Marshal(req.MessageIds)
	if err != nil {
		return xerrors.InternalServerError(err)
	}

	// 创建分享记录
	share := &model.TChatShare{
		ShareID:            shareID,
		ChatID:             req.ChatId,
		OriginalMessageIDs: messageIDsJSON,
		SharedBy:           req.UserId,
		ShareType:          int32(req.ShareType),
		ShareStatus:        int32(aipb.ShareStatus_SHARE_STATUS_ACTIVE),
		AssistantID:        req.AssistantId,
	}

	// 设置过期时间（如果有）
	if req.ExpireDays > 0 {
		expireDate := time.Now().AddDate(0, 0, int(req.ExpireDays))
		share.ExpireDate = &expireDate
	}

	// 保存到数据库
	if err := model.NewQuery[model.TChatShare](ctx).Create(share); err != nil {
		return xerrors.InternalServerError(err)
	}

	// 返回分享信息
	rsp.ShareId = shareID
	return nil
}

// ContinueChatFromShare 从分享继续聊天
func (a *Ai) ContinueChatFromShare(ctx context.Context, req *aipb.ReqContinueChatFromShare, rsp *aipb.RspContinueChatFromShare) error {
	if len(req.Messages) == 0 {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	chat := &model.TChat{
		CreateBy:    req.UserId,
		Title:       req.Messages[0].Text,
		UpdateBy:    req.UserId,
		Type:        int32(req.ChatType),
		AssistantID: req.AssistantId,
		RegionCode:  req.RegionCode,
		IsShared:    true,
	}

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		// 创建会话
		if err := tx.Model(&model.TChat{}).Create(&chat).Error; err != nil {
			return xerrors.InternalServerError(err)
		}

		// 复制消息
		questionIdMap := make(map[uint64]uint64)
		for _, msg := range req.Messages {
			m := &chatlogic.ChatMessage{ChatMessage: msg}
			newMsg, err := m.TransformToTMessage()
			if err != nil {
				return xerrors.InternalServerError(err)
			}
			newMsg.ID = 0
			newMsg.ChatID = chat.ID
			newMsg.CreateBy = req.UserId
			newMsg.UpdateBy = req.UserId
			newMsg.CreateDate = time.Now()
			newMsg.UpdateDate = time.Now()
			newMsg.RatingScale = 0

			if newMsg.QuestionID > 0 {
				if newQuestionId, exists := questionIdMap[newMsg.QuestionID]; exists {
					newMsg.QuestionID = newQuestionId // 使用新问题的ID
				} else {
					return xerrors.NotFoundError("question not found for answer")
				}
			}

			if err := tx.Model(&model.TChatMessage{}).Create(newMsg).Error; err != nil {
				log.WithContext(ctx).Errorw("Failed to copy message to new chat", "err", err)
				return err
			}
			if m.QuestionId == 0 { // 如果是问题则更新questionIdMap
				questionIdMap[msg.Id] = newMsg.ID
			}
		}

		return nil
	})

	if err != nil {
		return err
	}

	rsp.ChatId = chat.ID
	rsp.Title = chat.Title

	return nil
}

// ListChatShares 获取用户的分享列表
func (a *Ai) ListChatShares(ctx context.Context, req *aipb.ReqListChatShares, rsp *aipb.RspListChatShares) error {
	query := model.NewQuery[model.TChatShare](ctx).DB()

	// 应用过滤条件
	if req.UserId > 0 {
		query = query.Where("shared_by = ?", req.UserId)
	}
	if req.ChatId > 0 {
		query = query.Where("chat_id = ?", req.ChatId)
	}
	if req.ShareStatus != aipb.ShareStatus_SHARE_STATUS_UNSPECIFIED {
		query = query.Where("share_status = ?", req.ShareStatus)
	}

	// 计算总数
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return xerrors.InternalServerError(err)
	}
	rsp.TotalCount = uint32(totalCount)

	// 应用分页
	paginator := xorm.NewPaginator(req.Offset, req.Limit)
	query = paginator.Apply(query)

	// 应用排序
	query = query.Order("share_date DESC")

	// 查询数据
	var shares []*model.TChatShare
	if err := query.Find(&shares).Error; err != nil {
		return xerrors.InternalServerError(err)
	}

	// 转换为响应格式
	rsp.Shares = make([]*aipb.ChatShare, len(shares))
	for i, share := range shares {
		rsp.Shares[i] = &aipb.ChatShare{
			Id:          share.ID,
			ShareId:     share.ShareID,
			ChatId:      share.ChatID,
			ShareType:   aipb.ShareType(share.ShareType),
			ShareStatus: aipb.ShareStatus(share.ShareStatus),
			SharedBy:    share.SharedBy,
			AccessCount: share.AccessCount,
			ShareDate:   timestamppb.New(share.ShareDate),
			AssistantId: share.AssistantID,
		}
		if share.ExpireDate != nil {
			rsp.Shares[i].ExpireDate = timestamppb.New(*share.ExpireDate)
		}
		if share.LastAccessTime != nil {
			rsp.Shares[i].LastAccessTime = timestamppb.New(*share.LastAccessTime)
		}
	}

	return nil
}

// UpdateChatShareStatus 更新分享状态
func (a *Ai) UpdateChatShareStatus(ctx context.Context, req *aipb.ReqUpdateChatShareStatus, _ *emptypb.Empty) error {
	if req.ShareId == "" {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	// 查询分享记录
	share, err := model.NewQuery[model.TChatShare](ctx).FindBy("share_id = ?", req.ShareId)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if share == nil {
		return xerrors.NotFoundError("share not found")
	}

	// 检查权限
	if req.UserId > 0 && share.SharedBy != req.UserId {
		return xerrors.ForbiddenError("no permission to update share status")
	}

	// 更新状态
	if err := model.NewQuery[model.TChatShare](ctx).DB().Model(&model.TChatShare{}).
		Where("id = ?", share.ID).
		Update("share_status", int32(req.Status)).Error; err != nil {
		return xerrors.InternalServerError(err)
	}

	return nil
}

// ListChatShareAccesses 获取分享访问记录
func (a *Ai) ListChatShareAccesses(ctx context.Context, req *aipb.ReqListChatShareAccesses, rsp *aipb.RspListChatShareAccesses) error {
	if req.ShareId == "" {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	// 查询分享记录
	share, err := model.NewQuery[model.TChatShare](ctx).FindBy("share_id = ?", req.ShareId)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if share == nil {
		return xerrors.NotFoundError("share not found")
	}

	// 检查权限
	if req.UserId > 0 && share.SharedBy != req.UserId {
		return xerrors.ForbiddenError("no permission to view share accesses")
	}

	query := model.NewQuery[model.TChatShareAccess](ctx).DB().
		Where("share_id = ?", req.ShareId)

	// 计算总数
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		return xerrors.InternalServerError(err)
	}
	rsp.TotalCount = uint32(totalCount)

	// 应用分页
	paginator := xorm.NewPaginator(req.Offset, req.Limit)
	query = paginator.Apply(query)

	// 应用排序
	query = query.Order("access_date DESC")

	// 查询数据
	var accesses []*model.TChatShareAccess
	if err := query.Find(&accesses).Error; err != nil {
		return xerrors.InternalServerError(err)
	}

	// 转换为响应格式
	rsp.Accesses = make([]*aipb.ChatShareAccess, len(accesses))
	for i, access := range accesses {
		rsp.Accesses[i] = &aipb.ChatShareAccess{
			Id:          access.ID,
			ShareId:     access.ShareID,
			AccessBy:    access.AccessBy,
			AccessDate:  timestamppb.New(access.AccessDate),
			IsContinued: access.IsContinued == 1,
			NewChatId:   access.NewChatID,
		}
	}

	return nil
}

// GetChatShareRecord 查询分享记录
func (a *Ai) GetChatShareRecord(ctx context.Context, req *aipb.ReqGetChatShareRecord, rsp *aipb.RspGetChatShareRecord) error {
	if req.ShareId == "" {
		return xerrors.NewCode(errorspb.BaseError_UnprocessableEntity)
	}

	share, err := model.NewQuery[model.TChatShare](ctx).FindBy("share_id = ?", req.ShareId)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if share == nil {
		return xerrors.NotFoundError("share not found")
	}

	// 解析原始消息ID
	var messageIDs []uint64
	if err := json.Unmarshal(share.OriginalMessageIDs, &messageIDs); err != nil {
		return xerrors.InternalServerError(err)
	}

	rsp.ChatId = share.ChatID
	rsp.AssistantId = share.AssistantID
	rsp.ShareType = aipb.ShareType(share.ShareType)
	rsp.MessageIds = messageIDs
	rsp.ChatId = share.ChatID
	rsp.AssistantId = share.AssistantID
	rsp.ShareType = aipb.ShareType(share.ShareType)
	rsp.ShareStatus = aipb.ShareStatus(share.ShareStatus)
	rsp.SharedBy = share.SharedBy
	rsp.ShareDate = timestamppb.New(share.ShareDate)
	if share.ExpireDate != nil {
		rsp.ExpireDate = timestamppb.New(*share.ExpireDate)
	}
	rsp.AccessCount = share.AccessCount
	if share.LastAccessTime != nil {
		rsp.LastAccessTime = timestamppb.New(*share.LastAccessTime)
	}

	return nil
}

// GetChatShareMessages 获取分享消息
func (a *Ai) GetChatShareMessages(ctx context.Context, req *aipb.ReqGetChatShareMessages, rsp *aipb.RspGetChatShareMessages) error {
	// 查询消息详情（包含所有关联数据）
	var messages []*model.TChatMessage
	if err := model.NewQuery[model.TChatMessage](ctx).DB().
		Preload("Docs").
		Preload("DocSnapshot").
		Preload("MessageLog").
		Preload("Collections").
		Preload("Think").
		Preload("LastOperation").
		Preload("Suggests").
		Preload("Files").
		Where("chat_id = ? and id IN ?", req.ChatId, req.MessageIds).
		Order("id ASC").
		Find(&messages).Error; err != nil {
		return xerrors.InternalServerError(err)
	}

	// 检查是否有没q的answer
	questionMap := make(map[uint64]struct{})
	for _, message := range messages {
		if message.QuestionID == 0 {
			questionMap[message.ID] = struct{}{}
		}
	}
	for _, message := range messages {
		if message.QuestionID > 0 {
			if _, exists := questionMap[message.QuestionID]; !exists {
				return xerrors.NotFoundError("exist answer without question")
			}
		}
	}

	// 转换为PB格式
	var pbMessages []*aipb.ChatMessage
	for _, msg := range messages {
		pbMsg, err := msg.TransformToAiMessage()
		if err != nil {
			continue
		}
		pbMessages = append(pbMessages, pbMsg)
	}

	rsp.Messages = pbMessages

	return nil
}

// RecordChatShareAccess 记录分享访问（仅国内）
func (a *Ai) RecordChatShareAccess(ctx context.Context, req *aipb.ReqRecordChatShareAccess, rsp *aipb.RspRecordChatShareAccess) error {
	// 查询分享记录是否存在
	share, err := model.NewQuery[model.TChatShare](ctx).FindBy("share_id = ?", req.ShareId)
	if err != nil {
		return xerrors.InternalServerError(err)
	}
	if share == nil {
		return xerrors.NotFoundError("share not found")
	}

	// 创建访问记录
	access := &model.TChatShareAccess{
		ShareID:     req.ShareId,
		AccessBy:    req.UserId,
		AccessDate:  time.Now(),
		IsContinued: 0,
	}

	// 如果是继续聊天，设置相关字段
	if req.IsContinued && req.NewChatId > 0 {
		access.IsContinued = 1
		access.NewChatID = req.NewChatId
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		// 保存访问记录
		if err := tx.Model(&model.TChatShareAccess{}).Create(access).Error; err != nil {
			log.WithContext(ctx).Errorw("Failed to record share access", "err", err)
			return err
		}

		// 更新分享记录的访问计数和最后访问时间
		if !req.IsContinued {
			now := time.Now()
			if err := tx.Model(&model.TChatShare{}).
				Where("id = ?", share.ID).
				Updates(map[string]interface{}{
					"access_count":     gorm.Expr("access_count + 1"),
					"last_access_time": now,
				}).Error; err != nil {
				log.WithContext(ctx).Errorw("Failed to update share access count", "err", err)
				return err
			}
		}

		return nil
	})

	if err != nil {
		return xerrors.InternalServerError(err)
	}

	rsp.AccessId = access.ID
	return nil
}
